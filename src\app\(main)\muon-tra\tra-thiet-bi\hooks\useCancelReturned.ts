import { CANCEL_RETURN_DEVICE } from "@/constant/api.const";
import http from "@/api";
import { toast } from "sonner";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import { ApiConstant, EnvConstant } from "@/constant";
import { DataListResponseModel } from "@/models/response.model";
import { IReturnDeviceHistory } from "../returnDevice.model";

const useCancelReturned = () => {
  const cancelReturnedDevices = async (
    selectedRows: IReturnDeviceHistory[],
    onSuccess?: () => void
  ) => {
    try {
      toggleAppProgress(true);

      const id = selectedRows.map((row) => row.id).filter(Boolean);

      if (id.length === 0) {
        toast.warning("Thất bại!", {
          description: "Không có thiết bị nào đượ<PERSON> chọn",
        });
        return;
      }

      const response: DataListResponseModel<any> = await http.put(
        CANCEL_RETURN_DEVICE,
        id
      );

      if (response.code === ApiConstant.ERROR_CODE_OK) {
        onSuccess?.();
        toast.success("Thành công!", {
          description: "Hủy ghi trả thiết bị thành công",
        });
      } else {
        throw response;
      }
    } catch (error: any) {
      EnvConstant.IS_DEV && console.log(error);
      toast.error("Thất bại!", {
        description:
          extractErrorMessage(error) ||
          "Có lỗi xảy ra khi hủy ghi trả thiết bị",
      });
    } finally {
      toggleAppProgress(false);
    }
  };

  return {
    cancelReturnedDevices,
  };
};

export default useCancelReturned;
