import { AppTextField, TablePageLayout } from "@/components/common";
import { FilterConfig } from "@/components/common/TablePageLayout/type";
import { DEVICE_LIST } from "@/constant/api.const";
import { BorrowStatusEnum, IEduDevice } from "@/models/eduDevice.model";
import { formatNumber } from "@/utils/format.utils";
import { Button, Popover } from "@mui/material";
import { ArrowDropDownIcon } from "@mui/x-date-pickers";
import { ColumnDef } from "@tanstack/react-table";
import React, { memo, useCallback, useMemo, useState } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import { IBorrowRequestAction } from "../../../borrowRequestModel";
import { toOption } from "@/components/common/AppAutoComplete";
import { v4 as uuid } from "uuid";

const ChooseDeviceColCell = ({ row, update, disabled, append }) => {
  const index = row.original._index;
  const [anchor, setAnchor] = useState<HTMLElement | null>(null);

  const { control } = useFormContext<IBorrowRequestAction>();
  const [deviceName, deviceCode] = useWatch({
    control,
    name: [
      `borrowRequestDevices.${index}.deviceName`,
      `borrowRequestDevices.${index}.deviceCode`,
    ],
  });

  const handleToggleDropdown = useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      if (disabled) return;
      setAnchor(event.currentTarget);
    },
    [disabled]
  );

  const handleClose = useCallback(() => setAnchor(null), []);

  const handleSelectDevice = useCallback(
    (data: IEduDevice) => {
      setAnchor(null);

      if (index === -1) {
        const newDevice = {
          ...row.original,
          id: uuid(),
          fromDate: row.original.fromDate,
          toDate: row.original.toDate,
          status: BorrowStatusEnum.Register,
          deviceId: data.id,
          deviceName: data.deviceName,
          deviceCode: data.code,
          deviceUnitName: data.deviceUnitName,
          deviceUnitId: data.deviceUnitId,
          gradeName: data.gradeName,
          roomName: data.roomName,
          roomId: toOption(data.roomId, data.roomName),
          subjectId: toOption(data.schoolSubjectId, data.schoolSubjectName),
          totalBorrowReady: data.totalBorrowReady,
        };

        append(newDevice);
      } else {
        update(index, {
          ...row.original,
          status: BorrowStatusEnum.Register,
          deviceId: data.id,
          deviceName: data.deviceName,
          deviceCode: data.code || "",
          deviceUnitName: data.deviceUnitName,
          deviceUnitId: data.deviceUnitId,
          gradeName: data.gradeName,
          roomName: data.roomName,
          roomId: toOption(data.roomId, data.roomName),
          subjectId: toOption(data.schoolSubjectId, data.schoolSubjectName),
          totalBorrowReady: data.totalBorrowReady,
        });
      }
    },
    [index]
  );

  return (
    <>
      <AppTextField
        value={`${deviceName ?? ""} ${
          deviceCode ? "(" + deviceCode + ")" : ""
        }`}
        onClick={handleToggleDropdown}
        slotProps={{
          input: {
            readOnly: true,
            endAdornment: (
              <ArrowDropDownIcon
                sx={{
                  color: disabled ? "action.disabled" : "text.primary",
                  transform: anchor ? "rotate(180deg)" : "rotate(0deg)",
                }}
              />
            ),
          },
        }}
        disabled={disabled}
      />
      <TableSelector
        anchorEl={anchor}
        onClose={handleClose}
        onSelect={handleSelectDevice}
      />
    </>
  );
};

export default memo(ChooseDeviceColCell);

const TableSelector = ({ anchorEl, onClose, onSelect }) => {
  const tableProps = useMemo(() => {
    const columns = getColumns(onSelect);
    return {
      columns,
      tableContainerProps: {
        sx: {
          height: "400px",
        },
      },
    };
  }, [onSelect]);

  return (
    <Popover
      open={Boolean(anchorEl)}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "left",
      }}
      slotProps={{
        paper: {
          sx: {
            width: 800,
          },
        },
      }}
    >
      <TablePageLayout<IEduDevice>
        apiUrl={DEVICE_LIST}
        tableProps={tableProps}
        filterConfig={FILTER_CONFIG}
      />
    </Popover>
  );
};

const FILTER_CONFIG: FilterConfig[] = [
  {
    key: "searchKey",
    type: "text",
    label: "Tìm kiếm",
    size: 4,
  },
  {
    key: "isAvailable",
    value: "true",
  },
];

const getColumns = (
  onSelectDevice?: (device: IEduDevice) => void
): ColumnDef<IEduDevice>[] => [
  {
    id: "select",
    header: "",
    size: 60,
    meta: { align: "center" },
    cell: ({ row }) => (
      <Button
        disabled={!row.original.totalBorrowReady}
        variant="contained"
        size="small"
        onClick={() => onSelectDevice?.(row.original)}
      >
        Chọn
      </Button>
    ),
  },
  {
    id: "code",
    header: "Mã thiết bị",
    accessorKey: "code",
    size: 40,
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
    size: 150,
  },
  {
    id: "roomName",
    accessorKey: "roomName",
    header: "Kho/phòng",
    size: 60,
  },
  {
    id: "schoolSubjectName",
    accessorKey: "schoolSubjectName",
    header: "Môn học",
    size: 60,
  },
  {
    id: "totalBorrowReady ",
    accessorKey: "totalBorrowReady ",
    header: "Số lượng",
    size: 40,
    meta: { align: "right" },
    cell: ({ row }) => formatNumber(row.original.totalBorrowReady),
  },
];
