import { AppModal } from "@/components/common";
import { AppModalProps } from "@/components/common/modal/AppModal";
import { Button } from "@mui/material";
import React, { memo, useCallback } from "react";
import {
  FormProvider,
  useForm,
  useFormContext,
  useWatch,
} from "react-hook-form";
import DeviceForm from "./DeviceForm";
import {
  IDeviceTransactionAction,
  IDeviceTransactionUI,
} from "../../equipmentDocument.model";
import { MANAGE_BY_LIST } from "@/models/system.model";
import { useAppDispatch } from "@/redux/hook";
import { equipmentDocumentActions } from "../../equipmentDocument.slice";
import { v4 as uuidv4 } from "uuid";
import { getEntriesEquipmentDocumentAndDevice } from "../../helper";
import useValidForm from "./hooks/useValidForm";
import { toast } from "sonner";

const AddDeviceModal = ({ onClose, ...otherProps }: AddDeviceModalProps) => {
  const { control: controlParent } = useFormContext<IDeviceTransactionAction>();
  const documentDate = useWatch({
    control: controlParent,
    name: "documentDate",
  });

  const dispatch = useAppDispatch();
  const handleValid = useValidForm();
  const methods = useForm({
    defaultValues: INIT_VALUE,
  });
  const { handleSubmit, reset } = methods;

  const handleClose = () => {
    reset(INIT_VALUE);
    onClose();
    reset(INIT_VALUE);
  };

  const handleSubmitData = useCallback((data) => {
    const isError = data.devices.some((item) => !item.roomId);
    if (isError) {
      toast.warning("Cảnh báo", {
        description: "Vui lòng chọn Kho phòng cho tất cả thiết bị!",
      });
      return;
    }
    const { equipmentDocumentEntry, device } =
      getEntriesEquipmentDocumentAndDevice({
        id: uuidv4(),
        ...data,
      });
    if (device.length === 0) {
      toast.warning("Cảnh báo", {
        description: "Danh sách thiết bị không được bỏ trống!",
      });
      return;
    }
    handleValid(equipmentDocumentEntry, () => {
      dispatch(
        equipmentDocumentActions.addEquipmentDocumentEntry({
          equipmentDocumentEntry,
          device,
        })
      );
      handleClose();
    });
  }, []);

  return (
    <FormProvider {...methods}>
      <AppModal
        fullScreen
        component="form"
        onSubmit={(e) => {
          e.stopPropagation();
          handleSubmit(handleSubmitData)(e);
        }}
        modalTitleProps={{
          title: "Thêm mới thiết bị",
        }}
        slotProps={{
          paper: {
            sx: {
              height: "100%",
            },
          },
        }}
        maxWidth="lg"
        fullWidth
        onClose={handleClose}
        modalContentProps={{
          sx: {
            bgcolor: "background.grey",
            px: "12px",
            py: "12px",
          },
          content: <DeviceForm documentDate={documentDate} />,
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                onClick={handleClose}
                color="secondary"
                variant="outlined"
              >
                Đóng
              </Button>
              <Button variant="contained" type="submit">
                Ghi
              </Button>
            </>
          ),
        }}
        {...otherProps}
      />
    </FormProvider>
  );
};

type AddDeviceModalProps = AppModalProps & {};

export default memo(AddDeviceModal);

export const INIT_VALUE: IDeviceTransactionUI = {
  statisticCode: "",
  deviceCode: "",
  deviceName: "",
  deviceUnitId: null,
  schoolDeviceTypeId: null,
  schoolSubjectId: null,
  gradeCodes: [],
  userTypes: [],
  manageBy: MANAGE_BY_LIST[0].id,
  isConsumable: 0,
  isSelfMade: 0,
  minimumQuantity: 1,

  totalAvailable: undefined,

  devices: [],

  totalDevices: 1,

  maxIndexItem: 1,
  totalAdd: 1,
  // isShowInReport: false, // nếu bật lại toggle cuối cùng
};
