import { memo, useMemo, useEffect } from "react";
import { Typography } from "@mui/material";
import AppDatePicker from "@/components/common/AppDatePicker";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import {
  selectHeaderDateFromRedux,
  setHeaderDate,
  updateEditedValue,
  selectEditedRows,
} from "../../../returnDevice.slice";
import { IReturnDeviceList } from "../../../returnDevice.model";
import dayjs from "dayjs";
import { DATE_TIME_YYYYescape } from "@/constant/app.const";
import { useTableStore } from "@/components/common/TablePageLayout/table-store/TableContext";

interface DateHeaderWithReduxProps {}

const DateHeaderWithRedux = memo(({}: DateHeaderWithReduxProps) => {
  const dispatch = useAppDispatch();
  const headerDate = useAppSelector(selectHeaderDateFromRedux);
  const editedData = useAppSelector(selectEditedRows);

  const tableStore = useTableStore<IReturnDeviceList>();
  const allTableData = tableStore((state) => state.data);

  const allRowIds = useMemo(() => {
    return allTableData.map((row) => row.id);
  }, [allTableData]);

  useEffect(() => {
    if (!allTableData || allTableData.length === 0) {
      dispatch(setHeaderDate(dayjs().format(DATE_TIME_YYYYescape)));
      return;
    }
  }, [allTableData, dispatch]);

  useEffect(() => {
    if (!allTableData || allTableData.length === 0) {
      return;
    }

    const allCurrentDates = allTableData.map((row) => {
      const editedDate = editedData[row.id]?.borrowReturnDate;
      return editedDate !== undefined ? editedDate : row.borrowReturnDate;
    });

    const firstDate = allCurrentDates[0];
    const allSameDate = allCurrentDates.every((date) => date === firstDate);

    if (!allSameDate && headerDate !== null) {
      dispatch(setHeaderDate(null));
    }
  }, [allTableData, editedData, headerDate, dispatch]);

  const handleHeaderDateChange = (newValue: any) => {
    const formattedDate = newValue
      ? dayjs(newValue).format(DATE_TIME_YYYYescape)
      : null;

    dispatch(setHeaderDate(formattedDate));

    if (allRowIds.length > 0 && formattedDate) {
      allRowIds.forEach((rowId) => {
        dispatch(
          updateEditedValue({
            rowId,
            field: "borrowReturnDate",
            value: formattedDate,
          })
        );
      });
    }
  };

  const displayValue = useMemo(() => {
    if (headerDate === null) return null;
    return headerDate ? dayjs(headerDate) : dayjs();
  }, [headerDate]);

  return (
    <>
      <Typography variant="body1" color="primary.contrastText">
        Ngày trả
      </Typography>
      <AppDatePicker
        value={displayValue}
        onChange={handleHeaderDateChange}
        slotProps={{
          textField: {
            size: "small",
            fullWidth: true,
            placeholder: "Ngày trả cho tất cả",
          },
        }}
        maxDate={dayjs()}
      />
    </>
  );
});

DateHeaderWithRedux.displayName = "DateHeaderWithRedux";

export default DateHeaderWithRedux;
