import { memo, useMemo, useEffect } from "react";
import { Typography } from "@mui/material";
import AppDatePicker from "@/components/common/AppDatePicker";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import {
  selectHeaderDateFromRedux,
  setHeaderDate,
  updateEditedValue,
  selectEditedRows,
} from "../../../returnDevice.slice";
import { IReturnDeviceList } from "../../../returnDevice.model";
import dayjs from "dayjs";
import { DATE_TIME_YYYYescape } from "@/constant/app.const";
import { Table } from "@tanstack/react-table";
import { useTableStore } from "@/components/common/TablePageLayout/table-store/TableContext";

interface DateHeaderWithReduxProps {
  table: Table<IReturnDeviceList>;
}

const DateHeaderWithRedux = memo(({ table }: DateHeaderWithReduxProps) => {
  const dispatch = useAppDispatch();
  const headerDate = useAppSelector(selectHeaderDateFromRedux);
  const editedData = useAppSelector(selectEditedRows);

  // Lấy tất cả data từ table
  const allTableData = useMemo(() => {
    // Thử nhiều cách để lấy data
    const coreRows = table.getCoreRowModel().rows.map((row) => row.original);
    const filteredRows = table.getRowModel().rows.map((row) => row.original);
    const optionsData = table.options.data || [];

    console.log("Core rows:", coreRows);
    console.log("Filtered rows:", filteredRows);
    console.log("Options data:", optionsData);

    // Ưu tiên sử dụng data từ options nếu rows models trống
    if (coreRows.length > 0) return coreRows;
    if (filteredRows.length > 0) return filteredRows;
    return optionsData;
  }, [table]);

  const allRowIds = useMemo(() => {
    const ids = allTableData.map((row) => row.id);
    console.log("All row IDs computed:", ids);
    return ids;
  }, [allTableData]);

  // Effect để khởi tạo header date
  useEffect(() => {
    if (!allTableData || allTableData.length === 0) {
      dispatch(setHeaderDate(dayjs().format(DATE_TIME_YYYYescape)));
      return;
    }
  }, [allTableData, dispatch]);

  // Effect để kiểm tra khi các rows có ngày khác nhau thì set header date = null
  useEffect(() => {
    if (!allTableData || allTableData.length === 0) {
      return;
    }

    // Lấy tất cả ngày hiện tại của các rows (từ editedData hoặc originalData)
    const allCurrentDates = allTableData.map((row) => {
      const editedDate = editedData[row.id]?.borrowReturnDate;
      return editedDate !== undefined ? editedDate : row.borrowReturnDate;
    });

    // Kiểm tra xem tất cả ngày có giống nhau không
    const firstDate = allCurrentDates[0];
    const allSameDate = allCurrentDates.every((date) => date === firstDate);

    // Nếu không phải tất cả cùng ngày và header date không phải null
    if (!allSameDate && headerDate !== null) {
      dispatch(setHeaderDate(null));
    }
  }, [allTableData, editedData, headerDate, dispatch]);

  const handleHeaderDateChange = (newValue: any) => {
    const formattedDate = newValue
      ? dayjs(newValue).format(DATE_TIME_YYYYescape)
      : null;

    // Cập nhật header date
    dispatch(setHeaderDate(formattedDate));

    // Debug: Log để kiểm tra
    console.log("Header date changed:", formattedDate);
    console.log("All row IDs:", allRowIds);
    console.log("All table data:", allTableData);

    // Cập nhật tất cả các row trong bảng
    if (allRowIds.length > 0) {
      allRowIds.forEach((rowId) => {
        console.log("Updating row:", rowId, "with date:", formattedDate || "");
        dispatch(
          updateEditedValue({
            rowId,
            field: "borrowReturnDate",
            value: formattedDate || "",
          })
        );
      });
    } else {
      console.log("No rows to update");
    }
  };

  const displayValue = useMemo(() => {
    if (headerDate === null) return null;
    return headerDate ? dayjs(headerDate) : dayjs();
  }, [headerDate]);

  return (
    <>
      <Typography variant="body1" color="primary.contrastText">
        Ngày trả
      </Typography>
      <AppDatePicker
        value={displayValue}
        onChange={handleHeaderDateChange}
        slotProps={{
          textField: {
            size: "small",
            fullWidth: true,
            placeholder:
              headerDate === null ? "Ngày khác nhau" : "Ngày trả cho tất cả",
          },
        }}
        maxDate={dayjs()}
      />
    </>
  );
});

DateHeaderWithRedux.displayName = "DateHeaderWithRedux";

export default DateHeaderWithRedux;
