import { memo, useMemo, useEffect } from "react";
import { Typography } from "@mui/material";
import AppDatePicker from "@/components/common/AppDatePicker";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import {
  selectHeaderDateFromRedux,
  setHeaderDate,
  updateHeaderDateForSelectedRows,
  makeSelectIsAllRowsSameDate,
  selectEditedRows,
  updateEditedValue,
} from "../../../returnDevice.slice";
import { IReturnDeviceList } from "../../../returnDevice.model";
import dayjs from "dayjs";
import { DATE_TIME_YYYYescape } from "@/constant/app.const";

interface DateHeaderWithReduxProps {
  selectedRows: IReturnDeviceList[];
}

const DateHeaderWithRedux = memo(
  ({ selectedRows }: DateHeaderWithReduxProps) => {
    const dispatch = useAppDispatch();
    const headerDate = useAppSelector(selectHeaderDateFromRedux);
    const editedData = useAppSelector(selectEditedRows);

    // Tạo selector để kiểm tra xem tất cả các row có cùng ngày không
    const selectIsAllRowsSameDate = useMemo(() => {
      return makeSelectIsAllRowsSameDate();
    }, []);

    // Tạo map của original dates và selected row IDs
    const { selectedRowIds, originalDates } = useMemo(() => {
      const ids = selectedRows.map((row) => row.id);
      const dates = selectedRows.reduce((acc, row) => {
        acc[row.id] = row.borrowReturnDate;
        return acc;
      }, {} as Record<number, string>);
      return { selectedRowIds: ids, originalDates: dates };
    }, [selectedRows]);

    const isAllRowsSameDate = useAppSelector((state) =>
      selectIsAllRowsSameDate(state, selectedRowIds, originalDates)
    );

    // Effect để cập nhật header date dựa trên trạng thái của các row
    useEffect(() => {
      if (!selectedRows || selectedRows.length === 0) {
        dispatch(setHeaderDate(dayjs().format(DATE_TIME_YYYYescape)));
        return;
      }

      if (!isAllRowsSameDate) {
        dispatch(setHeaderDate(null));
        return;
      }
    }, [selectedRows, isAllRowsSameDate, dispatch, headerDate]);

    // Effect để đồng bộ ngày cho các row mới được select với header date hiện tại
    useEffect(() => {
      if (!headerDate || !selectedRows || selectedRows.length === 0) {
        return;
      }

      // Tìm các row chưa có ngày trong editedData hoặc có ngày khác với header
      selectedRows.forEach((row) => {
        const currentEditedDate = editedData[row.id]?.borrowReturnDate;
        const originalDate = row.borrowReturnDate;

        // Nếu row chưa có ngày trong editedData hoặc ngày khác với header date
        if (!currentEditedDate || currentEditedDate !== headerDate) {
          // Chỉ cập nhật nếu ngày gốc cũng khác với header date (tức là row mới select)
          if (originalDate !== headerDate) {
            dispatch(
              updateEditedValue({
                rowId: row.id,
                field: "borrowReturnDate",
                value: headerDate,
              })
            );
          }
        }
      });
    }, [selectedRows, headerDate, editedData, dispatch]);

    const handleHeaderDateChange = (newValue: any) => {
      const formattedDate = newValue
        ? dayjs(newValue).format(DATE_TIME_YYYYescape)
        : null;

      // Cập nhật header date
      dispatch(setHeaderDate(formattedDate));

      // Cập nhật tất cả các row đã chọn
      if (selectedRowIds.length > 0) {
        dispatch(
          updateHeaderDateForSelectedRows({
            selectedRowIds,
            headerDate: formattedDate,
          })
        );
      }
    };

    const displayValue = useMemo(() => {
      if (headerDate === null) return null;
      return headerDate ? dayjs(headerDate) : dayjs();
    }, [headerDate]);

    return (
      <>
        <Typography variant="body1" color="primary.contrastText">
          Ngày trả
        </Typography>
        <AppDatePicker
          value={displayValue}
          onChange={handleHeaderDateChange}
          slotProps={{
            textField: {
              size: "small",
              fullWidth: true,
              placeholder: "Ngày trả cho tất cả",
            },
          }}
          maxDate={dayjs()}
        />
      </>
    );
  }
);

DateHeaderWithRedux.displayName = "DateHeaderWithRedux";

export default DateHeaderWithRedux;
