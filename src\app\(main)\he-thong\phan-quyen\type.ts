import { DataConstant } from "@/constant";

export interface IApplicationFeature {
  id: number;
  code: string;
  name: string;
  status: DataConstant.STATUS_TYPE;
}

export interface IFunction {
  id: number;
  code: string;
  name: string;
  applicationId: number;
  parentId: number;
  parentName: string;
  order: number;
  status: DataConstant.STATUS_TYPE;
  isSystem: DataConstant.STATUS_TYPE;
  createdBy: string | null;
  createdAt: string | null;
  updatedBy: string | null;
  updatedAt: string | null;
  applicationFeatures: IApplicationFeature[];
  listGroupsUnitCode: string[];
  menuConfigId: number | null;
}

export interface IFunctionChecked {
  id: number;
  applicationFunctionId: number;
  applicationId: number;
  applicationFeatureId: number;
}
