name: staging-up
on:
  push:
    branches: [staging]

jobs:
  cmsthietbi:
    runs-on: qi-runner-31
    steps:
      - uses: actions/checkout@v3
      - name: cmsthietbi web up
        run: docker-compose -f docker-compose-staging.yml -p qipf_cmsthietbi_web up -d --build
  restarthaproxy:
    runs-on: qi-runner-31
    steps:
      - name: restart ha-proxy
        run: docker restart qi-31-haproxy-qipfhaproxy-1
    needs: cmsthietbi
  clear:
    runs-on: qi-runner-31
    steps:
      - name: clean build cache file
        run: docker builder prune -a -f
      - name: clean all temp image
        run: docker image prune -f
    needs: restarthaproxy
