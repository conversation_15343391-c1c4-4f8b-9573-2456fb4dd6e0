import { DataConstant } from "@/constant";

export interface ISchoolConfig {
  activedTime: string;
  address: string;
  createdAt: string;
  createdBy: string;
  districtCode: string;
  divisionCode: string;
  divisionName: string;
  doetCode: string;
  doetName: string;
  email: string;
  fax: string;
  groupUnitCode: string;
  id: number;
  lat: string;
  lng: string;
  location: string;
  logo: string;
  name: string;
  phone: string;
  principal: string;
  principalEmail: string;
  principalPhone: string;
  provinceCode: string;
  schoolCode: string;
  schoolLevel: number;
  schoolLevelName: string;
  schoolLevels: number[];
  schoolType: string;
  shortName: string;
  status: number;
  themeConfig: IThemeConfig;
  updatedAt: string;
  updatedBy: string;
  useBookStoreMinhViet: number;
  wardCode: string;
  website: string;
  initedTime: string;
  isInitedData: number;
  isActivedMinhViet: number;
  quote: string;
  linkFacebook: string;
  linkYoutube: string;
  linkInstagram: string;
  linkTiktok: string;
  linkZalo: string;
  isConvertData: DataConstant.BOOLEAN_TYPE;
  loginToRead: DataConstant.BOOLEAN_TYPE;
  viewBookByBookType: DataConstant.BOOLEAN_TYPE;
}

export interface IThemeConfig {
  id: number;
  createdBy: number;
  updatedBy: number;
  createdAt: string;
  updatedAt: string;
  code: string;
  name: string;
  imageUrl: string;
  status: DataConstant.BOOLEAN_TYPE;
  backgroundColor: string;
  fontColor: string;
  groupUnitCode: string;
  doetCode: string;
  schoolCode: string;
  divisionCode: string;
  schoolId: number;
}
