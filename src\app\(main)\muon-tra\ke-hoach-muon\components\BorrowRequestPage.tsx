"use client";

import { TablePageLayout } from "@/components/common";
import React, { useEffect, useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import {
  ActionType,
  FilterConfig,
} from "@/components/common/TablePageLayout/type";
import { BORROW_REQUEST } from "@/constant/api.const";
import dynamic from "next/dynamic";
import { formatDayjsWithType } from "@/utils/format.utils";
import { useAppDispatch, useAppStore } from "@/redux/hook";
import { createInjectableSaga } from "@/saga/injectableSaga";
import { systemSaga } from "@/saga/system.saga";
import { systemActions } from "@/redux/system.slice";
import { borrowRequestSaga } from "@/saga/device/borrowRequest.saga";
import { eduDeviceSaga } from "@/saga/device/eduDevice.saga";
import { Typography } from "@mui/material";
import { borrowRequestActions } from "@/redux/device/borrowRequest.slice";
import DetailDrawer from "./DetailDrawer";
import { BORROW_TYPE, IBorrowRequest } from "@/models/eduDevice.model";
const CreateModal = dynamic(() => import("./CreateModal"), {
  ssr: false,
});
const EditModal = dynamic(() => import("./EditModal"), {
  ssr: false,
});

const BorrowRequestPage = () => {
  const dispatch = useAppDispatch();
  const store = useAppStore();

  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    createInjectableSaga("systemReducer", systemSaga).injectInto(store);
    createInjectableSaga("borrowRequestReducer", borrowRequestSaga).injectInto(
      store
    );
    createInjectableSaga("eduDeviceReducer", eduDeviceSaga).injectInto(store);
    setIsClient(true);

    return () => {
      dispatch(systemActions.systemReset());
    };
  }, []);

  if (!isClient) return null;

  return (
    <>
      <TablePageLayout<IBorrowRequest>
        apiUrl={BORROW_REQUEST}
        filterConfig={FILTER_CONFIG}
        tableProps={tableProps}
        actions={ACTIONS}
        CreateModalComponent={CreateModal}
        EditModalComponent={EditModal}
        formConfig={FORM_CONFIG}
      />
      <DetailDrawer />
    </>
  );
};

const FORM_CONFIG = {
  deleteUrl: BORROW_REQUEST,
  detailUrl: BORROW_REQUEST,
};
const ACTIONS: ActionType[] = ["create", "delete", "update"];

const FILTER_CONFIG: FilterConfig[] = [
  {
    key: "range",
    label: "Thời gian",
    type: "dateRange",
    fieldProps: {
      maxDate: null,
    },
    keyDateRange: ["fromDate", "toDate"],
  },
  {
    type: "text",
    key: "searchKey",
    label: "Tên giáo viên",
  },
];

const COLUMN: ColumnDef<IBorrowRequest>[] = [
  {
    id: "range",
    header: "Thời gian đăng ký",
    minSize: 300,
    meta: {
      cellSx: {
        whiteSpace: "nowrap",
      },
    },
    cell: ({ row }) => {
      if (row.original.borrowType === BORROW_TYPE.week)
        return `${row.original.schoolWeekConfigName}`;
      else
        return `${formatDayjsWithType(
          row.original.borrowFromDate
        )} - ${formatDayjsWithType(row.original.borrowToDate)}`;
    },
  },
  {
    id: "teacher",
    accessorKey: "teacherName",
    header: "Giáo viên",
    minSize: 500,
    meta: {
      cellSx: {
        px: 0,
        py: 0,
      },
    },
    cell: ({ row }) => <TeacherCell data={row.original} />,
  },
  {
    id: "empty",
    accessorKey: "empty",
    header: "",
    size: 1,
    meta: {
      headerSx: {
        width: "100%",
      },
    },
  },
];
const tableProps = {
  columns: COLUMN,
};
export default BorrowRequestPage;

const TeacherCell = ({ data }: { data: IBorrowRequest }) => {
  const dispatch = useAppDispatch();
  return (
    <Typography
      onClick={() => {
        dispatch(borrowRequestActions.toggleDetail(true));
        dispatch(borrowRequestActions.getBorrowRequest(data.id as number));
      }}
      color="primary"
      sx={{
        cursor: "pointer",
        px: 1,
        py: "7px",
      }}
    >
      {data.teacherName}
    </Typography>
  );
};
