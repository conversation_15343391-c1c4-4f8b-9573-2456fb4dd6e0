import { BorrowStatusEnum } from "@/models/eduDevice.model";
import { Chip } from "@mui/material";
import { useMemo } from "react";

export const BorrowStatusCell = ({ status }: { status: number }) => {
  const [bgcolor, name] = useMemo((): [string, string] => {
    switch (status) {
      case BorrowStatusEnum.Register:
        return ["warning.main", "Đăng ký"];
      case BorrowStatusEnum.Borrowing:
        return ["success.main", "Đang mượn"];
      case BorrowStatusEnum.Returned:
        return ["info.main", "Đã trả"];
      case BorrowStatusEnum.Reject:
        return ["error.main", "Từ chối"];
      default:
        return ["", ""];
    }
  }, [status]);

  return (
    <Chip
      label={name}
      sx={{
        bgcolor: bgcolor,
        color: "white",
        minWidth: "92px",
      }}
    />
  );
};
