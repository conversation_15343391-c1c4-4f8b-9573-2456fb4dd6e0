"use client";

import { Box, Tab, Tabs } from "@mui/material";
import ReturnDeviceList from "./ReturnDeviceList";
import ReturnDeviceHistory from "./ReturnDeviceHistory";
import { useEffect, useState } from "react";
import { systemActions } from "@/redux/system.slice";
import { createInjectableSaga } from "@/saga/injectableSaga";
import { systemSaga } from "@/saga/system.saga";
import { useAppDispatch, useAppStore } from "@/redux/hook";
import returnDeviceSaga from "../returnDevice.saga";

const ReturnDevicePage = () => {
  const store = useAppStore();
  const dispatch = useAppDispatch();
  useEffect(() => {
    createInjectableSaga("systemReducer", systemSaga).injectInto(store);
    createInjectableSaga("returnDeviceReducer", returnDeviceSaga).injectInto(
      store
    );

    return () => {
      dispatch(systemActions.systemReset());
    };
  }, []);
  const [selectedTab, setSelectedTab] = useState<string>("1");
  return (
    <Box>
      <Tabs
        value={selectedTab}
        onChange={(_, value) => setSelectedTab(value)}
        sx={{ backgroundColor: "background.grey" }}
      >
        <Tab label="Danh sách TB cần trả" value="1" />
        <Tab label="Lịch sử trả thiết bị" value="2" />
      </Tabs>
      {selectedTab === "1" ? <ReturnDeviceList /> : <ReturnDeviceHistory />}
    </Box>
  );
};

export default ReturnDevicePage;
