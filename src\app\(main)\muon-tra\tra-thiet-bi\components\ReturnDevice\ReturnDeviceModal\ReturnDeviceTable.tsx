import { TablePageLayout } from "@/components/common";
import {
  TABLE_MODAL_FULL_HEIGHT,
  DATE_TIME_YYYYescape,
} from "@/constant/app.const";
import {
  IReturnDeviceList,
  ReturnDeviceFieldName,
} from "../../../returnDevice.model";
import {
  makeSelectTotalBrokenById,
  makeSelectTotalLostById,
  makeSelectTotalConsumedById,
  makeSelectNotesById,
  makeSelectBorrowReturnDateById,
  updateEditedValue,
  selectEditedRows,
} from "../../../returnDevice.slice";
import { RETURN_DEVICE_LIST } from "@/constant/api.const";
import { useMemo, useState, memo, useCallback, useRef, useEffect } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { formatNumber } from "@/utils/format.utils";
import {
  ActionType,
  FilterConfig,
} from "@/components/common/TablePageLayout/type";
import { selectTeacherComboList } from "@/redux/system.slice";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { AppTextField } from "@/components/common";
import AppDatePicker from "@/components/common/AppDatePicker";
import { toast } from "sonner";
import { Typography } from "@mui/material";
import dayjs from "dayjs";

interface ReturnDeviceTableProps {
  selectedRows: IReturnDeviceList[];
  onSelectedRowsChange?: (selectedRows: IReturnDeviceList[]) => void;
  approveTableSelectedRows?: IReturnDeviceList[];
}

interface NumberCellWithReduxProps {
  rowId: number;
  fieldName: ReturnDeviceFieldName;
  maxValue?: number;
  disabled?: boolean;
  rowIndex?: number;
  originalValue?: number;
  totalQuantity?: number;
}

interface NotesCellWithReduxProps {
  rowId: number;
  rowIndex?: number;
  originalValue?: string;
}

interface DateCellWithReduxProps {
  rowId: number;
  rowIndex?: number;
  originalValue?: string;
}

const ReturnDeviceTable = ({
  selectedRows,
  onSelectedRowsChange,
  approveTableSelectedRows,
}: ReturnDeviceTableProps) => {
  const [totalDataCount, setTotalDataCount] = useState(0);
  const [headerDate, setHeaderDate] = useState<dayjs.Dayjs | null>(() =>
    dayjs()
  );
  const dispatch = useAppDispatch();

  const editedData = useAppSelector(selectEditedRows);

  const approveTableSelectedRowsRef = useRef(approveTableSelectedRows);

  useEffect(() => {
    approveTableSelectedRowsRef.current = approveTableSelectedRows;
  }, [approveTableSelectedRows]);

  useEffect(() => {
    if (!selectedRows || selectedRows.length === 0) {
      setHeaderDate(dayjs());
      return;
    }
    const allDates = selectedRows.map((row) => {
      const edited = editedData[row.id];
      return edited?.borrowReturnDate ?? row.borrowReturnDate;
    });
    const firstDate = allDates[0];
    const allSame = allDates.every((date) => date === firstDate);
    if (!allSame) {
      setHeaderDate(null);
    }
  }, [selectedRows, editedData]);

  useEffect(() => {
    selectedRows.forEach((row) => {
      dispatch(
        updateEditedValue({
          rowId: row.id,
          field: "borrowReturnDate",
          value: headerDate?.format(DATE_TIME_YYYYescape) || "",
        })
      );
    });
  }, []);

  const handleHeaderDateChange = useCallback(
    (newValue: any) => {
      setHeaderDate(newValue ? dayjs(newValue) : null);
      approveTableSelectedRowsRef.current?.forEach((row) => {
        dispatch(
          updateEditedValue({
            rowId: row.id,
            field: "borrowReturnDate",
            value: newValue || null,
          })
        );
      });
    },
    [dispatch]
  );

  const columns = useMemo(() => {
    return createColumns(headerDate, handleHeaderDateChange);
  }, [headerDate, handleHeaderDateChange]);

  const tableProps = useMemo(() => {
    return {
      ...TABLE_MODAL_FULL_HEIGHT,
      columns,
      rowSelected: selectedRows,
      onRowSelectionChange: (selectedRowsData: IReturnDeviceList[]) => {
        onSelectedRowsChange?.(selectedRowsData);
      },
    };
  }, [columns, selectedRows, onSelectedRowsChange]);

  const teacherOptions = useAppSelector(selectTeacherComboList);

  const teacherFilter = useMemo(() => {
    return createTeacherFilter(selectedRows, teacherOptions);
  }, [selectedRows, teacherOptions]);

  const filterConfig: FilterConfig[] = useMemo(() => {
    return [
      {
        key: "teacherIds",
        type: "select" as const,
        label: "Giáo viên",
        size: 2.4,
        options: teacherOptions,
        value: teacherFilter.teacherOptionsValues,
        isMulti: true,
        hasAllOption: true,
        required: true,
      },
      {
        key: "searchKey",
        type: "text" as const,
        label: "Tìm kiếm",
        size: 2.4,
      },
    ];
  }, [teacherOptions, teacherFilter]);

  const handleFormatData = useCallback(
    (data: IReturnDeviceList[]) => {
      setTotalDataCount(data.length);
      return data;
    },
    [setTotalDataCount]
  );

  return (
    <TablePageLayout<IReturnDeviceList>
      apiUrl={RETURN_DEVICE_LIST}
      methodFetch="POST"
      tableProps={tableProps}
      actions={ACTIONS}
      filterConfig={filterConfig}
      formatData={handleFormatData}
      collapseFilterCustom={() => (
        <>
          <Typography
            variant="body1"
            color="primary"
            sx={{ width: "100%", textAlign: "right" }}
          >
            {approveTableSelectedRows?.length} / {totalDataCount} thiết bị đã
            chọn
          </Typography>
        </>
      )}
    />
  );
};

const createTeacherFilter = (
  selectedRows: IReturnDeviceList[],
  teacherOptions: any[]
) => {
  if (!selectedRows || selectedRows.length === 0) {
    return {
      teacherIds: undefined,
      teacherOptionsValues: undefined,
    };
  }

  const teacherIds = Array.from(
    new Set(selectedRows.map((row) => row.teacherId).filter(Boolean))
  );

  const teacherOptionsValues = teacherIds
    .map((id) => teacherOptions.find((option) => option.id === id))
    .filter((option) => option !== undefined);

  return {
    teacherIds,
    teacherOptionsValues,
  };
};

const ACTIONS: ActionType[] = ["check"];

const NumberCellWithRedux = memo(
  ({
    rowId,
    fieldName,
    maxValue,
    disabled = false,
    rowIndex,
    originalValue,
    totalQuantity,
  }: NumberCellWithReduxProps) => {
    const dispatch = useAppDispatch();

    const selectValueById = useMemo(() => {
      switch (fieldName) {
        case "totalBroken":
          return makeSelectTotalBrokenById();
        case "totalLost":
          return makeSelectTotalLostById();
        case "totalConsumed":
          return makeSelectTotalConsumedById();
        default:
          return makeSelectTotalBrokenById();
      }
    }, [fieldName]);

    const selectOtherValueById = useMemo(() => {
      switch (fieldName) {
        case "totalBroken":
          return makeSelectTotalLostById();
        case "totalLost":
          return makeSelectTotalBrokenById();
        default:
          return makeSelectTotalBrokenById();
      }
    }, [fieldName]);

    const value =
      useAppSelector((state) => selectValueById(state, rowId)) ?? originalValue;
    const otherValue = useAppSelector((state) =>
      selectOtherValueById(state, rowId)
    );

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      const newValue = Number(inputValue);

      if (inputValue === "") {
        dispatch(updateEditedValue({ rowId, field: fieldName, value: 0 }));
        return;
      }

      if (isNaN(newValue) || newValue < 0) {
        toast.warning("Cảnh báo", {
          description: "Vui lòng nhập số hợp lệ (>= 0).",
        });
        return;
      }

      if (maxValue !== undefined && newValue > maxValue) {
        toast.warning("Cảnh báo", {
          description: `Số lượng không được vượt quá ${maxValue}.`,
        });
        dispatch(
          updateEditedValue({ rowId, field: fieldName, value: maxValue })
        );
        return;
      }

      if (
        totalQuantity !== undefined &&
        (fieldName === "totalBroken" || fieldName === "totalLost")
      ) {
        const currentOtherValue = otherValue ?? 0;
        const totalSum = newValue + currentOtherValue;

        if (totalSum > totalQuantity) {
          toast.warning("Cảnh báo", {
            description: `Tổng số lượng hỏng (${
              fieldName === "totalBroken" ? newValue : currentOtherValue
            }) và mất (${
              fieldName === "totalLost" ? newValue : currentOtherValue
            }) không được vượt quá ${totalQuantity}. Giá trị tối đa cho phép: ${
              totalQuantity - currentOtherValue
            }.`,
          });
          const maxAllowedValue = totalQuantity - currentOtherValue;
          dispatch(
            updateEditedValue({
              rowId,
              field: fieldName,
              value: Math.max(0, maxAllowedValue),
            })
          );
          return;
        }
      }

      dispatch(updateEditedValue({ rowId, field: fieldName, value: newValue }));
    };

    return (
      <AppTextField
        type="number"
        value={value ?? ""}
        onChange={handleChange}
        disabled={disabled}
        size="small"
        name={`${fieldName}.${rowIndex}`}
        key={`${fieldName}.${rowIndex}`}
        slotProps={{
          htmlInput: {
            min: 0,
            max: maxValue,
            step: 1,
          },
        }}
      />
    );
  }
);

const NotesCellWithRedux = memo(
  ({ rowId, rowIndex, originalValue }: NotesCellWithReduxProps) => {
    const dispatch = useAppDispatch();

    const selectNotesById = useMemo(() => {
      return makeSelectNotesById();
    }, []);

    const value =
      useAppSelector((state) => selectNotesById(state, rowId)) ?? originalValue;

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      dispatch(updateEditedValue({ rowId, field: "notes", value: inputValue }));
    };

    return (
      <AppTextField
        type="text"
        value={value ?? ""}
        onChange={handleChange}
        size="small"
        name={`notes.${rowIndex}`}
        key={`notes.${rowIndex}`}
      />
    );
  }
);

const DateCellWithRedux = memo(
  ({ rowId, rowIndex, originalValue }: DateCellWithReduxProps) => {
    const dispatch = useAppDispatch();

    const selectBorrowReturnDateById = useMemo(() => {
      return makeSelectBorrowReturnDateById();
    }, []);

    const value =
      useAppSelector((state) => selectBorrowReturnDateById(state, rowId)) ??
      originalValue;

    const handleChange = (newValue: any) => {
      const formattedDate = newValue;
      dispatch(
        updateEditedValue({
          rowId,
          field: "borrowReturnDate",
          value: formattedDate || "",
        })
      );
    };

    return (
      <AppDatePicker
        key={`borrowReturnDate.${rowIndex}`}
        value={value ? dayjs(value) : dayjs().toDate()}
        onChange={handleChange}
        slotProps={{
          textField: {
            size: "small",
            fullWidth: true,
            name: `borrowReturnDate.${rowIndex}`,
          },
        }}
        maxDate={dayjs()}
      />
    );
  }
);

const createColumns = (
  headerDate: dayjs.Dayjs | null,
  handleHeaderDateChange: (newValue: any) => void
): ColumnDef<IReturnDeviceList>[] => [
  {
    id: "borrowReturnDate",
    header: ({ column }) => {
      return (
        <>
          <Typography variant="body1" color="primary.contrastText">
            Ngày trả
          </Typography>
          <AppDatePicker
            value={headerDate}
            onChange={handleHeaderDateChange}
            slotProps={{
              textField: {
                size: "small",
                fullWidth: true,
                placeholder: "Ngày trả cho tất cả",
              },
            }}
            maxDate={dayjs()}
          />
        </>
      );
    },
    accessorKey: "borrowReturnDate",
    cell: ({ row }) => (
      <DateCellWithRedux
        rowId={row.original.id}
        rowIndex={row.index}
        originalValue={row.original.borrowReturnDate}
      />
    ),
    size: 150,
  },
  {
    id: "teacherName",
    header: "Giáo viên",
    accessorKey: "teacherName",
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
  },
  {
    id: "deviceCode",
    header: "Mã thiết bị",
    accessorKey: "deviceCode",
  },
  {
    id: "roomName",
    header: "Kho/Phòng",
    accessorKey: "roomName",
  },
  {
    id: "quantity",
    header: "Số lượng",
    accessorKey: "quantity",
    cell: ({ row }) => formatNumber(row.original.quantity),
    size: 60,
    meta: {
      align: "right",
    },
  },

  {
    header: "Tình trạng sau khi trả",
    columns: [
      {
        id: "totalBroken",
        header: "Hỏng",
        accessorKey: "totalBroken",
        cell: ({ row }) => {
          return (
            <NumberCellWithRedux
              rowId={row.original.id}
              fieldName="totalBroken"
              maxValue={row.original.quantity}
              totalQuantity={row.original.quantity}
              rowIndex={row.index}
              originalValue={row.original.totalBroken}
              disabled={row.original.isConsumable}
            />
          );
        },
        size: 100,
        meta: {
          align: "right",
        },
      },
      {
        id: "totalLost",
        header: "Mất",
        accessorKey: "totalLost",
        cell: ({ row }) => {
          return (
            <NumberCellWithRedux
              rowId={row.original.id}
              fieldName="totalLost"
              maxValue={row.original.quantity}
              totalQuantity={row.original.quantity}
              rowIndex={row.index}
              originalValue={row.original.totalLost}
              disabled={row.original.isConsumable}
            />
          );
        },
        size: 100,
        meta: {
          align: "right",
        },
      },
    ],
  },
  {
    id: "totalConsumed",
    header: "Tiêu hao",
    accessorKey: "totalConsumed",
    cell: ({ row }) => (
      <NumberCellWithRedux
        rowId={row.original.id}
        fieldName="totalConsumed"
        maxValue={row.original.quantity}
        rowIndex={row.index}
        originalValue={row.original.totalConsumed}
        disabled={!row.original.isConsumable}
      />
    ),
    size: 100,
    meta: {
      align: "right",
    },
  },
  {
    id: "notes",
    header: "Ghi chú",
    accessorKey: "notes",
    cell: ({ row }) => (
      <NotesCellWithRedux
        rowId={row.original.id}
        rowIndex={row.index}
        originalValue={row.original.notes}
      />
    ),
    size: 200,
  },
];

export default ReturnDeviceTable;
