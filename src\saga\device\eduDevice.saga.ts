import { PayloadAction } from "@reduxjs/toolkit";
import { call, put, takeLatest } from "redux-saga/effects";
import {
  DataListResponseModel,
  DataResponseModel,
  IPaginationModel,
} from "@/models/response.model";
import { ApiConstant, EnvConstant } from "@/constant";

import { toast } from "sonner";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import { IDeviceParams, IEduDevice } from "@/models/eduDevice.model";
import { eduDeviceActions } from "@/redux/device/eduDevice.slice";
import {
  getDeviceDetailService,
  getDeviceDefinitionService,
  getDeviceService,
} from "@/services/eduDevice.service";

function* getDeviceSaga(action: PayloadAction<IDeviceParams>) {
  try {
    toggleAppProgress(true);

    const response: DataListResponseModel<IEduDevice> = yield call(
      getDeviceDefinitionService,
      action.payload
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        eduDeviceActions.getDeviceSuccess({
          data: response.data.data,
          totalCount: response.data.totalCount,
        })
      );
      return;
    }

    throw response;
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

function* chooseDeviceSaga(
  action: PayloadAction<{
    data: IEduDevice;
    onSuccess: (data: IEduDevice) => void;
  }>
) {
  try {
    toggleAppProgress(true);

    const response: DataResponseModel<IEduDevice> = yield call(
      getDeviceDetailService,
      action.payload.data.id as number
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      const data = response.data;
      yield call(action.payload.onSuccess, data);
      return;
    }

    throw response;
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

function* getDeviceOriginalSaga(
  action: PayloadAction<{
    params: IDeviceParams & IPaginationModel;
    onTotalChange: (total: number) => void;
    isReset?: boolean;
  }>
) {
  try {
    toggleAppProgress(true);

    const response: DataListResponseModel<IEduDevice> = yield call(
      getDeviceService,
      action.payload.params
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield call(action.payload.onTotalChange, response.data.totalCount);
      yield put(
        eduDeviceActions.getDeviceOriginalSuccess({
          data: response.data.data,
          totalCount: response.data.totalCount,
          isReset: action.payload.isReset,
        })
      );
      return;
    }

    throw response;
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

export function* eduDeviceSaga() {
  yield takeLatest(eduDeviceActions.getDevice.type, getDeviceSaga);
  yield takeLatest(eduDeviceActions.chooseDevice.type, chooseDeviceSaga);
  yield takeLatest(
    eduDeviceActions.getDeviceOriginal.type,
    getDeviceOriginalSaga
  );
}
