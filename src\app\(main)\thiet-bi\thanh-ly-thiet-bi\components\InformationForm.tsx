import { AppFormTextField, GridFormContainer } from "@/components/common";
import AppFormLayoutPanel from "@/components/common/AppFormLayoutPanel";
import AppFormDatePicker from "@/components/common/form/AppFormDatePicker";
import { Grid } from "@mui/material";
import dayjs from "dayjs";
import { memo } from "react";
import { useFormState } from "react-hook-form";

const InformationForm = ({ control }) => {
  const { errors } = useFormState({ control });

  return (
    <AppFormLayoutPanel title="Thông tin chung">
      <GridFormContainer>
        <Grid container spacing={2} size={12}>
          <Grid size={6}>
            <AppFormTextField
              control={control}
              name="documentNumber"
              label="Số phiếu"
              rules={{
                required: "Vui lòng nhập số phiếu.",
              }}
              textfieldProps={{
                error: !!errors.documentNumber,
                helperText: errors.documentNumber?.message as string,
              }}
            />
          </Grid>
          <Grid size={6}>
            <AppFormDatePicker
              control={control}
              name="documentDate"
              label="Ngày lập"
              rules={{
                required: "Vui lòng chọn ngày lập.",
              }}
              datePickerProps={{
                maxDate: dayjs(),
                slotProps: {
                  textField: {
                    error: !!errors.documentDate,
                    helperText: errors.documentDate?.message as string,
                  },
                },
              }}
            />
          </Grid>
        </Grid>
        <Grid size={12}>
          <AppFormTextField
            control={control}
            name="notes"
            label="Nội dung"
            textfieldProps={{
              error: !!errors.notes,
              helperText: errors.notes?.message as string,
            }}
          />
        </Grid>
      </GridFormContainer>
    </AppFormLayoutPanel>
  );
};

export default memo(InformationForm);
