import { ApiConstant, EnvConstant } from "@/constant";
import { DATE_TIME_YYYYescape } from "@/constant/app.const";
import { IDeviceParams } from "@/models/eduDevice.model";
import {
  DataListResponseModel,
  DataResponseModel,
  IPaginationModel,
} from "@/models/response.model";
import { ISearchParams, ITeacher } from "@/models/system.model";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import { PayloadAction } from "@reduxjs/toolkit";
import dayjs from "dayjs";
import { call, put, select, takeLatest } from "redux-saga/effects";
import { toast } from "sonner";
import {
  IDeviceItem,
  IDeviceLiquidationAction,
  ITransactionTeams,
} from "./deviceLiquidation.model";
import {
  getTeacherChooseService,
  postDeviceLiquidationService,
  putUpdateDeviceLiquidationService,
} from "./deviceLiquidation.service";
import {
  deviceLiquidationActions,
  selectDevices,
  selectorDeviceDeleted,
  selectTeamlead,
} from "./deviceLiquidation.slice";
import { getDeviceService } from "@/services/eduDevice.service";

function* getDeviceChooseSaga(
  action: PayloadAction<IDeviceParams & IPaginationModel>
) {
  try {
    toggleAppProgress(true);

    const response: DataListResponseModel<IDeviceItem> = yield call(
      getDeviceService,
      action.payload
    );
    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(deviceLiquidationActions.getDeviceChooseSuccess(response.data));
    } else {
      throw new Error(response.message);
    }
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

function* getTeacherChooseSaga(action: PayloadAction<ISearchParams>) {
  try {
    toggleAppProgress(true);

    const response: DataListResponseModel<ITeacher> = yield call(
      getTeacherChooseService,
      action.payload
    );
    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        deviceLiquidationActions.getTeacherChooseListSuccess(response.data)
      );
    } else {
      throw new Error(response.message);
    }
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

function* postDeviceLiquidationSaga(
  action: PayloadAction<{
    data: IDeviceLiquidationAction;
    onSuccess?: () => void;
  }>
) {
  try {
    toggleAppProgress(true);
    const teamLead: ITransactionTeams[] = yield select(selectTeamlead);
    const devices: IDeviceItem[] = yield select(selectDevices);

    if (!devices.length) {
      toast.warning("Cảnh báo", {
        description: "Danh sách thiết bị không được bỏ trống",
      });
      return;
    }
    const formData = action.payload.data;
    const payload: IDeviceLiquidationAction = {
      ...formData,
      documentDate: formData.documentDate
        ? dayjs(formData.documentDate).format(DATE_TIME_YYYYescape)
        : "",
      devices,
      transactionTeams: teamLead,
    };

    const response: DataResponseModel<unknown> = yield call(
      postDeviceLiquidationService,
      payload
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      if (action.payload.onSuccess) yield call(action.payload.onSuccess);
      toast.success("Thành công", {
        description: "Thêm mới thiết bị thanh lý thành công",
      });
      return;
    }

    throw response;
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

function* putEditDeviceLiquidationSaga(
  action: PayloadAction<{
    id?: number;
    data: IDeviceLiquidationAction;
    onSuccess?: () => void;
  }>
) {
  try {
    toggleAppProgress(true);
    const teamLead: ITransactionTeams[] = yield select(selectTeamlead);
    const devices: IDeviceItem[] = yield select(selectDevices);
    const deviceDeleted = yield select(selectorDeviceDeleted);

    if (!devices.length) {
      toast.warning("Cảnh báo", {
        description: "Danh sách thiết bị không được bỏ trống",
      });
      return;
    }

    const formData = action.payload.data;
    const payload: IDeviceLiquidationAction = {
      ...formData,
      documentDate: formData.documentDate
        ? dayjs(formData.documentDate).format(DATE_TIME_YYYYescape)
        : "",
      devices,
      transactionTeams: teamLead,
      deleteDeviceTransactionItemIds: deviceDeleted,
    };

    const response: DataResponseModel<unknown> = yield call(
      putUpdateDeviceLiquidationService,
      payload,
      action.payload?.id
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      if (action.payload.onSuccess) yield call(action.payload.onSuccess);
      toast.success("Thành công", {
        description: "Thêm mới thiết bị thanh lý thành công",
      });
      return;
    }

    throw response;
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

export function* deviceLiquidationSaga() {
  yield takeLatest(
    deviceLiquidationActions.getDeviceChoose.type,
    getDeviceChooseSaga
  );
  yield takeLatest(
    deviceLiquidationActions.getTeacherChooseList.type,
    getTeacherChooseSaga
  );
  yield takeLatest(
    deviceLiquidationActions.addDeviceLiquidation.type,
    postDeviceLiquidationSaga
  );
  yield takeLatest(
    deviceLiquidationActions.updateDeviceLiquidation.type,
    putEditDeviceLiquidationSaga
  );
}
