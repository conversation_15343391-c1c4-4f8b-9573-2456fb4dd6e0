import {
  SimpleTreeView,
  SimpleTreeViewProps,
} from "@mui/x-tree-view/SimpleTreeView";
import { CloseSquare, MinusSquare, PlusSquare } from "@/components/icons";
import { memo } from "react";

interface TreeViewCustomProps extends SimpleTreeViewProps<true> {
  selectedItems?: string[];
  onSelectedItemsChange?: (
    event: React.SyntheticEvent | null,
    ids: string[]
  ) => void;
}

const TreeViewCustom = ({
  children,
  sx,
  selectedItems,
  onSelectedItemsChange,
  ...otherProps
}: TreeViewCustomProps) => {
  return (
    <SimpleTreeView
      aria-label="customized"
      className="hidden-scroll-bar"
      selectedItems={selectedItems}
      onSelectedItemsChange={onSelectedItemsChange}
      sx={{
        color: "primary.main",
        "& .MuiTreeItem-iconContainer": {
          width: "unset",
        },
        overflow: "auto",
        flexGrow: 1,
        ...sx,
      }}
      slots={{
        expandIcon: PlusSquare,
        collapseIcon: MinusSquare,
        endIcon: CloseSquare,
      }}
      {...otherProps}
    >
      {children}
    </SimpleTreeView>
  );
};

export default memo(TreeViewCustom);
