import {
  createSlice,
  createSelector,
  WithSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { rootReducer } from "@/redux/reducer";
import { RootState } from "@/redux/store";
import { formatDayjsWithType } from "@/utils/format.utils";
import { IBorrowRequest, IBorrowRequestDevice } from "@/models/eduDevice.model";
import { ISchoolWeek } from "@/models/system.model";

/* ------------- State & Interface ------------- */
export interface IInitialState {
  isFetching: boolean;

  totalData: number;
  isOpenDetail: boolean;
  dataSelected: IBorrowRequest | null;
  requestOfTeacher: IBorrowRequest | null;

  isShowResetBtn: boolean;
}

const initialState: IInitialState = {
  isFetching: false,

  totalData: 0,

  isShowResetBtn: false,

  isOpenDetail: false,
  dataSelected: null,
  requestOfTeacher: null,
};

/* ------------- Selector ------------- */
export const selectIsShowReset = (state: RootState) =>
  state.borrowRequestReducer?.isShowResetBtn;
export const selectIsOpenDetail = (state: RootState) =>
  state.borrowRequestReducer?.isOpenDetail;
export const selectBorrowRequest = (state: RootState) =>
  state.borrowRequestReducer?.dataSelected;
export const selectBorrowRequestOfTeacher = (state: RootState) =>
  state.borrowRequestReducer?.requestOfTeacher;
export const selectHasBorrowRequestOfTeacher = createSelector(
  [(state: RootState) => state.borrowRequestReducer?.requestOfTeacher],
  (requestOfTeacher) => Boolean(requestOfTeacher)
);


export const selectDeviceNameAttach = (id?: string | null) =>
  createSelector(
    [
      (state: RootState) =>
        state.borrowRequestReducer?.dataSelected?.borrowRequestDevices,
    ],
    (devices: IBorrowRequestDevice[] | undefined) => {
      if (!devices) return "";
      return devices
        .filter((item) => item.roomDeviceGuid === id)
        .map((item) => `${item.deviceName} (${item.quantity})`)
        .join(", ");
    }
  );

/* ------------- Reducers ------------- */
const reducers = {
  getBorrowRequest: (state: IInitialState, action: PayloadAction<number>) => {
    state.dataSelected = null;
  },
  getBorrowRequestSuccess: (
    state: IInitialState,
    action: PayloadAction<IBorrowRequest | null>
  ) => {
    state.dataSelected = action.payload;
  },
  getBorrowRequestByTeacherWeek: (
    state: IInitialState,
    action: PayloadAction<{
      schoolWeekConfigId: number;
      teacherId: number;
    }>
  ) => {},
  getBorrowRequestByTeacherWeekSuccess: (
    state: IInitialState,
    action: PayloadAction<IBorrowRequest | null>
  ) => {
    state.requestOfTeacher = action.payload;
    state.isShowResetBtn = Boolean(action.payload);
  },
  setFetching: (state: IInitialState, action: { payload: boolean }) => {
    state.isFetching = action.payload;
  },
  toggleDetail: (state: IInitialState, action: PayloadAction<boolean>) => {
    state.isOpenDetail = action.payload;
  },
  reset: (state: IInitialState) => {
    state.isFetching = false;
    state.totalData = 0;
    state.isShowResetBtn = false;
    state.dataSelected = null;
    state.requestOfTeacher = null;
    state.isOpenDetail = false;
  },
};

/* ------------- Selectors ------------- */
const selectors = {
  selectSlice: (state: IInitialState) => state,
  selectIsFetching: (state: IInitialState) => state.isFetching,
};

/* ------------- Slice ------------- */
export const borrowRequestSlice = createSlice({
  name: "borrowRequestReducer",
  initialState,
  reducers,
  selectors,
});

/* ------------- Export Actions ------------- */
export const borrowRequestActions = borrowRequestSlice.actions;

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices
    extends WithSlice<typeof borrowRequestSlice> {}
}

const injectedBorrowRequestSlice = borrowRequestSlice.injectInto(rootReducer);

/* ------------- Export Selectors ------------- */
export const borrowRequestSelectors = {
  selectSlice: injectedBorrowRequestSlice.selectors.selectSlice,
  selectIsFetching: createSelector(
    [injectedBorrowRequestSlice.selectors.selectSlice],
    (slice) => slice.isFetching
  ),
};
