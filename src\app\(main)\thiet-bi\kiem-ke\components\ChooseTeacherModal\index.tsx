import ChooseTeacherTable from "@/app/(main)/thiet-bi/kiem-ke/components/ChooseTeacherModal/ChooseTeacherTable";
import { inventoryTransactionActions } from "@/app/(main)/thiet-bi/kiem-ke/inventoryTransaction.slice";
import AppModal from "@/components/common/modal/AppModal";
import { CursorClickIcon } from "@/components/icons";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { selectTeacherComboList, systemActions } from "@/redux/system.slice";
import { Button } from "@mui/material";
import React, { useCallback, useState } from "react";

const ChooseTeacherModal = () => {
  const dispatch = useAppDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const teachers = useAppSelector(selectTeacherComboList);

  const handleOpen = useCallback(() => {
    dispatch(systemActions.getTeacherComboList());
    setIsOpen(true);
  }, [dispatch]);

  const handleClose = useCallback(() => {
    dispatch(inventoryTransactionActions.resetChooseModal());
    setIsOpen(false);
  }, []);

  const handleChooseDevice = useCallback(() => {
    dispatch(inventoryTransactionActions.addTransactionTeams(teachers));
    setIsOpen(false);
  }, [dispatch, teachers]);

  return (
    <>
      <Button
        startIcon={<CursorClickIcon />}
        onClick={handleOpen}
        variant="contained"
        size="small"
      >
        Chọn giáo viên
      </Button>
      <AppModal
        fullWidth
        maxWidth="lg"
        slotProps={{
          paper: {
            sx: {
              height: "100%",
            },
          },
        }}
        onClose={handleClose}
        isOpen={isOpen}
        modalTitleProps={{ title: "Chọn giáo viên" }}
        modalContentProps={{
          sx: { py: 1 },
          content: <ChooseTeacherTable />,
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleClose}
              >
                Đóng
              </Button>
              <Button variant="contained" onClick={handleChooseDevice}>
                Chọn
              </Button>
            </>
          ),
        }}
      />
    </>
  );
};

export default ChooseTeacherModal;
