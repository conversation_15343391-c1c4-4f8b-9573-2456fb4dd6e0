import React, { memo } from "react";
import { SvgIcon, SvgIconProps } from "@mui/material";

const DeleteIcon = ({ sx, ...otherProps }: SvgIconProps) => {
  return (
    <SvgIcon
      viewBox="0 0 24 24"
      sx={{ fontSize: "inherit", ...sx }}
      {...otherProps}
    >
      <path
        d="M18 8.98975C17.55 8.98975 17.25 9.28975 17.25 9.73975V18.5897C17.25 19.1897 16.8 19.4897 16.35 19.4897H7.65C7.05 19.4897 6.75 19.0397 6.75 18.5897V9.73975C6.75 9.28975 6.45 8.98975 6 8.98975C5.55 8.98975 5.25 9.28975 5.25 9.73975V18.5897C5.25 19.9397 6.3 20.9897 7.65 20.9897H16.2C17.55 20.9897 18.6 19.9397 18.6 18.5897V9.73975C18.75 9.28975 18.45 8.98975 18 8.98975Z"
        fill="currentColor"
      />
      <path
        d="M10.7998 17.2397V9.73975C10.7998 9.28975 10.4998 8.98975 10.1998 8.98975C9.89981 8.98975 9.2998 9.28975 9.2998 9.73975V17.2397C9.2998 17.6897 9.5998 17.9897 10.0498 17.9897C10.4998 17.9897 10.7998 17.6897 10.7998 17.2397Z"
        fill="currentColor"
      />
      <path
        d="M14.7002 17.2397V9.73975C14.7002 9.28975 14.2502 8.98975 13.8002 8.98975C13.3502 8.98975 13.2002 9.28975 13.2002 9.73975V17.2397C13.2002 17.6897 13.5002 17.9897 13.8002 17.9897C14.1002 17.9897 14.7002 17.6897 14.7002 17.2397Z"
        fill="currentColor"
      />
      <path
        d="M19.5 5.98975H15.75V4.93975C15.75 3.88975 14.85 2.98975 13.8 2.98975H10.2C9.15 2.98975 8.25 3.88975 8.25 4.93975V5.98975H4.5C4.05 5.98975 3.75 6.28975 3.75 6.73975C3.75 7.18975 4.05 7.48975 4.5 7.48975H19.5C19.95 7.48975 20.25 7.18975 20.25 6.73975C20.25 6.28975 19.95 5.98975 19.5 5.98975ZM9.75 4.93975C9.75 4.63975 9.9 4.48975 10.2 4.48975H13.8C14.1 4.48975 14.25 4.63975 14.25 4.93975V5.98975H9.75V4.93975Z"
        fill="currentColor"
      />
    </SvgIcon>
  );
};

export default memo(DeleteIcon);
