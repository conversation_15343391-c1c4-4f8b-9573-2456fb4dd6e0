import * as React from "react";
import { AppRouterCacheProvider } from "@mui/material-nextjs/v15-appRouter";
import { ThemeProvider } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import theme from "@/theme/theme";
import InitColorSchemeScript from "@mui/material/InitColorSchemeScript";
import StoreProvider from "./StoreProvider";
import "./globals.scss";
import { Metadata } from "next";
import { AppConstant } from "@/constant";
import AppToaster from "@/components/common/AppToaster";
import { Roboto } from "next/font/google";

const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
  display: "swap",
});

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: AppConstant.APP_TITLE,
    description: AppConstant.APP_DESCRIPTION,
  };
}

export default function RootLayout(props: { children: React.ReactNode }) {
  return (
    <html
      lang="en"
      suppressHydrationWarning
      translate="no"
      className={roboto.className}
    >
      <body>
        <StoreProvider>
          <AppToaster />
          <InitColorSchemeScript attribute="class" />
          <AppRouterCacheProvider options={{ enableCssLayer: true }}>
            <ThemeProvider theme={theme}>
              {/* CssBaseline kickstart an elegant, consistent, and simple baseline to build upon. */}
              <CssBaseline />
              {/* <ModeSwitch /> */}
              {props.children}
            </ThemeProvider>
          </AppRouterCacheProvider>
        </StoreProvider>
      </body>
    </html>
  );
}
