"use client";

import { AppFormTextField } from "@/components/common";
import { CloseEyePasswordIcon, OpenEyePasswordIcon } from "@/components/icons";
import { InputAdornment } from "@mui/material";
import { useState } from "react";
import { Control, useFormState } from "react-hook-form";
import { ILogin } from "../../login.model";

const InfoAccountPanel = ({
  control,
}: {
  control: Control<ILogin, any, ILogin>;
}) => {
  const { errors } = useFormState({ control });

  const [isShowPassword, setIsShowPassword] = useState<boolean>(false);

  const handleClickShowPassword = () => setIsShowPassword((show) => !show);

  return (
    <>
      <AppFormTextField
        name={"username"}
        control={control}
        rules={{ required: "Vui lòng nhập tên đăng nhập" }}
        textfieldProps={{
          autoFocus: true,
          placeholder: "Tê<PERSON> đăng nhập",
          error: !!errors.username,
          helperText: errors.username?.message
            ? errors.username.message
            : errors.username
            ? "<PERSON>ạn nhập sai tên đăng nhập vui lòng liên hệ admin trường của bạn để lấy lại tên đăng nhập."
            : "",
        }}
      />
      <AppFormTextField
        name={"password"}
        control={control}
        rules={{ required: "Vui lòng nhập mật khẩu" }}
        textfieldProps={{
          placeholder: "Mật khẩu",
          InputProps: {
            autoComplete: "on",
            endAdornment: (
              <InputAdornment
                onClick={handleClickShowPassword}
                sx={{
                  cursor: "pointer",
                  height: "100%",
                  marginLeft: "0px",
                  paddingRight: "8px",
                  paddingLeft: "8px",
                }}
                position="end"
              >
                {isShowPassword ? (
                  <OpenEyePasswordIcon />
                ) : (
                  <CloseEyePasswordIcon />
                )}
              </InputAdornment>
            ),
            sx: {
              borderRadius: "8px",
              paddingRight: "0",
            },
          },
          type: isShowPassword ? "text" : "password",
          error: !!errors.password,
          helperText: errors.password?.message
            ? errors.password.message
            : errors.password
            ? "Bạn nhập sai mật khẩu vui lòng liên hệ admin trường của bạn để lấy lại mật khẩu."
            : "",
        }}
      />
    </>
  );
};

export default InfoAccountPanel;
