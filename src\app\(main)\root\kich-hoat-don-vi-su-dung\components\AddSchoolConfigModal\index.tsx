import { AppModal } from "@/components/common";
import { SaveIcon } from "@/components/icons";
import { <PERSON><PERSON>, <PERSON>ack, Tab, Tabs } from "@mui/material";
import { useCallback, useState } from "react";
import { useForm } from "react-hook-form";
import { postSchoolConfig } from "../../hooks/usePostSchoolConfig";
import AddNewTabContent from "./AddNewTabContent";
import AsyncCSDLContent from "./AsyncCSDLContent";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import {
  schoolConfigActions,
  selectIsSelectAll,
  selectRenderTableList,
  selectSelectedList,
} from "../../schoolConfig.slice";

const AddSchoolConfigModal = ({
  open,
  onClose,
  onSuccess,
}: AddSchoolConfigModalProps) => {
  const dispatch = useAppDispatch();
  const [viewTab, setViewTab] = useState(VIEW_TAB.add);
  const isCheckBoxAll = useAppSelector(selectIsSelectAll);
  const tableList = useAppSelector(selectRenderTableList);
  const selectedList = useAppSelector(selectSelectedList);

  const {
    control,
    formState: { errors },
    reset,
    handleSubmit,
    setValue: handleSetValueForm,
  } = useForm({ defaultValues: INIT_VALUE });

  const handleResetState = useCallback(() => {
    reset(INIT_VALUE);
    onClose();
    onSuccess?.();
  }, []);

  const handleSubmitData = useCallback(
    (data: any) => {
      if (viewTab === VIEW_TAB.add) {
        const payload = {
          ...data,
          groupUnitCode: data.groupUnitCode.schoolCode,
          doetCode: data.doetCode.code,
          divisionCode: data.divisionCode.code,
          schoolType: data.schoolType.id,
          schoolLevels: data.schoolLevels.map((item) => item.id),
        };
        postSchoolConfig({ body: payload, onSuccess: handleResetState });
      } else {
        dispatch(
          schoolConfigActions.postMultiMoetSchool({
            data: isCheckBoxAll ? tableList : selectedList,
            onSuccess: handleResetState,
          })
        );
      }
    },
    [viewTab, isCheckBoxAll, tableList, selectedList]
  );

  const handleChangeTab = useCallback((_, value) => {
    setViewTab(value);
  }, []);

  return (
    <AppModal
      component="form"
      onSubmit={handleSubmit(handleSubmitData)}
      isOpen={open}
      onClose={handleResetState}
      maxWidth="lg"
      modalTitleProps={{
        title: "Thêm mới đơn vị sử dụng",
      }}
      sx={{
        "& .MuiDialog-paper": {
          minWidth: 1300,
          borderRadius: "4px",
          boxShadow: "0px 0px 20px 0px rgba(0, 0, 0, 0.25)",
        },
      }}
      modalActionsProps={{
        children: (
          <>
            <Button
              variant="outlined"
              onClick={handleResetState}
              color="secondary"
            >
              Đóng
            </Button>
            <Button type="submit" variant="contained" endIcon={<SaveIcon />}>
              Ghi
            </Button>
          </>
        ),
      }}
      modalContentProps={{
        sx: { pt: 0 },
        content: (
          <Stack spacing={1} minHeight={500}>
            <Stack>
              <Tabs
                sx={{
                  bgcolor: "common.white",
                  width: "fit-content",
                  borderRadius: "4px 4px 0px 0px",
                }}
                value={viewTab}
                onChange={handleChangeTab}
              >
                <Tab value={VIEW_TAB.add} label="Thêm mới" />
                <Tab value={VIEW_TAB.asyncFromCSDL} label="Đồng bộ từ CSDL" />
              </Tabs>
            </Stack>
            {viewTab === VIEW_TAB.add && (
              <AddNewTabContent
                control={control}
                errors={errors}
                onSetValueForm={handleSetValueForm}
              />
            )}
            {viewTab === VIEW_TAB.asyncFromCSDL && <AsyncCSDLContent />}
          </Stack>
        ),
      }}
    />
  );
};

export default AddSchoolConfigModal;

type AddSchoolConfigModalProps = {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
};

const INIT_VALUE = {
  groupUnitCode: null,
  doetCode: null,
  divisionCode: null,
  name: "",
  schoolCode: "",
  shortName: "",
  principal: "",
  phone: "",
  email: "",
  website: "",
  schoolType: null,
  address: "",
  schoolLevels: [],
};

export enum VIEW_TAB {
  add = 1,
  asyncFromCSDL,
}
