"use client";

import AppModal, { AppModalProps } from "@/components/common/modal/AppModal";
import { Button } from "@mui/material";
import React, { memo, useEffect } from "react";
import RegisterForm from "./RegisterForm";
import {
  FormProvider,
  useForm,
  useFormContext,
  useWatch,
} from "react-hook-form";
import { IBorrowRequestAction } from "../borrowRequestModel";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { systemActions } from "@/redux/system.slice";
import {
  borrowRequestActions,
  selectBorrowRequestOfTeacher,
} from "@/redux/device/borrowRequest.slice";
import useActionRequest from "./hooks/useActionRequest";
import { BORROW_TYPE, IBorrowRequest } from "@/models/eduDevice.model";
import { useInitBorrowRequestForm } from "./hooks/useInitBorrowRequestForm";

const CreateModal = ({
  fetchCurrentData,
  defaultDelayedTab,
  onClose,
  ...otherProps
}: CreateModalProps) => {
  const dispatch = useAppDispatch();
  const borrowRequest = useAppSelector(selectBorrowRequestOfTeacher);

  const { handleRequestPost, handleRequestUpdate } = useActionRequest();

  const methods = useForm({
    defaultValues: defaultBorrowRequestAction,
  });

  const { handleSubmit, reset } = methods;

  const handleClose = () => {
    onClose();
    dispatch(borrowRequestActions.getBorrowRequestByTeacherWeekSuccess(null));
    reset(defaultBorrowRequestAction);
  };

  const handleSubmitData = (data) => {
    if (borrowRequest) {
      handleRequestUpdate({ data, dataOrigin: borrowRequest }, () => {
        handleClose();
        fetchCurrentData?.();
      });
    } else {
      handleRequestPost(data, () => {
        handleClose();
        fetchCurrentData?.();
      });
    }
  };

  useEffect(() => {
    dispatch(systemActions.getTeacherComboList());
    dispatch(systemActions.getRoomList());
    dispatch(systemActions.getSubjectList());
    dispatch(systemActions.getSchoolWeekList({}));
    dispatch(systemActions.getPeriodList());
    dispatch(systemActions.getClassList());
  }, []);

  return (
    <FormProvider {...methods}>
      <AppModal
        onClose={handleClose}
        onSubmit={handleSubmit(handleSubmitData)}
        component="form"
        modalTitleProps={{
          title: "Đăng ký mượn",
        }}
        fullScreen
        modalContentProps={{
          sx: {
            pt: 1,
            pb: 0,
            display: "flex",
            flexDirection: "column",
          },
          content: (
            <RegisterForm
              defaultDelayedTab={defaultDelayedTab}
              isEdit={Boolean(borrowRequest)}
            />
          ),
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleClose}
              >
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
        {...otherProps}
      />
      <EditController />
    </FormProvider>
  );
};

type CreateModalProps = AppModalProps & {
  fetchCurrentData?: () => void;
  defaultDelayedTab?: number;
};

export default memo(CreateModal);

export const defaultBorrowRequestAction: IBorrowRequestAction = {
  borrowFromDate: new Date(),
  borrowToDate: new Date(),
  borrowType: BORROW_TYPE.longTerm,
  teacherId: null,
  teacherName: "",
  schoolWeekConfigId: null,
  schoolWeekConfigName: "",

  borrowRequestDevices: [],
  borrowRequestRooms: [],

  deleteBorrowRequestDeviceIds: [],
  deleteBorrowRequestRoomIds: [],
};

const EditController = memo((props: {}) => {
  const dispatch = useAppDispatch();
  const { setValue, control } = useFormContext<IBorrowRequestAction>();
  const [teacherId, schoolWeekConfigId, borrowType] = useWatch({
    control,
    name: ["teacherId", "schoolWeekConfigId", "borrowType"],
  });

  const borrowRequest = useAppSelector(selectBorrowRequestOfTeacher);
  useInitBorrowRequestForm({
    modalData: borrowRequest as IBorrowRequest,
    setValue,
  });

  useEffect(() => {
    if (
      teacherId?.id &&
      schoolWeekConfigId?.id &&
      Number(borrowType) === BORROW_TYPE.week
    ) {
      dispatch(
        borrowRequestActions.getBorrowRequestByTeacherWeek({
          schoolWeekConfigId: schoolWeekConfigId.id as number,
          teacherId: teacherId.id as number,
        })
      );
    }
  }, [teacherId?.id, schoolWeekConfigId?.id]);

  return null;
});
