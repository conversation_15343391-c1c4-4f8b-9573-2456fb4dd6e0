import { AppFormTextField, AppTextField } from "@/components/common";
import { PlusIcon } from "@/components/icons";
import { <PERSON><PERSON>, Input<PERSON><PERSON><PERSON>, Stack } from "@mui/material";
import React, { memo, useEffect, useMemo, useState } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import { IDeviceTransactionUI } from "../../../../equipmentDocument.model";
import { useAppSelector } from "@/redux/hook";
import { selectIsPendingBuildDevice } from "../../../../equipmentDocument.slice";
import { MANAGE_BY } from "@/models/system.model";
import { getMaxCodeIndex } from "../../../../helper";

const AddDeviceAction = ({ onAddDevice }) => {
  const { control } = useFormContext<IDeviceTransactionUI>();
  const isPending = useAppSelector(selectIsPendingBuildDevice);
  const manageBy = useWatch({ control, name: "manageBy" });
  const devices = useWatch({ control, name: "devices" });
  const maxIndexItem = useWatch({ control, name: "maxIndexItem" });

  const [value, setValue] = useState("1");

  const minRegister = useMemo(() => {
    const maxIndexOfDevice = getMaxCodeIndex(devices);

    return Math.max(maxIndexOfDevice + 1, Number(maxIndexItem));
  }, [devices, maxIndexItem]);

  useEffect(() => {
    if (Number(minRegister) > Number(value)) {
      setValue(minRegister.toString());
    }
  }, [maxIndexItem]);

  return (
    <Stack direction="row" spacing={1} alignItems="center">
      {manageBy === MANAGE_BY.isManageDevice && (
        <Stack alignItems="center" direction="row">
          <InputLabel
            sx={{
              minWidth: 145,
            }}
          >
            Số đăng ký thiết bị từ:
          </InputLabel>
          <AppTextField
            onChange={(e) => setValue(e.target.value)}
            value={value}
            slotProps={{
              htmlInput: {
                min: minRegister,
              },
            }}
            type="number"
          />
        </Stack>
      )}
      <AppFormTextField
        label="Số dòng:"
        textfieldProps={{
          type: "number",
          size: "small",
          slotProps: {
            htmlInput: {
              min: 1,
              max: 500,
            },
          },
        }}
        direction="row"
        labelProps={{
          sx: {
            minWidth: 60,
          },
        }}
        control={control}
        name="totalAdd"
      />
      <Button
        variant="contained"
        size="small"
        startIcon={<PlusIcon />}
        disabled={isPending}
        onClick={() => onAddDevice(value, setValue)}
      >
        Thêm thiết bị
      </Button>
    </Stack>
  );
};

export const MAX_REGISTER = 9999;

export default memo(AddDeviceAction);
