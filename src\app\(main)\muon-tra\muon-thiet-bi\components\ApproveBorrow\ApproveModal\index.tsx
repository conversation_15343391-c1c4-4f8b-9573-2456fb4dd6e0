import { AppModal } from "@/components/common";
import { But<PERSON> } from "@mui/material";
import { memo, useEffect, useState } from "react";
import ApproveTable from "./ApproveTable";
import { IBorrowDeviceModal } from "../../../borrowDevice.modal";
import useApproveBorrow from "../../../hooks/useApproveBorrow";
import { systemActions } from "@/redux/system.slice";
import { useAppDispatch } from "@/redux/hook";

const ApproveModal = ({
  isOpen,
  onClose,
  selectedRows = [],
  borrowType,
  fetchCurrentData,
  onClearSelectedRows,
}: {
  isOpen: boolean;
  onClose: () => void;
  selectedRows?: IBorrowDeviceModal[];
  borrowType?: number;
  fetchCurrentData?: () => void;
  onClearSelectedRows?: () => void;
}) => {
  const [approveTableSelectedRows, setApproveTableSelectedRows] = useState<
    IBorrowDeviceModal[]
  >([]);
  const { approveBorrowDevices, isLoading } = useApproveBorrow();
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(systemActions.getTeacherComboList());
  }, [dispatch]);

  const handleClose = () => {
    setApproveTableSelectedRows([]);
    onClose();
  };

  const handleSubmit = async () => {
    await approveBorrowDevices(approveTableSelectedRows, () => {
      onClearSelectedRows?.();
      onClose();
      fetchCurrentData?.();
    });
  };

  return (
    <AppModal
      isOpen={isOpen}
      onClose={handleClose}
      modalTitleProps={{
        title: "Ghi mượn",
      }}
      fullWidth
      maxWidth="lg"
      modalContentProps={{
        sx: {
          pt: 0,
          pb: 0,
          display: "flex",
          flexDirection: "column",
        },
        content: (
          <ApproveTable
            selectedRows={selectedRows}
            borrowType={borrowType}
            onSelectedRowsChange={setApproveTableSelectedRows}
            approveTableSelectedRows={approveTableSelectedRows}
          />
        ),
      }}
      modalActionsProps={{
        children: (
          <>
            <Button variant="outlined" color="secondary" onClick={handleClose}>
              Đóng
            </Button>
            <Button
              type="submit"
              variant="contained"
              onClick={handleSubmit}
              disabled={isLoading || approveTableSelectedRows.length === 0}
            >
              {isLoading ? "Đang xử lý..." : "Lưu"}
            </Button>
          </>
        ),
      }}
      sx={{
        "& .MuiDialog-paper": {
          overflow: "hidden",
          height: "100%",
        },
      }}
    />
  );
};

export default memo(ApproveModal);
