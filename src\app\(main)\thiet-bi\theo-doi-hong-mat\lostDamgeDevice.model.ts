export interface ILostDamageDevice {
  id: number;
  createdBy: number;
  updatedBy: number;
  createdAt: string;
  updatedAt: string;
  deviceId: number;
  totalBroken: number;
  totalLost: number;
  totalFixed: number;
  notes: string;
  reportedDate: string;
  roomId: number;
  roomName: string;
  deviceCode: string;
  deviceName?: string;
  deviceTotalBroken: number;
  deviceTotalLost: number;
  deviceTotalAvailable: number;
  deviceUnitId: number;
  deviceUnitName: string;
  schoolDeviceTypeId: number;
  schoolDeviceTypeName: string;
  deviceDTITypeId: number;
  deviceDTITypeName: string;
  schoolSubjectId: number;
  schoolSubjectName: string;
  inventoryTransactionId: number;
  inventoryTransactionName: string;
  inventoryTransactionNumber: string;
  inventoryTransactionItemId: number;
}

export interface IDeviceItem {
  id: number;
  code: string;
  deviceTransactionId: number;
  deviceDefinitionId: number;
  roomId: number;
  roomName: string;
  teacherId: number;
  teacherName: string;
  quantity: number;
  price: number;
  totalPrices: number;
  countryId: number;
  countryName: string;
  entryDate: string;
  serial: string;
  expireDate: string;
  totalBroken: number;
  totalLost: number;
  totalAvailable: number;
  statisticCode: string;
  deviceCode: string;
  deviceName: string;
  deviceUnitId: number;
  deviceUnitName: string;
  schoolDeviceTypeId: number;
  schoolDeviceTypeName: string;
  deviceDTITypeId: number;
  deviceDTITypeName: string;
  schoolSubjectId: number;
  schoolSubjectName: string;
  gradeCodes: number[];
  gradeCode: string;
  userType: string;
  userTypes: number[];
  documentNumber: string;
  documentDate: string;
  deviceTransactionItemId: number;

  transactionTotalBroken: number;
  transactionTotalLost: number;
  transactionTotalAvailable: number;
}

export interface IFormData {
  deviceId: string | number;
  reportedDate: string;
  totalBroken: string | number;
  totalLost: string | number;
  totalAvailable: string | number;
  roomName: string;
  notes: string;
}

export interface IAddLostDamageDeviceAction {
  data: IFormData;
  onSuccess?: () => void;
}

export interface LostDamageDeviceFormData {
  inventoryTransactionId: number;
  inventoryTransactionName: string;
  inventoryTransactionNumber: string;
  deviceId: number;
  deviceName: string;
  reportedDate: string;
  totalBroken: number;
  totalLost: number;
  totalFixed: number;
  notes: string;
  deviceTotalAvailable: number;
}

export interface LostDamageDeviceData extends LostDamageDeviceFormData {
  id?: string | number;
}

export interface EditLostDamageDeviceModalActionProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  data?: LostDamageDeviceData;
}
