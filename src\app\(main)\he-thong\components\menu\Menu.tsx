"use client";

import React, { memo, useState } from "react";
import {
  Box,
  Paper,
  ToggleButton,
  ToggleButtonGroup,
  Stack,
  Grid,
} from "@mui/material";
import { ViewMode } from "./index";
import MenuGroup from "./MenuGroup";
import MenuList from "./MenuList";
import { GridViewIcon, ListViewIcon } from "../icons";
import { useAppSelector } from "@/redux/hook";
import { selectSystemMenuData } from "@/redux/app.slice";

const Menu: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>(ViewMode.GROUP);

  const systemMenuData = useAppSelector(selectSystemMenuData);

  const handleViewModeChange = (
    _: React.MouseEvent<HTMLElement>,
    newViewMode: ViewMode
  ) => {
    setViewMode(newViewMode);
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        width: "100%",
        height: "100%",
        backgroundColor: "background.grey",
      }}
    >
      <Paper
        elevation={1}
        sx={{
          px: 3,
          py: 0.5,
          borderRadius: 0,
          borderBottom: "1px solid",
          borderColor: "border.main",
          justifyContent: "end",
          boxShadow: "unset",
          alignItems: "center",
          display: "flex",
        }}
      >
        <ToggleButtonGroup
          value={viewMode}
          exclusive
          onChange={handleViewModeChange}
          aria-label="view mode"
          size="medium"
          sx={{
            fontSize: 18,
            "& .MuiToggleButton-root": {
              border: "1px solid",
              borderColor: "divider",
              "&.Mui-selected": {
                backgroundColor: "primary.main",
                color: "white",
                "&:hover": {
                  backgroundColor: "primary.dark",
                },
              },
            },
          }}
        >
          <ToggleButton
            sx={{
              p: 1,
            }}
            value={ViewMode.GROUP}
            aria-label="grid view"
          >
            <GridViewIcon sx={{ fontSize: 18 }} />
          </ToggleButton>
          <ToggleButton
            sx={{
              p: 0.9,
            }}
            value={ViewMode.LIST}
            aria-label="list view"
          >
            <ListViewIcon sx={{ fontSize: 18 }} />
          </ToggleButton>
        </ToggleButtonGroup>
      </Paper>
      <Box
        className="custom-scrollbar"
        sx={{
          overflowY: "auto",
          flex: 1,
          minHeight: 0,
          p: 3,
        }}
      >
        <Grid container rowSpacing={3} columnSpacing={2}>
          {viewMode === ViewMode.GROUP ? (
            <MenuGroup groups={systemMenuData} />
          ) : (
            <MenuList groups={systemMenuData} />
          )}
        </Grid>
      </Box>
    </Box>
  );
};

export default memo(Menu);
