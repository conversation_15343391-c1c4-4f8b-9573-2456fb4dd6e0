import { AppCheckbox, AppTable } from "@/components/common";
import { IPaginationModel } from "@/models/response.model";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { CommonUtils } from "@/utils";
import { ColumnDef } from "@tanstack/react-table";
import { useCallback, useEffect, useMemo } from "react";
import { ISchoolConfig } from "../../../schoolConfig.model";
import {
  schoolConfigActions,
  selectIsFetchingModal,
  selectMoetSchoolList,
  selectPagination,
  selectRenderTableList,
  selectSelectedList,
  selectTotalCountModal,
} from "../../../schoolConfig.slice";
import useConfigHeightOfTableInModal from "@/hooks/useConfigHeightOfTableInModal";

const AsyncCSDLTable = () => {
  const dispatch = useAppDispatch();
  const totalCount = useAppSelector(selectTotalCountModal);
  const isFetching = useAppSelector(selectIsFetchingModal);
  const pagination = useAppSelector(selectPagination);
  const tableList = useAppSelector(selectRenderTableList);

  const handleChangeCheckboxAll = useCallback((isCheckBoxAll) => {
    dispatch(schoolConfigActions.handleChangeSelectAllCheckbox(!isCheckBoxAll));
  }, []);

  const handleChangeCheckbox = useCallback((row) => {
    dispatch(schoolConfigActions.handleChangeSelectedCheckbox(row));
  }, []);

  const columns = getColumns({
    pagination,
    onChangeCheckboxAll: handleChangeCheckboxAll,
    onChangeCheckbox: handleChangeCheckbox,
  });

  useConfigHeightOfTableInModal();

  return (
    <AppTable
      data={tableList}
      totalData={totalCount}
      columns={columns}
      paginationData={pagination}
      onPageChange={(page) =>
        dispatch(schoolConfigActions.handleChangePage(page))
      }
      isFetching={isFetching}
      className="table-modal"
      boxProps={{
        sx: { mt: 1 },
      }}
    />
  );
};

export default AsyncCSDLTable;

type ColumnsProps = {
  pagination: IPaginationModel;
  onChangeCheckboxAll: (isCheckBoxAll: boolean) => void;
  onChangeCheckbox: (row: ISchoolConfig) => void;
};

const getColumns = ({
  pagination,
  onChangeCheckboxAll,
  onChangeCheckbox,
}: ColumnsProps): ColumnDef<ISchoolConfig>[] => [
  {
    id: "index",
    header: "STT",
    size: 50,
    cell: ({ row }) => {
      return row.index + 1 + pagination.skip;
    },
    meta: {
      align: "center",
    },
  },
  {
    id: "select",
    accessorKey: "select",
    meta: {
      align: "center",
    },
    size: 50,
    header: ({ table }) => (
      <AppCheckbox
        checked={table.getIsAllRowsSelected()}
        indeterminate={table.getIsSomeRowsSelected()}
        onChange={(e) => {
          table.getToggleAllRowsSelectedHandler()(e);
          onChangeCheckboxAll(table.getIsAllRowsSelected());
        }}
      />
    ),
    cell: ({ row }) => (
      <AppCheckbox
        checked={row.getIsSelected()}
        disabled={!row.getCanSelect()}
        indeterminate={row.getIsSomeSelected()}
        onChange={(e) => {
          row.getToggleSelectedHandler()(e);
          onChangeCheckbox(row.original);
        }}
      />
    ),
  },
  {
    id: "name",
    accessorKey: "name",
    header: "Tên trường",
    size: 250,
    cell: ({ row }) => (
      <>{`${row.original.name} (${row.original.schoolCode})`}</>
    ),
  },
  {
    id: "divisionName",
    accessorKey: "divisionName",
    header: "Phòng",
    size: 150,
  },
  {
    id: "doetName",
    accessorKey: "doetName",
    header: "Sở",
    size: 150,
  },
];
