"use client";

import AppCheckbox from "@/components/common/AppCheckbox";
import AppSearchDebounceTextFiled from "@/components/common/AppSearchDebounceTextFiled";
import { ColumnVisibilityIcon } from "@/components/icons";
import {
  Box,
  Button,
  Divider,
  FormControlLabel,
  Popover,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import { memo, useCallback, useState } from "react";
import { VisibilityState } from "@tanstack/react-table";
import { useColumnToggle, IColumnVisible } from "./useColumnToggle";
import IconButtonCustom from "../IconButtonCustom";

type ColumnToggleButtonProps = {
  idKey: string;
  columns: IColumnVisible[];
  columnVisibleDefault: VisibilityState;
  onChangeColumnVisibility: (state: VisibilityState) => void;
};

const ColumnToggleButton = ({
  columns,
  columnVisibleDefault,
  onChangeColumnVisibility,
}: ColumnToggleButtonProps) => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  const {
    ids,
    searchKey,
    setSearchKey,
    columnSearched,
    handleReset,
    toggleId,
    toggleAll,
  } = useColumnToggle({
    columns,
    columnVisibleDefault,
    onChangeColumnVisibility,
  });

  const handleOpen = useCallback(
    (event: React.MouseEvent<HTMLButtonElement>) =>
      setAnchorEl(event.currentTarget),
    []
  );

  const handleClose = useCallback(() => {
    setAnchorEl(null);
    setTimeout(() => setSearchKey(""), 100);
  }, [setSearchKey]);

  return (
    <>
      <Tooltip title="Cấu hình hiển thị cột" arrow>
        <IconButtonCustom
          onClick={handleOpen}
          aria-label="Cấu hình hiển thị cột"
        >
          <ColumnVisibilityIcon sx={{ fontSize: 20 }} />
        </IconButtonCustom>
      </Tooltip>
      <Popover
        open={!!anchorEl}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        transformOrigin={{ vertical: "top", horizontal: "left" }}
      >
        <Stack sx={{ pt: 3, width: 300 }}>
          <Box px={2.5}>
            <AppSearchDebounceTextFiled
              valueInput={searchKey}
              placeholder="Tìm kiếm"
              sx={{ mb: 1.5 }}
              onChangeValue={setSearchKey}
            />
          </Box>
          <Stack
            className="custom-scrollbar"
            sx={{ maxHeight: 500, overflowY: "auto" }}
          >
            {columnSearched.length ? (
              columnSearched.map((item) => (
                <FormControlLabel
                  key={item.id}
                  value={item.id}
                  checked={ids.includes(item.id)}
                  control={<AppCheckbox sx={{ width: 32, height: 32 }} />}
                  label={item.name}
                  onChange={(e, checked) => toggleId(item.id, checked)}
                  sx={{
                    px: 3,
                    "&:hover": { color: "primary.main" },
                  }}
                />
              ))
            ) : (
              <Typography textAlign="center" my={2}>
                Không có cột!
              </Typography>
            )}
          </Stack>
          <Divider sx={{ mt: 1 }} />
          <Stack direction="row" justifyContent="space-between" px={3} py={1}>
            <FormControlLabel
              value="all"
              checked={ids.length === columns.length}
              control={<AppCheckbox sx={{ width: 32, height: 32 }} />}
              label="Ẩn/Hiện tất cả"
              onChange={(_, checked) => toggleAll(checked)}
              sx={{ "&:hover": { color: "primary.main" } }}
            />
            <Button onClick={() => handleReset()}>Đặt lại</Button>
          </Stack>
        </Stack>
      </Popover>
    </>
  );
};

export default memo(ColumnToggleButton);
