import {
  getDeviceTransferService,
  getHistoryDeviceService,
} from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.service";
import { deviceActions } from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.slice";
import {
  IDeviceTransfer,
  IDeviceTransferParams,
  IHistoryDevice,
} from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/type";
import { ApiConstant, EnvConstant } from "@/constant";
import { IDevice } from "@/models/eduDevice.model";
import {
  DataListResponseModel,
  DataResponseModel,
  IPaginationModel,
} from "@/models/response.model";
import { getDeviceService } from "@/services/eduDevice.service";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import { PayloadAction } from "@reduxjs/toolkit";
import { call, put, takeLatest } from "redux-saga/effects";
import { toast } from "sonner";

export function* getDevicesSaga(action: PayloadAction<any>) {
  try {
    toggleAppProgress(true);
    const response: DataListResponseModel<IDevice> = yield call(
      getDeviceService,
      action.payload
    );
    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(deviceActions.setTransferDevice(response.data.data));
    } else {
      throw response;
    }
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

export function* getDeviceTransferSaga(
  action: PayloadAction<IDeviceTransferParams & Partial<IPaginationModel>>
) {
  try {
    toggleAppProgress(true);
    const response: DataListResponseModel<IDeviceTransfer> = yield call(
      getDeviceTransferService,
      action.payload
    );
    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(deviceActions.getDeviceTransferSuccess(response.data));
    } else {
      throw response;
    }
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

export function* getHistoryDeviceSaga(
  action: PayloadAction<{
    id: number;
    params;
  }>
) {
  try {
    toggleAppProgress(true);
    const response: DataResponseModel<IHistoryDevice> = yield call(
      getHistoryDeviceService,
      action.payload.id,
      action.payload.params
    );
    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(deviceActions.getHistoryDeviceSuccess(response.data));
    } else {
      throw response;
    }
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

export function* deviceSaga() {
  yield takeLatest(deviceActions.getHistoryDevice.type, getHistoryDeviceSaga);
  yield takeLatest(deviceActions.getDeviceCombo.type, getDevicesSaga);
  yield takeLatest(deviceActions.getDeviceTransfer.type, getDeviceTransferSaga);
}
