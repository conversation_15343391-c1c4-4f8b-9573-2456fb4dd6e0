# 🌟 Stage 1: Build ứng dụng Next.js
FROM node:20 AS builder

# Thiết lập thư mục làm việc
WORKDIR /app

# Copy file khai báo trước để cache tốt hơn
COPY package.json yarn.lock ./

# Cài đặt dependency
RUN yarn --frozen-lockfile

# Thiết lập biến môi trường build
ARG NEXT_ENVIRONMENT
ENV NODE_ENV=production
ENV NEXT_PUBLIC_ENV=$NEXT_ENVIRONMENT
ENV APP_ENV=$NEXT_ENVIRONMENT

# Copy toàn bộ mã nguồn + file môi trường tương ứng
COPY . .

# Copy file .env phù hợp theo biến staging/local/prod
COPY .env.${NEXT_ENVIRONMENT} .env.production

# Build ứng dụng
RUN yarn build

# 🌟 Stage 2: Runtime chạy app nhẹ hơn
FROM node:20-alpine AS runtime

WORKDIR /app

# Thiết lập biến môi trường runtime
ENV NODE_ENV=production
ENV NEXT_PUBLIC_ENV=staging
ENV APP_ENV=staging

# Cài only production deps
COPY --from=builder /app/node_modules ./node_modules

# Copy file build và public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json

# Expose port nếu cần
EXPOSE 3000

# Khởi động app
CMD ["yarn", "start"]
