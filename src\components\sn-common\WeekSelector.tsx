import { memo, useEffect, useState } from "react";
import { Box, IconButton } from "@mui/material";
import AppAutoComplete from "@/components/common/AppAutoComplete";
import { useAppSelector } from "@/redux/hook";
import { ArrowIcon } from "@/components/icons";
import dayjs from "dayjs";
import { selectSchoolWeekList } from "@/redux/system.slice";
import { DATE_FORMAT } from "@/constant/app.const";

interface WeekSelectorProps {
  onDateChange: (fromDate: Date, toDate: Date) => void;
  fromDate?: string;
  toDate?: string;
}

const WeekSelector = ({
  onDateChange,
  fromDate,
  toDate,
}: WeekSelectorProps) => {
  const [selectedWeek, setSelectedWeek] = useState<any>(null);
  const schoolWeek = useAppSelector(selectSchoolWeekList);

  useEffect(() => {
    if (schoolWeek.length > 0 && fromDate && toDate) {
      const matchingWeek = schoolWeek.find((week) => {
        return (
          dayjs(week.fromDate).format(DATE_FORMAT) ===
            dayjs(fromDate).format(DATE_FORMAT) &&
          dayjs(week.toDate).format(DATE_FORMAT) ===
            dayjs(toDate).format(DATE_FORMAT)
        );
      });

      if (matchingWeek && matchingWeek.id !== selectedWeek?.id) {
        setSelectedWeek(matchingWeek);
      }
    }
  }, [fromDate, toDate, schoolWeek]);

  const handleChangeWeek = (direction: "prev" | "next") => {
    if (!selectedWeek) return;

    const currentIndex = schoolWeek.findIndex((w) => w.id === selectedWeek.id);
    if (currentIndex === -1) return;

    const nextIndex =
      direction === "prev"
        ? (currentIndex - 1 + schoolWeek.length) % schoolWeek.length
        : (currentIndex + 1) % schoolWeek.length;

    const nextWeek = schoolWeek[nextIndex];
    setSelectedWeek(nextWeek);

    if (onDateChange) {
      const fromDate = dayjs(nextWeek.fromDate).toDate();
      const toDate = dayjs(nextWeek.toDate).toDate();
      onDateChange(fromDate, toDate);
    }
  };

  const handleWeekChange = (_, week: any) => {
    setSelectedWeek(week);

    if (week && onDateChange) {
      onDateChange(week.fromDate, week.toDate);
    }
  };

  return (
    <>
      <IconButton
        aria-label="Quay lại"
        onClick={() => handleChangeWeek("prev")}
      >
        <ArrowIcon />
      </IconButton>
      <AppAutoComplete
        value={selectedWeek}
        options={schoolWeek}
        onChange={handleWeekChange}
        disableClearable
        textFieldProps={{
          label: "Tuần",
        }}
      />
      <IconButton aria-label="Kế tiếp" onClick={() => handleChangeWeek("next")}>
        <ArrowIcon
          sx={{
            transform: "rotate(180deg)",
          }}
        />
      </IconButton>
    </>
  );
};

export default memo(WeekSelector);
