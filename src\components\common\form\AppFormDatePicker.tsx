import React, { memo } from "react";
import { InputLabel, InputLabelProps, Stack, StackProps } from "@mui/material";
import {
  Control,
  Controller,
  ControllerProps,
  FieldPath,
  FieldValues,
  Path,
  RegisterOptions,
} from "react-hook-form";
import { DatePickerProps } from "@mui/x-date-pickers";
import dayjs from "dayjs";
import AppTextField from "../AppTextField";
import AppDatePicker from "../AppDatePicker";

const AppFormDatePicker = <T extends FieldValues>({
  label,
  control,
  name,
  rules,
  controlProps,
  labelProps,
  datePickerProps,
  onChangeValueForm,
  direction = "column",
  ...otherProps
}: AppFormDatePickerProps<T>) => {
  return (
    <Stack
      spacing={0.5}
      alignItems={"center"}
      direction={direction}
      {...otherProps}
    >
      {Boolean(label) && (
        <InputLabel
          required={Boolean(rules?.required)}
          htmlFor={name}
          {...labelProps}
          sx={{
            width: direction === "row" ? "unset" : "100%",
            ...labelProps?.sx,
          }}
        >
          {label}
        </InputLabel>
      )}
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({ field }) => (
          <AppDatePicker
            {...field}
            maxDate={dayjs()}
            onChange={(val: any) => {
              field?.onChange?.(val);
              onChangeValueForm?.(val);
            }}
            {...datePickerProps}
          />
        )}
        {...controlProps}
      />
    </Stack>
  );
};

export type AppFormDatePickerProps<T extends FieldValues> = StackProps & {
  label?: string;
  control: Control<any, object>;
  name: FieldPath<T>;

  rules?:
    | Omit<
        RegisterOptions<any, Path<T>>,
        "disabled" | "valueAsNumber" | "valueAsDate" | "setValueAs"
      >
    | undefined;
  controlProps?: Omit<ControllerProps, "render" | "name" | "control">;
  labelProps?: InputLabelProps;
  datePickerProps?: DatePickerProps<any>;
  onChangeValueForm?: (value: any) => void;
};

export default memo(AppFormDatePicker);
