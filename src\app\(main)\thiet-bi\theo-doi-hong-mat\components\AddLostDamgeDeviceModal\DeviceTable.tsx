"use client";

import { TablePageLayout } from "@/components/common";
import { DEVICE_LIST } from "@/constant/api.const";
import { ColumnDef } from "@tanstack/react-table";
import React, { memo, useEffect } from "react";
import { IDeviceItem } from "../../lostDamgeDevice.model";
import { Button, Box } from "@mui/material";
import { formatNumber } from "@/utils/format.utils";

interface DeviceTableProps {
  onSelectDevice?: (device: IDeviceItem) => void;
}

const DeviceTable = ({ onSelectDevice }: DeviceTableProps) => {
  const columns = getColumns(onSelectDevice);

  return (
    <TablePageLayout<IDeviceItem>
      apiUrl={DEVICE_LIST}
      tableProps={{
        columns,
        tableContainerProps: {
          sx: {
            height: "400px",
          },
        },
      }}
      filterConfig={[
        {
          key: "searchKey",
          type: "text",
          label: "<PERSON><PERSON><PERSON> kiếm",
          size: 4,
        },
        {
          key: "isAvailable",
          value: "true",
        },
      ]}
    />
  );
};

export default memo(DeviceTable);

const getColumns = (
  onSelectDevice?: (device: IDeviceItem) => void
): ColumnDef<IDeviceItem>[] => [
  {
    id: "select",
    header: "",
    size: 60,
    meta: { align: "center" },
    cell: ({ row }) => (
      <Button
        variant="contained"
        size="small"
        onClick={() => onSelectDevice?.(row.original)}
      >
        Chọn
      </Button>
    ),
  },
  {
    id: "code",
    header: "Mã thiết bị",
    accessorKey: "code",
    size: 40,
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
    size: 150,
  },
  {
    id: "roomName",
    accessorKey: "roomName",
    header: "Kho/phòng",
    size: 60,
  },
  {
    id: "schoolSubjectName",
    accessorKey: "schoolSubjectName",
    header: "Môn học",
    size: 60,
  },
  {
    id: "totalAvailable",
    accessorKey: "totalAvailable",
    header: "Số lượng",
    size: 40,
    meta: { align: "right" },
    cell: ({ row }) => formatNumber(row.original.totalAvailable),
  },
];
