"use client";

import { TablePageLayout } from "@/components/common";
import StatusCell from "@/components/common/table/cell/StatusCell";
import {
  FormFieldConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import { CheckIcon } from "@/components/icons";
import { DataConstant } from "@/constant";
import {
  FUNCTIONAL_ROOM_TYPE,
  ROOM,
  STATUS_ROOM,
  SUBJECT,
  TEACHER_COMBO,
} from "@/constant/api.const";
import { CLASSIFICATION_LIST, IRoom, IRoomAction } from "@/models/system.model";
import { updateStatusService } from "@/services/app.service";
import { getLabelCellFromList } from "@/utils/common.utils";
import { formatNumber } from "@/utils/format.utils";
import { ColumnDef } from "@tanstack/react-table";
import React, { useRef } from "react";

const RoomPage = () => {
  const tableRef = useRef<ITableRef>(null);
  const columns = getColumns(tableRef);

  const cleanDataFormFiled = (data: IRoomAction) => {
    const newData = {
      ...data,
      subjectIds: data.subjects,
      teacherIds: data.teachers,
    };
    delete newData.subjects;
    delete newData.teachers;
    if (!data.isFunctionalClassroom) {
      newData.isInternetConnection = 0;
      newData.functionalClassroomTypeId = 0;
      newData.area = 0;
    }
    return newData;
  };

  return (
    <TablePageLayout<IRoom>
      ref={tableRef}
      visibleCol={VISIBLE_COL}
      cleanDataFormFiled={cleanDataFormFiled}
      apiUrl={ROOM}
      tableProps={{
        columns,
      }}
      filterConfig={[
        {
          key: "searchKey",
          type: "text",
          label: "Tìm kiếm",
          size: 2.4,
        },
      ]}
      actions={["create", "update", "delete"]}
      formConfig={{
        modalProps: {
          maxWidth: "sm",
          fullWidth: true,
        },
        deleteUrl: ROOM,
        detailUrl: ROOM,
        createUrl: ROOM,
        updateUrl: ROOM,
        createFields: CREATE_CONFIG,
        updateFields: CREATE_CONFIG,
      }}
    />
  );
};

export default RoomPage;

const getColumns = (
  tableRef: React.RefObject<ITableRef | null>
): ColumnDef<IRoom>[] => [
  {
    id: "name",
    header: "Tên Kho/phòng",
    accessorKey: "name",
    size: 150,
  },
  {
    id: "subjectName",
    accessorKey: "subjectName",
    header: "Môn học",
    size: 150,
  },
  {
    id: "teacherName",
    header: "Cán bộ phụ trách",
    accessorKey: "teacherName",
  },
  {
    id: "classificationType",
    accessorKey: "classificationType",
    header: "Xếp loại phòng",
    size: 100,
    meta: {
      cellSx: {
        whiteSpace: "nowrap",
      },
    },
    cell: ({ row }) => {
      return getLabelCellFromList(
        row.original.classificationType,
        CLASSIFICATION_LIST
      );
    },
  },
  {
    id: "area",
    header: "Diện tích (m2)",
    accessorKey: "area",
    accessorFn: (row) => formatNumber(row.area),
    size: 60,
    meta: {
      align: "right",
    },
  },
  {
    id: "isFunctionalClassroom",
    header: "Phòng học chức năng",
    accessorKey: "isFunctionalClassroom",
    size: 60,
    meta: { align: "center" },
    cell: ({ row }) =>
      Boolean(row.original.isFunctionalClassroom) && (
        <CheckIcon sx={{ fontSize: 24, color: "primary.main" }} />
      ),
  },
  {
    id: "isInternetConnection",
    header: "Có kết nối mạng",
    accessorKey: "isInternetConnection",
    meta: { align: "center" },
    size: 60,

    cell: ({ row }) =>
      Boolean(row.original.isFunctionalClassroom) && (
        <CheckIcon sx={{ fontSize: 24, color: "primary.main" }} />
      ),
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Hiển thị",
    size: 60,
    cell: ({ row }) => (
      <StatusCell
        status={row.original.status}
        onStatusChange={(_, value) => {
          updateStatusService({
            id: row.original.id,
            status: Number(value),
            onSuccess: tableRef?.current?.fetchCurrentData,
            url: STATUS_ROOM,
          });
        }}
      />
    ),
    meta: { align: "center" },
  },
];

const VISIBLE_COL = [
  {
    id: "name",
    name: "Tên Kho/phong",
  },
  {
    id: "subjectName",
    name: "Môn học",
  },
  {
    id: "teacherName",
    name: "Cán bộ phụ trách",
  },
  {
    id: "classificationType",
    name: "Xếp loại phòng",
  },
  {
    id: "area",
    name: "Diện tích (m2)",
  },
  {
    id: "isFunctionalClassroom",
    name: "Phòng học chức năng",
  },
  {
    id: "functionalClassroomTypeId",
    name: "Loại phòng",
  },
  {
    id: "isInternetConnection",
    name: "Có kết nối mạng",
  },
  {
    id: "status",
    name: "Hiển thị",
  },
];

const CREATE_CONFIG: FormFieldConfig<IRoomAction>[] = [
  {
    key: "name",
    type: "text",
    label: "Tên phòng",
    size: 8,
    rules: { required: "Tên phòng không được để trống" },
  },
  {
    key: "classificationType",
    type: "select",
    label: "Xếp loại",
    size: 4,
    rules: { required: "Xếp loại phòng không được để trống" },
    selectConfig: {
      options: CLASSIFICATION_LIST,
    },
  },
  {
    key: "subjects",
    type: "select",
    label: "Môn học",
    size: 12,
    apiListUrl: SUBJECT,
    selectConfig: {
      isMulti: true,
    },
  },
  {
    key: "teachers",
    type: "select",
    label: "Người dùng",
    size: 12,
    apiListUrl: TEACHER_COMBO,
    selectConfig: {
      isMulti: true,
    },
  },
  {
    key: "status",
    label: "Trạng thái hiển thị",
    type: "toggle",
    size: 12,
  },
  {
    key: "isFunctionalClassroom",
    label: "Là phòng học chức năng",
    type: "toggle",
    size: 6,
  },
  {
    key: "isInternetConnection",
    label: "Phòng học có kết nối mạng",
    type: "toggle",
    size: 6,
    displayIf: [
      { key: "isFunctionalClassroom", value: DataConstant.BOOLEAN_TYPE.true },
    ],
  },
  {
    key: "functionalClassroomTypeId",
    label: "Loại phòng chức năng",
    type: "select",
    apiListUrl: FUNCTIONAL_ROOM_TYPE,
    size: 6,
    displayIf: [
      { key: "isFunctionalClassroom", value: DataConstant.BOOLEAN_TYPE.true },
    ],
  },
  {
    key: "area",
    label: "Diện tích (m2)",
    type: "number",
    size: 6,
    displayIf: [
      { key: "isFunctionalClassroom", value: DataConstant.BOOLEAN_TYPE.true },
    ],
  },
];
