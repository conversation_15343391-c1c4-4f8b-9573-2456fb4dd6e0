import { PayloadAction } from "@reduxjs/toolkit";
import { call, put, takeLatest } from "redux-saga/effects";
import { DataResponseModel } from "@/models/response.model";
import { ApiConstant, EnvConstant } from "@/constant";

import { toast } from "sonner";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import http from "@/api";
import { borrowRequestActions } from "@/redux/device/borrowRequest.slice";
import { IBorrowRequest } from "@/models/eduDevice.model";
import {
  findBorrowRequestByWeekService,
  getBorrowRequestService,
} from "@/services/borrowRequest.service";

function* getBorrowRequestSaga(action: PayloadAction<number>) {
  try {
    toggleAppProgress(true);

    const response: DataResponseModel<IBorrowRequest> = yield call(
      getBorrowRequestService,
      action.payload
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(borrowRequestActions.getBorrowRequestSuccess(response.data));
      return;
    }

    throw response;
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

function* getBorrowRequestByTeacherWeekSaga(
  action: PayloadAction<{
    schoolWeekConfigId: number;
    teacherId: number;
  }>
) {
  try {
    toggleAppProgress(true);

    const response: DataResponseModel<IBorrowRequest> = yield call(
      findBorrowRequestByWeekService,
      action.payload
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        borrowRequestActions.getBorrowRequestByTeacherWeekSuccess(response.data)
      );
      return;
    }

    throw response;
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

export function* borrowRequestSaga() {
  yield takeLatest(
    borrowRequestActions.getBorrowRequest.type,
    getBorrowRequestSaga
  );
  yield takeLatest(
    borrowRequestActions.getBorrowRequestByTeacherWeek.type,
    getBorrowRequestByTeacherWeekSaga
  );
}
