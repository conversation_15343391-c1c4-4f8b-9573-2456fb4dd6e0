import { AppModal } from "@/components/common";
import { useCallback } from "react";
import { Button } from "@mui/material";
import { FormProvider, useForm } from "react-hook-form";
import CreateForm from "./CreateForm";
import { useAppDispatch } from "@/redux/hook";
import { addLostDamageDevice } from "../../redux/lostDamageDevice.slice";
import { toast } from "sonner";
import { IFormData } from "../../lostDamgeDevice.model";

interface CreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  fetchCurrentData?: () => void;
}

const INIT_VALUE = {
  deviceId: "",
  deviceName: "",
  reportedDate: "",
  totalBroken: 0,
  totalLost: 0,
  totalAvailable: 0,
  notes: "",
  roomName: "",
};

const CreateModal = ({
  isOpen,
  onClose,
  fetchCurrentData,
}: CreateModalProps) => {
  const dispatch = useAppDispatch();

  const methods = useForm<IFormData>({
    defaultValues: INIT_VALUE,
  });

  const {
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = methods;

  const handleClose = useCallback(() => {
    reset(INIT_VALUE);
    onClose();
  }, [onClose, reset]);

  const handleSubmitData = useCallback(
    (data: IFormData) => {
      const totalSum = Number(data.totalBroken) + Number(data.totalLost);
      const totalAvailable = Number(data.totalAvailable);

      if (totalSum > totalAvailable) {
        toast.error(
          `Tổng số lượng hỏng và mất (${totalSum}) không được vượt quá số lượng thiết bị (${totalAvailable})`
        );
        return;
      }

      dispatch(
        addLostDamageDevice({
          data,
          onSuccess: () => {
            fetchCurrentData?.();
            handleClose();
          },
        })
      );
    },
    [dispatch, fetchCurrentData, handleClose]
  );

  return (
    <FormProvider {...methods}>
      <AppModal
        isOpen={isOpen}
        onClose={handleClose}
        component="form"
        onSubmit={handleSubmit(handleSubmitData)}
        modalTitleProps={{
          title: "Thêm thiết bị hỏng/mất",
        }}
        modalContentProps={{
          content: <CreateForm />,
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                variant="outlined"
                onClick={handleClose}
                color="secondary"
              >
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
      />
    </FormProvider>
  );
};

export default CreateModal;
