"use client";

import React, { forwardRef, memo } from "react";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker, DatePickerProps } from "@mui/x-date-pickers/DatePicker";
import "dayjs/locale/vi";
import AppTextField from "./AppTextField";
import dayjs from "dayjs";
import { CalendarIcon } from "../icons";

const AppDatePicker = forwardRef<HTMLInputElement, DatePickerProps<any>>(
  (props: DatePickerProps<any>, ref) => {
    return (
      <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale={"vi"}>
        <DatePicker
          dayOfWeekFormatter={(day) => weekDays[new Date(day).getDay()]}
          inputRef={ref}
          slots={{
            textField: AppTextField,
            openPickerIcon: CalendarIcon,
            ...props?.slots,
          }}
          slotProps={{
            ...props?.slotProps,
            textField: {
              fullWidth: true,
              ...props?.slotProps?.textField,
            },
          }}
          {...props}
          value={props.value ? dayjs(props.value) : null}
        />
      </LocalizationProvider>
    );
  }
);

AppDatePicker.displayName = "AppDatePicker";

export default memo(AppDatePicker);

const weekDays = ["CN", "T2", "T3", "T4", "T5", "T6", "T7"];
