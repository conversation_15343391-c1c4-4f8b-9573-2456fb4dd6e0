"use client";

import { AppAutoComplete } from "@/components/common";
import {
  DEFAULT_FILTER_SIZE,
  FilterCustomProps,
} from "@/components/common/TablePageLayout/ContentPage/HeaderFilter";
import TruongFilter from "@/components/sn-common/TruongFilter";
import { AppConstant, DataConstant } from "@/constant";
import {
  educationUnitsActions,
  selectPhongList,
  selectPhongValue,
  selectSoList,
  selectSoValue,
} from "@/redux/educationUnits.slice";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { CommonUtils } from "@/utils";
import { Grid } from "@mui/material";
import { memo, useCallback, useEffect, useMemo } from "react";

const FilterCustom = ({ props }: { props: FilterCustomProps }) => {
  const dispatch = useAppDispatch();
  const soList = useAppSelector(selectSoList);
  const phongList = useAppSelector(selectPhongList);
  const soValue = useAppSelector(selectSoValue);
  const phongValue = useAppSelector(selectPhongValue);

  const getFilterValue = useCallback(
    (key: string): string | undefined => {
      const raw = props?.filter?.find((item) => item.key === key)?.value;
      return typeof raw === "string"
        ? raw
        : raw !== undefined
        ? String(raw)
        : undefined;
    },
    [props?.filter]
  );

  const groupUnitCode = getFilterValue("groupUnitCode");
  const doetCode = getFilterValue("doetCode");
  const divisionCode = getFilterValue("divisionCode");

  const isTruong = useMemo(
    () => groupUnitCode === DataConstant.DON_VI_TYPE.truong,
    [groupUnitCode]
  );

  useEffect(() => {
    if (!isTruong) return;

    dispatch(educationUnitsActions.getSoList());
  }, [isTruong]);

  const handleDoetChange = useCallback(
    async (_: any, data: any) => {
      if (data) {
        const doetCode = getCodeFromData(data, "code");
        props.onChangeFilter?.("doetCode")(doetCode);

        dispatch(
          educationUnitsActions.handleChangeValue({
            field: "soValue",
            value: data,
          })
        );

        dispatch(
          educationUnitsActions.getPhongList({
            doetCode,
          })
        );
        dispatch(
          educationUnitsActions.getTruongList({
            doetCode: data.code,
            ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
          })
        );
      } else {
        props.onChangeFilter?.("doetCode")(null);
        props.onChangeFilter?.("divisionCode")(null);
        props.onChangeFilter?.("schoolCode")(null);
        dispatch(
          educationUnitsActions.handleChangeValue({
            field: "soValue",
            value: null,
          })
        );
      }
    },
    [getCodeFromData, props.onChangeFilter]
  );

  const handlePhongChange = useCallback(
    async (_: any, data: any) => {
      if (data) {
        dispatch(
          educationUnitsActions.handleChangeValue({
            field: "phongValue",
            value: data,
          })
        );
        const divisionCode = getCodeFromData(data, "code");

        props.onChangeFilter?.("divisionCode")(divisionCode);
        dispatch(
          educationUnitsActions.getTruongList({
            doetCode,
            divisionCode: data?.code || undefined,
            ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
          })
        );
      } else {
        dispatch(
          educationUnitsActions.handleChangeValue({
            field: "phongValue",
            value: null,
          })
        );
        props.onChangeFilter?.("divisionCode")(null);
        props.onChangeFilter?.("schoolCode")(null);
      }
    },
    [getCodeFromData, getFilterValue, props.onChangeFilter]
  );

  const handleTruongChange = useCallback(
    (_: any, data: any) => {
      if (data) {
        props.onChangeFilter?.("schoolCode")(data.code);

        dispatch(
          educationUnitsActions.handleChangeValue({
            field: "truongValue",
            value: data,
          })
        );
      } else {
        props.onChangeFilter?.("schoolCode")(null);
        dispatch(
          educationUnitsActions.handleChangeValue({
            field: "truongValue",
            value: null,
          })
        );
      }
    },
    [getCodeFromData, props.onChangeFilter]
  );

  if (!isTruong) return null;

  return (
    <>
      <Grid size={DEFAULT_FILTER_SIZE}>
        <AppAutoComplete
          options={soList}
          label="Sở"
          onChange={handleDoetChange}
          value={soValue}
        />
      </Grid>

      <Grid size={DEFAULT_FILTER_SIZE}>
        <AppAutoComplete
          options={phongList}
          label="Phòng"
          onChange={handlePhongChange}
          value={phongValue}
        />
      </Grid>

      <Grid size={DEFAULT_FILTER_SIZE}>
        <TruongFilter
          groupUnitCode={groupUnitCode}
          divisionCode={divisionCode}
          doetCode={doetCode}
          onChangeTruong={handleTruongChange}
        />
      </Grid>
    </>
  );
};

export default memo(FilterCustom);

const getCodeFromData = (data: any, key: string, fallback: string = "") => {
  if (typeof data === "object" && data !== null && key in data)
    return data[key];
  if (typeof data === "string") return data;
  return fallback;
};
