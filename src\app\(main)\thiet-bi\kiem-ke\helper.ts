import { IDevice } from "@/models/eduDevice.model";
import {
  IInventoryItems,
  IInventoryTransactionItems,
  ITransactionTeam,
} from "./type";
import { ITeacher } from "@/models/system.model";

export const convertToInventoryTransactionItems = (
  devices: IDevice[]
): IInventoryTransactionItems[] => {
  return devices.map((item) => ({
    id: 0,
    changeBrokenTotal: 0,
    changeLostTotal: 0,
    changeAvailableTotal: 0,
    deviceCode: item.deviceCode,
    deviceName: item.deviceName,
    deviceDefinitionId: item.deviceDefinitionId,
    beforeBrokenTotal: item.totalBroken,
    beforeLostTotal: item.totalLost,
    beforeAvailableTotal: item.totalAvailable,
    afterBrokenTotal: item.totalBroken,
    afterLostTotal: item.totalLost,
    afterAvailableTotal: item.totalAvailable,
    afterChangeTotal: 0,
    deviceId: item.id,
    quantity: item.quantity,
    notes: "",
    roomId: item.roomId,
    roomName: item.roomName,
    deviceUnitId: item.deviceUnitId,
    deviceUnitName: item.deviceUnitName,
    schoolDeviceTypeId: item.schoolDeviceTypeId,
    schoolDeviceTypeName: item.schoolDeviceTypeName,
    deviceDTITypeId: item.deviceDTITypeId,
    deviceDTITypeName: item.deviceDTITypeName,
    schoolSubjectId: item.schoolSubjectId,
    schoolSubjectName: item.schoolSubjectName,
    isChange: false,
    isError: false,
  }));
};

export const convertToTransactionTeams = (
  teachers: ITeacher[]
): ITransactionTeam[] => {
  return teachers.map((item) => ({
    stt: 0,
    teacherCode: item.code,
    teacherName: item.lastName + " " + item.firstName,
    position: "",
    role: "",
    note: "",
    isTeamlead: false,
  }));
};

export const convertToInventoryItemPayload = (devices): IInventoryItems[] => {
  return devices.map((device) => ({
    deviceId: device.deviceId,
    id: device.id,
    deviceDefinitionId: device.deviceDefinitionId,
    quantity: device.quantity,
    beforeBrokenTotal: device.beforeBrokenTotal,
    beforeLostTotal: device.beforeLostTotal,
    beforeAvailableTotal: device.beforeAvailableTotal,
    afterBrokenTotal: device.afterBrokenTotal,
    afterLostTotal: device.afterLostTotal,
    afterAvailableTotal:
      device.quantity - device.afterBrokenTotal - device.afterLostTotal,
    afterChangeTotal: device.afterChangeTotal,
    changeBrokenTotal: device.afterBrokenTotal - device.beforeBrokenTotal,
    changeLostTotal: device.afterLostTotal - device.beforeLostTotal,
    notes: device.notes,
  }));
};
