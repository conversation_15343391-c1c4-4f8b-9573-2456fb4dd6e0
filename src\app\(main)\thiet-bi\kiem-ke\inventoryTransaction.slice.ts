import {
  convertToInventoryTransactionItems,
  convertToTransactionTeams,
} from "@/app/(main)/thiet-bi/kiem-ke/helper";
import {
  IInventoryDeviceParams,
  IInventoryItems,
  IInventoryTransactionItems,
  ITransactionActionTeam,
  ITransactionTeam,
} from "@/app/(main)/thiet-bi/kiem-ke/type";
import { IDevice, IDeviceParams } from "@/models/eduDevice.model";
import { IPaginationModel } from "@/models/response.model";
import { ITeacher } from "@/models/system.model";
import { rootReducer } from "@/redux/reducer";
import { RootState } from "@/redux/store";
import {
  createSelector,
  createSlice,
  PayloadAction,
  WithSlice,
} from "@reduxjs/toolkit";

export interface IInitialState {
  isFetching: boolean;
  selectedIds: number[];
  teacherIds: string[];
  chooseDevices: IDevice[];
  filterChooseDevices: IInventoryDeviceParams;
  totalChooseDevices: number;
  inventoryItems: IInventoryTransactionItems[];
  inventoryActionItems: IInventoryItems[];
  transactionTeams: ITransactionTeam[];
  transactionActionTeams: ITransactionActionTeam[];
  deleteInventoryTransactionItemIds: number[];
}

const DEFAULT_FILTER_CHOOSE_DEVICES: IInventoryDeviceParams = {
  searchKey: "",
  roomIds: [],
  schoolSubjectIds: [],
};

export const initialState: IInitialState = {
  isFetching: false,
  selectedIds: [],
  teacherIds: [],
  chooseDevices: [],
  filterChooseDevices: DEFAULT_FILTER_CHOOSE_DEVICES,
  totalChooseDevices: 0,
  inventoryItems: [],
  inventoryActionItems: [],
  transactionTeams: [],
  transactionActionTeams: [],
  deleteInventoryTransactionItemIds: [],
};

export const selectorInventoryActionItems = (id: number) =>
  createSelector(
    [
      (state: RootState) =>
        state.inventoryTransaction?.inventoryActionItems ?? [],
    ],
    (inventoryActionItems) =>
      inventoryActionItems.find((item) => item.deviceId === id)
  );

export const selectorTransactionActionTeams = (code: string) =>
  createSelector(
    [
      (state: RootState) =>
        state.inventoryTransaction?.transactionActionTeams ?? [],
    ],
    (transactionActionTeams) =>
      transactionActionTeams.find((item) => item.teacherCode === code)
  );

const selectors = {
  inventoryItems: (state: IInitialState) => state.inventoryItems,
  inventoryActionItems: (state: IInitialState) => state.inventoryActionItems,
  transactionTeams: (state: IInitialState) => state.transactionTeams,
  transactionActionTeams: (state: IInitialState) =>
    state.transactionActionTeams,
  deleteInventoryTransactionItemIds: (state: IInitialState) =>
    state.deleteInventoryTransactionItemIds,
  chooseDevices: (state: IInitialState) => state.chooseDevices,
  totalChooseDevices: (state: IInitialState) => state.totalChooseDevices,
  selectedIds: (state: IInitialState) => state.selectedIds,
  teacherIds: (state: IInitialState) => state.teacherIds,
  filterChooseDevices: (state: IInitialState) => state.filterChooseDevices,
};

const reducers = {
  getChooseDevice: (
    state: IInitialState,
    action: PayloadAction<IDeviceParams & IPaginationModel>
  ) => {
    state.isFetching = true;
  },
  addChooseDevices: (state: IInitialState) => {
    const chooseNews = convertToInventoryTransactionItems(
      state.chooseDevices.filter((item) => state.selectedIds.includes(item.id))
    );
    state.inventoryActionItems = [
      ...state.inventoryActionItems,
      ...chooseNews.map((item) => ({
        ...item,
        id: 0,
      })),
    ];
    state.inventoryItems = [...state.inventoryItems, ...chooseNews];
    state.selectedIds = [];
  },
  setChooseDevices: (
    state: IInitialState,
    action: PayloadAction<{ data: IDevice[]; totalCount: number }>
  ) => {
    state.isFetching = false;
    state.chooseDevices = action.payload.data;
    state.totalChooseDevices = action.payload.totalCount;
  },
  setTeacherIds: (state: IInitialState, action: PayloadAction<string>) => {
    if (state.teacherIds.includes(action.payload)) {
      state.teacherIds = state.teacherIds.filter((id) => id !== action.payload);
    } else {
      state.teacherIds = [...state.teacherIds, action.payload];
    }
  },
  setSelectedAllTeacherIds: (
    state: IInitialState,
    action: PayloadAction<{ checked: boolean; teacherChooseList: ITeacher[] }>
  ) => {
    state.teacherIds = action.payload.checked
      ? action.payload.teacherChooseList.map((item) => item.code)
      : [];
  },
  addTransactionTeams: (
    state: IInitialState,
    action: PayloadAction<ITeacher[]>
  ) => {
    const teams = action.payload.filter((item) =>
      state.teacherIds.includes(item.code)
    );
    state.transactionTeams = [
      ...state.transactionTeams,
      ...convertToTransactionTeams(teams),
    ];
    state.transactionActionTeams = [
      ...state.transactionActionTeams,
      ...convertToTransactionTeams(teams),
    ];
    state.teacherIds = [];
  },
  setSelectedIds: (state: IInitialState, action: PayloadAction<number>) => {
    if (state.selectedIds.includes(action.payload)) {
      state.selectedIds = state.selectedIds.filter(
        (id) => id !== action.payload
      );
    } else {
      state.selectedIds = [...state.selectedIds, action.payload];
    }
  },
  setSelectedAllIds: (
    state: IInitialState,
    action: PayloadAction<{ checked: boolean }>
  ) => {
    state.selectedIds = action.payload.checked
      ? state.chooseDevices.map((item) => item.id)
      : [];
  },
  setInventoryItems: (
    state: IInitialState,
    action: PayloadAction<IInventoryTransactionItems[]>
  ) => {
    state.inventoryItems = action.payload;
    state.inventoryActionItems = action.payload.map((item) => ({
      deviceId: item.deviceId,
      id: item.id,
      deviceDefinitionId: item.deviceDefinitionId,
      quantity: item.quantity,
      beforeBrokenTotal: item.beforeBrokenTotal,
      beforeLostTotal: item.beforeLostTotal,
      beforeAvailableTotal: item.beforeAvailableTotal,
      afterBrokenTotal: item.afterBrokenTotal,
      afterLostTotal: item.afterLostTotal,
      afterAvailableTotal: item.afterAvailableTotal,
      afterChangeTotal: item.afterChangeTotal,
      changeBrokenTotal: item.changeBrokenTotal,
      changeLostTotal: item.changeLostTotal,
      notes: item.notes,
      roomId: item.roomId,
      isChange: false,
      isError: false,
    }));
  },
  setTransactionTeams: (
    state: IInitialState,
    action: PayloadAction<ITransactionTeam[]>
  ) => {
    state.transactionTeams = action.payload;
    state.transactionActionTeams = action.payload;
  },
  changeInventoryActionItems: (
    state: IInitialState,
    action: PayloadAction<IInventoryItems>
  ) => {
    const actionItemFind = state.inventoryActionItems.find(
      (item) => item.deviceId === action.payload.deviceId
    );
    const newDevice = {
      ...actionItemFind,
      ...action.payload,
    };
    const isOverQuantity =
      Number(newDevice.afterBrokenTotal) + Number(newDevice.afterLostTotal) >
      Number(newDevice.quantity);
    const isUnderQuantity =
      Number(newDevice.afterBrokenTotal) <
        Number(newDevice.beforeBrokenTotal) ||
      Number(newDevice.afterLostTotal) < Number(newDevice.beforeLostTotal);

    if (actionItemFind) {
      state.inventoryActionItems = state.inventoryActionItems.map((item) =>
        item.deviceId === action.payload.deviceId
          ? {
              ...newDevice,
              isError: isOverQuantity || isUnderQuantity,
              isChange: true,
            }
          : item
      );
    }
  },
  changeTransactionActionTeams: (
    state: IInitialState,
    action: PayloadAction<ITransactionActionTeam>
  ) => {
    const actionItemFind = state.transactionActionTeams.find(
      (item) => item.teacherCode === action.payload.teacherCode
    );
    if (actionItemFind) {
      state.transactionActionTeams = state.transactionActionTeams.map((item) =>
        item.teacherCode === action.payload.teacherCode
          ? { ...item, ...action.payload }
          : item
      );
    }
  },
  changeTransactionActionTeamLead: (
    state: IInitialState,
    action: PayloadAction<{ teacherCode: string }>
  ) => {
    state.transactionActionTeams = state.transactionActionTeams.map((item) =>
      item.teacherCode === action.payload.teacherCode
        ? { ...item, isTeamlead: !item.isTeamlead }
        : item
    );
  },
  changeFilterChooseDevices: (
    state: IInitialState,
    action: PayloadAction<{ key: keyof IInventoryDeviceParams; value: any }>
  ) => {
    state.filterChooseDevices = {
      ...state.filterChooseDevices,
      [action.payload.key]: action.payload.value,
    };
  },
  resetChooseModal: (state: IInitialState) => {
    state.chooseDevices = [];
    state.totalChooseDevices = 0;
    state.selectedIds = [];
  },
  deleteTransactionTeam: (
    state: IInitialState,
    action: PayloadAction<string>
  ) => {
    state.transactionTeams = state.transactionTeams.filter(
      (item) => item.teacherCode !== action.payload
    );
  },
  deleteInventoryTransactionItem: (
    state: IInitialState,
    action: PayloadAction<{ deviceId: number; id: number }>
  ) => {
    if (action.payload.id !== 0) {
      state.deleteInventoryTransactionItemIds = [
        ...state.deleteInventoryTransactionItemIds,
        action.payload.id,
      ];
    }
    state.inventoryItems = state.inventoryItems.filter(
      (item) => item.deviceId !== action.payload.deviceId
    );
  },
  deleteTransactionActionTeams: (
    state: IInitialState,
    action: PayloadAction<string>
  ) => {
    state.transactionActionTeams = state.transactionActionTeams.filter(
      (item) => item.teacherCode !== action.payload
    );
  },
  reset: (state: IInitialState) => {
    state.isFetching = false;
    state.selectedIds = [];
    state.chooseDevices = [];
    state.totalChooseDevices = 0;
    state.inventoryItems = [];
    state.transactionTeams = [];
    state.deleteInventoryTransactionItemIds = [];
    state.inventoryActionItems = [];
    state.transactionActionTeams = [];
    state.teacherIds = [];
    state.filterChooseDevices = DEFAULT_FILTER_CHOOSE_DEVICES;
  },
};

const inventoryTransactionSlice = createSlice({
  name: "inventoryTransaction",
  initialState,
  reducers,
  selectors,
});

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices
    extends WithSlice<typeof inventoryTransactionSlice> {}
}

const injectedInventoryTransactionSlice =
  inventoryTransactionSlice.injectInto(rootReducer);

export const inventoryTransactionSelectors =
  injectedInventoryTransactionSlice.selectors;
export const inventoryTransactionActions =
  injectedInventoryTransactionSlice.actions;
