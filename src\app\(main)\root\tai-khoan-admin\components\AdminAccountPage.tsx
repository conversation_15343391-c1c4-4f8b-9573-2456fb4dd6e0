"use client";

import { SchoolAdmin } from "./SchoolAdmin";
import { useState, useEffect } from "react";
import { Tabs, Tab, Box } from "@mui/material";
import { DivisionAdmin } from "./DivisionAdmin";
import { DoetAdmin } from "./DoetAdmin";
import { educationUnitsActions } from "@/redux/educationUnits.slice";
import { educationUnitsSaga } from "@/saga/educationUnits.saga";
import { createInjectableSaga } from "@/saga/injectableSaga";
import { useAppStore, useAppDispatch } from "@/redux/hook";

const AdminAccountPage = () => {
  const [tab, setTab] = useState(0);
  const store = useAppStore();
  const dispatch = useAppDispatch();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    createInjectableSaga(
      "educationUnitsReducer",
      educationUnitsSaga
    ).injectInto(store);
    setIsClient(true);

    return () => {
      dispatch(educationUnitsActions.educationUnitsReset());
    };
  }, []);

  if (!isClient) return null;

  return (
    <>
      <Box
        sx={{
          borderBottom: 1,
          borderColor: "divider",
          backgroundColor: "background.grey",
        }}
      >
        <Tabs value={tab} onChange={(_, v) => setTab(v)}>
          <Tab label="Nhà trường" />
          <Tab label="Phòng giáo dục" />
          <Tab label="Sở giáo dục" />
        </Tabs>
      </Box>
      {tab === 0 && <SchoolAdmin />}
      {tab === 1 && <DivisionAdmin />}
      {tab === 2 && <DoetAdmin />}
    </>
  );
};

export default AdminAccountPage;
