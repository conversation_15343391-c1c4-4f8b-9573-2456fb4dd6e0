import { PayloadAction, WithSlice, createSlice } from "@reduxjs/toolkit";
import { rootReducer } from "../../../redux/reducer";
import { AppConstant } from "@/constant";
import { IDonVi } from "@/models/app.model";
import { IDonviParams } from "./login.model";
import { IPaginationModel } from "@/models/response.model";

/* ------------- Initial State ------------- */
export interface IInitialState {
  isFetching: boolean;
  error: object | string | null;

  donViList: IDonVi[];
  phongList: IDonVi[];
  soList: IDonVi[];

  truongList: IDonVi[];
  pagination: IPaginationModel;
  totalTruong: number;
}

const initialState: IInitialState = {
  isFetching: false,
  error: null,

  donViList: [],
  soList: [],
  phongList: [],

  truongList: [],
  pagination: AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
  totalTruong: 0,
};

/* ------------- Selector ------------- */
const selectors = {
  getFetching: (state: IInitialState) => state.isFetching,
  selectSoList: (state: IInitialState) => state.soList,
  selectPhongList: (state: IInitialState) => state.phongList,
  selectTruongList: (state: IInitialState) => state.truongList,
  selectPagination: (state: IInitialState) => state.pagination,
  selectTotalTruong: (state: IInitialState) => state.totalTruong,
};

/* ------------- Reducers ------------- */
const reducers = {
  getSoList: (state: IInitialState) => {},
  getSoListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<IDonVi>;
      totalCount: number;
    }>
  ) => {
    state.soList = action.payload.data || [];
  },
  getPhongList: (
    state: IInitialState,
    action: PayloadAction<IDonviParams>
  ) => {},
  getPhongListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<IDonVi>;
      totalCount: number;
    }>
  ) => {
    state.isFetching = false;
    state.phongList = action.payload.data || [];
  },
  getTruongList: (
    state: IInitialState,
    action: PayloadAction<
      IDonviParams & {
        isScroll?: boolean;
      }
    >
  ) => {
    if (typeof action.payload.skip === "number") {
      state.pagination.skip = action.payload.skip;
    }
  },
  getTruongListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<IDonVi>;
      totalCount: number;
      isScroll?: boolean;
    }>
  ) => {
    if (action.payload.isScroll) {
      state.truongList = [...state.truongList, ...(action.payload.data || [])];
    } else {
      state.truongList = action.payload.data || [];
    }
    state.totalTruong = action.payload.totalCount || 0;
  },
  resetSoPhongTruong: (state: IInitialState) => {
    state.pagination = AppConstant.DEFAULT_PAGINATION_SKIP_TAKE;

    state.soList = [];
    state.phongList = [];
    state.truongList = [];
    state.totalTruong = 0;
  },
  loginFailure: (state: IInitialState, action: PayloadAction<any>) => {
    state.isFetching = false;
    state.error = action.payload ?? {};
  },
  loginReset: (state: IInitialState) => {
    state.isFetching = false;
    state.error = null;
  },
};

export const loginSlice = createSlice({
  name: "loginReducer",
  initialState,
  reducers,
  selectors,
});

export const loginActions = loginSlice.actions;

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices extends WithSlice<typeof loginSlice> {}
}

// Inject reducer
const injectedLoginSlice = loginSlice.injectInto(rootReducer);

export const loginSelectors = injectedLoginSlice.selectors;
