import http from "@/api";
import { ApiConstant } from "@/constant";
import {
  DataListResponseModel,
  IPaginationModel,
} from "@/models/response.model";
import stringFormat from "string-format";
import { IDevice, IDeviceParams, IEduDevice } from "@/models/eduDevice.model";

export const getDeviceDefinitionService = (params: IDeviceParams) => {
  return http.get<DataListResponseModel<IEduDevice>>(ApiConstant.EDU_DEVICE, {
    params,
  });
};

export const findBorrowRequestByWeekService = ({
  schoolWeekConfigId,
  teacherId,
}) => {
  return http.get(
    stringFormat(ApiConstant.FIND_BORROW_REQUEST_BY_TEACHER, {
      schoolWeekConfigId,
      teacherId,
    })
  );
};

export const getBorrowRequestService = (id) => {
  return http.get<DataListResponseModel<unknown>>(
    stringFormat(ApiConstant.BORROW_REQUEST_UPDATE, { id })
  );
};
