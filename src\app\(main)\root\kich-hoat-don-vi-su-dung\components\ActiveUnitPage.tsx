"use client";

import { TablePageLayout } from "@/components/common";
import StatusCell from "@/components/common/table/cell/StatusCell";
import { ITableRef } from "@/components/common/TablePageLayout/type";
import { ApiConstant, DataConstant } from "@/constant";
import { educationUnitsActions } from "@/redux/educationUnits.slice";
import { useAppDispatch, useAppStore } from "@/redux/hook";
import { educationUnitsSaga } from "@/saga/educationUnits.saga";
import { createInjectableSaga } from "@/saga/injectableSaga";
import { updateStatusService } from "@/services/app.service";
import { ColumnDef } from "@tanstack/react-table";
import { useEffect, useRef, useState } from "react";
import AddSchoolConfigModal from "./AddSchoolConfigModal";
import FilterCustom from "./FilterCustom";
import TabsFilterLayout from "./TabsFilterLayout";
import { ISchoolConfig } from "../schoolConfig.model";
import { schoolConfigSaga } from "../schoolConfig.saga";
import EditSchoolConfigModal from "./EditSchoolConfigModal";

const ActiveUnitPage = () => {
  const dispatch = useAppDispatch();
  const store = useAppStore();
  const tableRef = useRef<ITableRef>(null);
  const columns = getColumns(tableRef);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    createInjectableSaga(
      "educationUnitsReducer",
      educationUnitsSaga
    ).injectInto(store);

    createInjectableSaga("schoolConfigReducer", schoolConfigSaga).injectInto(
      store
    );

    setIsClient(true);

    return () => {
      dispatch(educationUnitsActions.educationUnitsReset());
    };
  }, []);

  if (!isClient) return null;

  return (
    <TabsFilterLayout tableRef={tableRef}>
      <TablePageLayout<ISchoolConfig>
        ref={tableRef}
        fetchAll={false}
        visibleCol={VISIBLE_COL}
        apiUrl={ApiConstant.SCHOOL_CONFIG}
        tableProps={{
          columns,
        }}
        filterConfig={[
          {
            key: "searchKey",
            type: "text",
            label: "Tìm kiếm",
            size: 2.4,
          },
          { key: "groupUnitCode", value: DataConstant.DON_VI_TYPE.truong },
          {
            key: "doetCode",
          },
          {
            key: "divisionCode",
          },
          {
            key: "schoolCode",
          },
          {
            key: "status",
          },
          {
            key: "isConfigDomain",
          },
        ]}
        formConfig={{
          detailUrl: ApiConstant.SCHOOL_CONFIG,
        }}
        collapseFilterCustom={(props) => <FilterCustom props={props} />}
        actions={["create", "update"]}
        EditModalComponent={({ isOpen, onClose, modalData }) => (
          <EditSchoolConfigModal
            open={isOpen}
            onClose={onClose}
            data={modalData}
          />
        )}
        CreateModalComponent={({ isOpen, onClose }) => (
          <AddSchoolConfigModal open={isOpen} onClose={onClose} />
        )}
      />
    </TabsFilterLayout>
  );
};

export default ActiveUnitPage;

const getColumns = (
  tableRef: React.RefObject<ITableRef | null>
): ColumnDef<ISchoolConfig>[] => [
  {
    id: "information",
    header: "Thông tin đơn vị",
    accessorKey: "information",
    size: 300,
    accessorFn: (row) => `${row.name} (${row.schoolCode})`,
  },
  {
    id: "domainUrl",
    header: "Domain nhà trường",
    accessorKey: "domainUrl",
    size: 200,
    cell: ({ row }) => (
      <a target="_blank" href={`http://${row.original.website}`}>
        {row.original.website}
      </a>
    ),
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Kích hoạt trường",
    size: 120,
    cell: ({ row }) => (
      <StatusCell
        status={row.original.status}
        onStatusChange={(_, value) => {
          updateStatusService({
            id: row.original.id,
            status: Number(value),
            onSuccess: tableRef?.current?.fetchCurrentData,
            url: ApiConstant.STATUS_SCHOOL,
          });
        }}
      />
    ),
    meta: { align: "center" },
  },
];

const VISIBLE_COL = [
  { id: "information", name: "Thông tin đơn vị" },
  { id: "domainUrl", name: "Domain nhà trường" },
  { id: "status", name: "Kích hoạt trường" },
];
