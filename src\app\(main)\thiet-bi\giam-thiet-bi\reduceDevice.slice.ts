import { IDeviceInput } from "@/app/(main)/thiet-bi/giam-thiet-bi/type";
import { IDevice, IDeviceParams } from "@/models/eduDevice.model";
import { IPaginationModel } from "@/models/response.model";
import { rootReducer } from "@/redux/reducer";
import { RootState } from "@/redux/store";
import {
  createSelector,
  createSlice,
  PayloadAction,
  WithSlice,
} from "@reduxjs/toolkit";

export interface IInitialState {
  deviceChooseList: IDevice[];
  totalDeviceChoose: number;
  devices: IDevice[];
  deviceDeleted: number[];
  deviceSelected: number[];
  deviceInput: IDeviceInput[];
}

const initialState: IInitialState = {
  deviceChooseList: [],
  totalDeviceChoose: 0,
  devices: [],
  deviceDeleted: [],
  deviceSelected: [],
  deviceInput: [],
};

const reduceDeviceSelector = {
  deviceChooseList: (state: IInitialState) => state.deviceChooseList,
  totalDeviceChoose: (state: IInitialState) => state.totalDeviceChoose,
  devices: (state: IInitialState) => state.devices,
  deviceDeleted: (state: IInitialState) => state.deviceDeleted,
  deviceSelected: (state: IInitialState) => state.deviceSelected,
  deviceInput: (state: IInitialState) => state.deviceInput,
};

export const selectorDeviceItem = (id: number) =>
  createSelector(
    [(state: RootState) => state.reduceDevice?.devices ?? []],
    (devices) => devices.find((item) => item.id === id)
  );

export const selectorDeviceInputById = (id: number) =>
  createSelector(
    [(state: RootState) => state.reduceDevice?.deviceInput ?? []],
    (deviceInput) => deviceInput.find((item) => item.id === id)
  );

export const selectorDeviceInput = createSelector(
  [(state: RootState) => state.reduceDevice?.deviceInput ?? []],
  (deviceInput) => deviceInput
);

export const selectorDeviceDeleted = createSelector(
  [(state: RootState) => state.reduceDevice?.deviceDeleted ?? []],
  (deviceDeleted) => deviceDeleted
);

export const selectorDevices = createSelector(
  [(state: RootState) => state.reduceDevice?.devices ?? []],
  (devices) => devices
);

const reduceDeviceReducer = {
  // Danh sách thiết bị ở phiếu
  setDevices: (state: IInitialState, action: PayloadAction<IDevice[]>) => {
    state.devices = action.payload.map((item) => ({
      ...item,
      totalBroken: (item.totalBroken || 0) + (item.transactionTotalBroken || 0),
      totalLost: (item.totalLost || 0) + (item.transactionTotalLost || 0),
      totalAvailable:
        (item.totalAvailable || 0) + (item.transactionTotalAvailable || 0),
    }));

    state.deviceInput = action.payload.map((item) => ({
      id: item.id,
      totalBroken: item.transactionTotalBroken,
      maxBroken: (item.totalBroken || 0) + (item.transactionTotalBroken || 0),
      totalLost: item.transactionTotalLost,
      maxLost: (item.totalLost || 0) + (item.transactionTotalLost || 0),
      totalAvailable: item.transactionTotalAvailable,
      maxAvailable:
        (item.totalAvailable || 0) + (item.transactionTotalAvailable || 0),
      isError: false,
      isChange: false,
    }));
  },
  addDevices: (state: IInitialState) => {
    const newDevices = state.deviceChooseList.filter((item) =>
      state.deviceSelected.includes(item.id)
    );
    state.devices = [...state.devices, ...newDevices];
    const newDeviceInput = newDevices.map((item) => ({
      id: item.id,
      maxBroken: item.totalBroken,
      maxLost: item.totalLost,
      maxAvailable: item.totalAvailable,
      isError: false,
      isChange: true,
    }));
    state.deviceInput = [...state.deviceInput, ...newDeviceInput];
    state.deviceSelected = [];
  },
  deleteDevice: (state: IInitialState, action: PayloadAction<number>) => {
    const deviceFind = state.devices.find((item) => item.id === action.payload);
    state.devices = state.devices.filter((item) => item.id !== action.payload);
    if (deviceFind && deviceFind.deviceTransactionItemId !== 0) {
      state.deviceDeleted = [
        ...state.deviceDeleted,
        deviceFind.deviceTransactionItemId,
      ];
    }
  },

  // Danh sách thiết bị đã chọn ở chọn thêm thiết bị
  setDeviceSelected: (
    state: IInitialState,
    action: PayloadAction<number | Array<number>>
  ) => {
    const id = action.payload;
    if (Array.isArray(id)) {
      state.deviceSelected = action.payload as Array<number>;
    } else {
      let newIds: Array<number> = [];
      const isExisted = state.deviceSelected.includes(id);
      if (isExisted) {
        newIds = state.deviceSelected.filter((item) => item !== id);
      } else {
        newIds = [...state.deviceSelected, id];
      }
      state.deviceSelected = newIds;
    }
  },
  setDeviceSelectedAll: (
    state: IInitialState,
    action: PayloadAction<{
      checked: boolean;
      deviceChooseList: IDevice[];
    }>
  ) => {
    let newIds: Array<number> = [];
    const deviceChooseList = action.payload.deviceChooseList;
    const numberIds = deviceChooseList.map((item) => item.id);

    if (action.payload.checked) {
      newIds = [...state.deviceSelected, ...numberIds];
    } else {
      newIds = state.deviceSelected.filter((item) => !numberIds.includes(item));
    }
    newIds = [...new Set(newIds)];
    state.deviceSelected = newIds;
  },

  // Danh sách thiết bị đã xóa ở phiếu
  // setDeviceDeleted: (state: IInitialState, action: PayloadAction<number[]>) => {
  //   state.deviceDeleted = action.payload;
  // },
  changeDeviceInput: (
    state: IInitialState,
    action: PayloadAction<IDeviceInput>
  ) => {
    const deviceFind = state.deviceInput.find(
      (item) => item.id === action.payload.id
    );

    const newDeviceInput = {
      ...deviceFind,
      ...action.payload,
      isChange: true,
    };

    const isError =
      (newDeviceInput.totalBroken || 0) > (newDeviceInput.maxBroken || 0) ||
      (newDeviceInput.totalLost || 0) > (newDeviceInput.maxLost || 0) ||
      (newDeviceInput.totalAvailable || 0) > (newDeviceInput.maxAvailable || 0);

    if (deviceFind) {
      state.deviceInput = state.deviceInput.map((item) =>
        item.id === action.payload.id
          ? { ...item, ...newDeviceInput, isError }
          : item
      );
    }
  },
  checkDeviceInput: (state: IInitialState, action: PayloadAction<number>) => {
    const deviceFind = state.deviceInput.find(
      (item) => item.id === action.payload
    );
    const isError =
      (deviceFind?.totalBroken || 0) > (deviceFind?.maxBroken || 0) ||
      (deviceFind?.totalLost || 0) > (deviceFind?.maxLost || 0) ||
      (deviceFind?.totalAvailable || 0) > (deviceFind?.maxAvailable || 0);

    state.deviceInput = state.deviceInput.map((item) =>
      item.id === action.payload ? { ...item, isError } : item
    );
  },
  getDeviceChoose: (
    state: IInitialState,
    action: PayloadAction<IDeviceParams & IPaginationModel>
  ) => {},
  getDeviceChooseSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: IDevice[];
      totalCount: number;
    }>
  ) => {
    state.deviceChooseList = action.payload.data;
    state.totalDeviceChoose = action.payload.totalCount;
  },

  resetModalSelected: (state: IInitialState) => {
    state.deviceSelected = [];
    state.totalDeviceChoose = 0;
    state.deviceChooseList = [];
  },
  resetModalDetails: (state: IInitialState) => {
    state.deviceInput = [];
    state.deviceDeleted = [];
    state.devices = [];
  },
  resetReduceDevice: (state: IInitialState) => {
    state.deviceChooseList = [];
    state.totalDeviceChoose = 0;
    state.devices = [];
    state.deviceDeleted = [];
    state.deviceInput = [];
    state.deviceDeleted = [];
    state.deviceSelected = [];
  },
};

export const reducerDeviceSlice = createSlice({
  name: "reduceDevice",
  initialState,
  reducers: reduceDeviceReducer,
  selectors: reduceDeviceSelector,
});

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices
    extends WithSlice<typeof reducerDeviceSlice> {}
}

const injectedReduceDeviceSlice = reducerDeviceSlice.injectInto(rootReducer);

export const reduceDeviceSelectors = injectedReduceDeviceSlice.selectors;
export const reduceDeviceActions = injectedReduceDeviceSlice.actions;
