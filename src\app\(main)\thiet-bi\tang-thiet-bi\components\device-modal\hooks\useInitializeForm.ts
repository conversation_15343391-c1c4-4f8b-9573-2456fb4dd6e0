import { useEffect } from "react";
import { UseFormSetValue } from "react-hook-form";
import { INIT_VALUE } from "../AddDeviceModal";
import {
  IDeviceRoomGroup,
  IDeviceTransactionUI,
} from "../../../equipmentDocument.model";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { selectGradeList } from "@/redux/system.slice";
import { USER_TYPE_LIST } from "@/constant/data.const";
import { MANAGE_BY } from "@/models/system.model";
import { mapIdsToOptions, toOption } from "@/components/common/AppAutoComplete";
import { equipmentDocumentActions } from "../../../equipmentDocument.slice";

interface Props {
  data?: IDeviceRoomGroup | null;
  setValue: UseFormSetValue<IDeviceTransactionUI>;
}

export const useInitializeForm = ({ data, setValue }: Props) => {
  const gradeList = useAppSelector(selectGradeList);

  const dispatch = useAppDispatch();
  useEffect(() => {
    if (!data?.id) return;

    setValue("statisticCode", data.statisticCode ?? "");
    setValue("deviceCode", data.deviceCode ?? "");
    setValue("deviceName", data.deviceName ?? "");
    setValue("isConsumable", data.isConsumable ?? INIT_VALUE.isConsumable);
    setValue("isSelfMade", data.isSelfMade ?? INIT_VALUE.isSelfMade);
    setValue(
      "minimumQuantity",
      data.minimumQuantity ?? INIT_VALUE.minimumQuantity
    );
    setValue("totalDevices", data.totalDevices ?? ("" as any));
    setValue(
      "maxIndexItem",
      data.maxIndexItem ? Number(data.maxIndexItem) + 1 : 1
    );

    setValue(
      "deviceUnitId",
      toOption(data.deviceUnitId, data.deviceUnitName ?? "")
    );

    setValue(
      "schoolDeviceTypeId",
      toOption(data.schoolDeviceTypeId ?? null, data.schoolDeviceTypeName ?? "")
    );

    setValue(
      "schoolSubjectId",
      toOption(data.schoolSubjectId ?? null, data.schoolSubjectName ?? "")
    );

    setValue("gradeCodes", mapIdsToOptions(data.gradeCodes, gradeList));
    setValue("userTypes", mapIdsToOptions(data.userTypes, USER_TYPE_LIST));

    setValue(
      "manageBy",
      data.isManageDevice
        ? MANAGE_BY.isManageDevice
        : MANAGE_BY.isManageQuantity
    );

    setValue("itemId", data.itemId as number);
    setValue("id", data.deviceDefinitionId as number);
    dispatch(
      equipmentDocumentActions.getMapDevice({
        id: data.deviceDefinitionId,
        setValue,
      })
    );
  }, [data?.id]);
};
