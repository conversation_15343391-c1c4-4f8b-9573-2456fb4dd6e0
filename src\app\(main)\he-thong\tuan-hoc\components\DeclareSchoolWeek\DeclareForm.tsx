import { AppFormAutocomplete, AppFormTextField } from "@/components/common";
import AppFormDatePicker from "@/components/common/form/AppFormDatePicker";
import { SEMESTER_TYPE_LIST } from "@/constant/data.const";
import { Stack } from "@mui/material";
import { useFormContext, useWatch } from "react-hook-form";

const DeclareForm = () => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const minValue = useWatch({
    control,
    name: "fromWeek",
  });

  return (
    <Stack direction={"column"} spacing={2}>
      <AppFormAutocomplete
        name="semester"
        label="<PERSON>ọ<PERSON> kỳ"
        options={SEMESTER_TYPE_LIST}
        control={control}
        rules={{
          required: "<PERSON>ọc kỳ không được để trống",
        }}
        autocompleteProps={{
          textFieldProps: {
            error: !!errors?.semester,
            helperText: errors?.semester?.message as string,
          },
        }}
      />
      <AppFormDatePicker
        control={control}
        name="fromDate"
        label="Bắt đầu từ ngày"
        rules={{
          required: "<PERSON>ày bắt đầu không được để trống",
        }}
        datePickerProps={{
          maxDate: undefined,
          slotProps: {
            textField: {
              error: !!errors?.fromDate,
              helperText: errors?.fromDate?.message as string,
            },
          },
        }}
      />
      <AppFormTextField
        name="fromWeek"
        label="Tuần bắt đầu"
        control={control}
        rules={{
          required: "Tuần bắt đầu không được để trống",
        }}
        textfieldProps={{
          error: !!errors?.fromWeek,
          helperText: errors?.fromWeek?.message as string,
          type: "number",
          slotProps: {
            htmlInput: {
              min: 1,
            },
          },
          sx: {
            "& input": {
              textAlign: "start",
            },
          },
        }}
      />
      <AppFormTextField
        name="toWeek"
        label="Đến tuần học"
        control={control}
        rules={{
          required: "Tuần kết thúc không được để trống",
          validate: (value) => {
            if (value && minValue && Number(value) < Number(minValue)) {
              return "Tuần kết thúc phải lớn hơn tuần bắt đầu";
            }
            return true;
          },
        }}
        textfieldProps={{
          error: !!errors?.toWeek,
          helperText: errors?.toWeek?.message as string,
          type: "number",
          slotProps: {
            htmlInput: {
              min: 1,
            },
          },
          sx: {
            "& input": {
              textAlign: "start",
            },
          },
        }}
      />
    </Stack>
  );
};

export default DeclareForm;
