import { Components, Theme } from "@mui/material";
import Mu<PERSON>Button from "./MuiButton";
import Mui<PERSON>conButton from "./MuiIconButton";
import MuiFormLabel from "./MuiFormLabel";
import MuiSwitch from "./MuiSwitch";
import MuiTab from "./MuiTab";
import MuiTabs from "./MuiTabs";
import MuiChip from "./MuiChip";
import MuiRadio from "./MuiRadio";

const components: Components<Omit<Theme, "components">> = {
  MuiButton,
  MuiIconButton,
  MuiFormLabel,
  MuiSwitch,
  MuiTab,
  MuiTabs,
  MuiChip,
  MuiRadio,
};

export default components;
