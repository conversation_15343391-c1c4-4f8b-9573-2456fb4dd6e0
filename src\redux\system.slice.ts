import { rootReducer } from "@/redux/reducer";
import {
  PayloadAction,
  WithSlice,
  createSelector,
  createSlice,
} from "@reduxjs/toolkit";
import { RootState } from "./store";
import {
  IClass,
  ICountry,
  IDeviceType,
  IGrade,
  IPeriod,
  IRoom,
  ISchoolWeek,
  ISource,
  ISubject,
  ITeacher,
  IUnit,
} from "@/models/system.model";
import {
  convertDataToOptions,
  formatDayjsWithType,
} from "@/utils/format.utils";

/* ------------- Initial State ------------- */
export interface IInitialState {
  isFetching: boolean;
  error: object | string | null;

  sourceList: ISource[];
  unitList: IUnit[];
  deviceTypeList: IDeviceType[];
  subjectList: ISubject[];
  classList: IClass[];
  roomList: IRoom[];
  teacherComboList: ITeacher[];
  gradeList: IGrade[];

  countries: ICountry[];

  periodList: IPeriod[];

  schoolWeekList: ISchoolWeek[];
}

const initialState: IInitialState = {
  isFetching: false,
  error: null,

  sourceList: [],
  unitList: [],
  deviceTypeList: [],
  subjectList: [],
  classList: [],
  roomList: [],
  teacherComboList: [],
  gradeList: [],
  countries: [],
  periodList: [],
  schoolWeekList: [],
};

/* ------------- Selector ------------- */

export const selectSystemSourceList = createSelector(
  [(state: RootState) => state.systemReducer?.sourceList],
  (sourceList) => {
    return convertDataToOptions(sourceList);
  }
);

export const selectUnitList = createSelector(
  (state: RootState) => state.systemReducer?.unitList,
  (unitList) => convertDataToOptions(unitList)
);
export const selectDeviceTypeList = createSelector(
  [(state: RootState) => state.systemReducer?.deviceTypeList],
  (list) => convertDataToOptions(list)
);

export const selectSubjectList = createSelector(
  [(state: RootState) => state.systemReducer?.subjectList],
  (list) => convertDataToOptions(list)
);

export const selectClassList = createSelector(
  [(state: RootState) => state.systemReducer?.classList],
  (list) => convertDataToOptions(list)
);

export const selectRoomList = createSelector(
  [(state: RootState) => state.systemReducer?.roomList],
  (list) => convertDataToOptions(list)
);

export const selectTeacherComboList = createSelector(
  [(state: RootState) => state.systemReducer?.teacherComboList],
  (list) => convertDataToOptions(list)
);

export const selectCountries = createSelector(
  [(state: RootState) => state.systemReducer?.countries],
  (list) => convertDataToOptions(list)
);

export const selectGradeList = createSelector(
  [(state: RootState) => state.systemReducer?.gradeList],
  (list) => convertDataToOptions(list)
);

export const selectPeriodList = createSelector(
  [(state: RootState) => state.systemReducer?.periodList],
  (list) =>
    list?.map((item) => ({
      id: item.id,
      label: `${item.lession} - ${item.session}`,
    })) ?? []
);

export const selectSchoolWeekList = createSelector(
  [(state: RootState) => state.systemReducer?.schoolWeekList],
  (shoolWeekConfigs) =>
    (shoolWeekConfigs ?? []).map((item) => ({
      ...item,
      label: `Tuần ${item.weekCode}: ${formatDayjsWithType(
        item.fromDate
      )} - ${formatDayjsWithType(item.toDate)}  `,
    }))
);

/* ------------- Reducers ------------- */
const reducers = {
  getSourceList: (state: IInitialState) => {},
  getSourceListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<ISource>;
      totalCount: number;
    }>
  ) => {
    state.sourceList = action.payload.data || [];
  },
  getUnitList: (state: IInitialState) => {},
  getUnitListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<IUnit>;
      totalCount: number;
    }>
  ) => {
    state.unitList = action.payload.data || [];
  },
  getDeviceTypeList: (state: IInitialState) => {},
  getDeviceTypeListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<IDeviceType>;
      totalCount: number;
    }>
  ) => {
    state.deviceTypeList = action.payload.data || [];
  },
  getSubjectList: (state: IInitialState) => {},
  getSubjectListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<ISubject>;
      totalCount: number;
    }>
  ) => {
    state.subjectList = action.payload.data || [];
  },
  getClassList: (state: IInitialState) => {},
  getClassListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<IClass>;
      totalCount: number;
    }>
  ) => {
    state.classList = action.payload.data || [];
  },
  getRoomList: (state: IInitialState) => {},
  getRoomListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<IRoom>;
      totalCount: number;
    }>
  ) => {
    state.roomList = action.payload.data || [];
  },
  getGradeList: (state: IInitialState) => {},
  getGradeListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<IGrade>;
      totalCount: number;
    }>
  ) => {
    state.gradeList = action.payload.data || [];
  },
  getTeacherComboList: (state: IInitialState) => {},
  getTeacherComboListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<ITeacher>;
      totalCount: number;
    }>
  ) => {
    state.teacherComboList = action.payload.data || [];
  },
  getCountryList: (state: IInitialState) => {},
  getCountryListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<ICountry>;
      totalCount: number;
    }>
  ) => {
    state.countries = action.payload.data || [];
  },
  getPeriodList: (state: IInitialState) => {},
  getPeriodListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<IPeriod>;
      totalCount: number;
    }>
  ) => {
    state.periodList = action.payload.data || [];
  },

  getSchoolWeekList: (
    state: IInitialState,
    action: PayloadAction<{ onSuccess?: () => void }>
  ) => {},

  getSchoolWeekListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<ISchoolWeek>;
      totalCount: number;
    }>
  ) => {
    state.schoolWeekList = action.payload.data || [];
  },

  systemFailure: (state: IInitialState, action: PayloadAction<any>) => {
    state.isFetching = false;
    state.error = action.payload ?? {};
  },
  systemReset: (state: IInitialState) => {
    state.isFetching = false;
    state.error = null;

    state.sourceList = [];
    state.unitList = [];
    state.deviceTypeList = [];
    state.subjectList = [];
    state.classList = [];
    state.roomList = [];
    state.teacherComboList = [];
    state.gradeList = [];
    state.countries = [];
    state.periodList = [];
    state.schoolWeekList = [];
  },
};

export const systemSlice = createSlice({
  name: "systemReducer",
  initialState,
  reducers,
});

export const systemActions = systemSlice.actions;

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices extends WithSlice<typeof systemSlice> {}
}

// Inject reducer
const injectedSystemSlice = systemSlice.injectInto(rootReducer);

export const systemSelectors = injectedSystemSlice.selectors;
