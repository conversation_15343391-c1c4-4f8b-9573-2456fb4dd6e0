import { DataConstant } from "@/constant";
import { <PERSON>Item, TreeItemProps } from "@mui/x-tree-view/TreeItem";
import { IconButton, Stack, StackProps, Typography } from "@mui/material";
import { MouseEvent, ReactNode, memo } from "react";
import { ITreeData } from "@/models/types";

const TreeItemCustom = ({
  data,
  hasMoreICon = true,
  onCustomIcon,
  onCheckBox,
  onClickTreeItem,
  onClickLabel,
  sx,
  hasOrder = false,
  labelItemProps,
  ...otherProps
}: TreeItemCustomProps) => {
  const renderTree = (nodes: ITreeData) => (
    <TreeItem
      key={nodes.id}
      itemId={nodes.id.toString()}
      label={
        <Stack
          alignItems="center"
          direction="row"
          spacing={1}
          {...labelItemProps}
        >
          {onCheckBox?.(nodes)}
          <Typography
            color={nodes?.status ? "inherit" : "text.disabled"}
            className="ellipsis"
            onClick={() => onClickLabel?.(nodes)}
            sx={{ cursor: "pointer" }}
          >
            {hasOrder && nodes?.order && <strong>{nodes?.order + ". "}</strong>}
            {nodes.name}
          </Typography>
          {onCustomIcon?.(nodes)}
          {hasMoreICon && (
            <IconButton
              onClick={(event) => {
                event.stopPropagation();

                if (onClickTreeItem instanceof Function)
                  onClickTreeItem(event, nodes);
              }}
              size="small"
              sx={{
                color: "primary.main",
                border: "1px solid currentColor",
                fontSize: 12,
                width: 16,
                height: 16,
              }}
            ></IconButton>
          )}
        </Stack>
      }
      sx={{
        "& .MuiTreeItem-label": {
          color: "text.primary",
        },
        "& .Mui-selected .MuiTreeItem-label": {
          color: "primary.main",
        },
        "& .MuiTreeItem-content": {
          height: 32,
          paddingLeft: 2,
          borderRadius: 0,
        },
        "& .MuiCollapse-root": {
          marginLeft: 3,
          borderLeft: `1px dashed`,
        },
        ...sx,
      }}
      {...otherProps}
    >
      {Array.isArray(nodes.children)
        ? nodes.children.map((node) => renderTree(node))
        : null}
    </TreeItem>
  );

  return <>{data.map((dataItem) => renderTree(dataItem))}</>;
};

type TreeItemCustomProps = Omit<TreeItemProps, "itemId"> & {
  data: Array<ITreeData>;
  hasMoreICon?: boolean;
  hasOrder?: boolean;
  labelItemProps?: StackProps;
  onClickLabel?: (data: ITreeData) => void;
  onCustomIcon?: (data: ITreeData) => ReactNode;
  onCheckBox?: (data: ITreeData) => ReactNode;
  onClickTreeItem?: (
    event: MouseEvent<HTMLButtonElement>,
    data: ITreeData
  ) => void;
};

export default memo(TreeItemCustom);
