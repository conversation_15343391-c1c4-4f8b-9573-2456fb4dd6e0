import { createSlice, WithSlice, PayloadAction } from "@reduxjs/toolkit";
import { rootReducer } from "@/redux/reducer";
import { IAddLostDamageDeviceAction } from "../lostDamgeDevice.model";
import { ILostDamageDevicePayload } from "../services/lostDamagedDvice.service";

interface DeviceState {
  loading: boolean;
  error: any;
}

const initialState: DeviceState = {
  loading: false,
  error: null,
};

const deviceSlice = createSlice({
  name: "lostDamageDevice",
  initialState,
  reducers: {
    addLostDamageDevice: (
      state,
      action: PayloadAction<IAddLostDamageDeviceAction>
    ) => {
      state.loading = true;
      state.error = null;
    },
    addLostDamageDeviceSuccess: (state, action: PayloadAction<unknown>) => {
      state.loading = false;
      state.error = null;
    },
    updateLostDamageDevice: (
      state,
      action: PayloadAction<{
        id: number;
        data: ILostDamageDevicePayload;
        onSuccess: () => void;
      }>
    ) => {
      state.loading = true;
      state.error = null;
    },
    updateLostDamageDeviceSuccess: (state, action: PayloadAction<unknown>) => {
      state.loading = false;
      state.error = null;
    },
    fixLostDamageDevice: (
      state,
      action: PayloadAction<{
        id: number;
        data: { totalFixed: number };
        onSuccess: () => void;
      }>
    ) => {
      state.loading = true;
      state.error = null;
    },
    fixLostDamageDeviceSuccess: (state, action: PayloadAction<unknown>) => {
      state.loading = false;
      state.error = null;
    },
  },
});

// Actions
export const {
  addLostDamageDevice,
  addLostDamageDeviceSuccess,
  updateLostDamageDevice,
  updateLostDamageDeviceSuccess,
  fixLostDamageDevice,
  fixLostDamageDeviceSuccess,
} = deviceSlice.actions;

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices extends WithSlice<typeof deviceSlice> {}
}

const injectedDeviceSlice = deviceSlice.injectInto(rootReducer);

export default deviceSlice.reducer;
