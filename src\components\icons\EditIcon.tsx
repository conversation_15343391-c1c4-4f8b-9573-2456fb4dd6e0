import React, { memo } from "react";
import { SvgIcon, SvgIconProps } from "@mui/material";

const EditIcon = ({ sx, ...otherProps }: SvgIconProps) => {
  return (
    <SvgIcon
      viewBox="0 0 25 25"
      sx={{ fontSize: "inherit", ...sx }}
      {...otherProps}
    >
      <path
        d="M20.9961 12.4038C20.5961 12.4038 20.2461 12.7538 20.2461 13.1538V20.6538C20.2461 20.8038 20.1461 20.9038 19.9961 20.9038H4.99609C4.84609 20.9038 4.74609 20.8038 4.74609 20.6538V5.65381C4.74609 5.50381 4.84609 5.40381 4.99609 5.40381H12.4961C12.8961 5.40381 13.2461 5.05381 13.2461 4.65381C13.2461 4.25381 12.8961 3.90381 12.4961 3.90381H4.99609C4.04609 3.90381 3.24609 4.70381 3.24609 5.65381V20.6538C3.24609 21.6038 4.04609 22.4038 4.99609 22.4038H19.9961C20.9461 22.4038 21.7461 21.6038 21.7461 20.6538V13.1538C21.7461 12.7538 21.3961 12.4038 20.9961 12.4038Z"
        fill="currentColor"
      />
      <path
        d="M10.4965 12.0039L9.74655 14.9539C9.69655 15.2039 9.74655 15.5039 9.94655 15.6539C10.0965 15.8039 10.2965 15.8539 10.4965 15.8539H10.6965L13.6465 15.1039C13.7965 15.0539 13.8965 15.0039 13.9965 14.9039L21.0465 7.85391C21.9465 6.95391 21.9465 5.45391 21.0465 4.55391C20.1465 3.65391 18.6465 3.65391 17.7465 4.55391L10.6965 11.6539C10.5965 11.7539 10.5465 11.8539 10.4965 12.0039ZM11.8965 12.5539L18.8465 5.65391C19.1465 5.35391 19.6965 5.35391 19.9965 5.65391C20.2965 5.95391 20.2965 6.50391 19.9965 6.80391L13.0965 13.7539L11.5465 14.1539L11.8965 12.5539Z"
        fill="currentColor"
      />
    </SvgIcon>
  );
};

export default memo(EditIcon);
