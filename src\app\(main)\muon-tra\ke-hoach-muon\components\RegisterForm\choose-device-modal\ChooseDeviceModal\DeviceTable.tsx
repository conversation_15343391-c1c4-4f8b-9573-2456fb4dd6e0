import { TablePageLayout } from "@/components/common";
import { DEVICE_LIST } from "@/constant/api.const";
import { ColumnDef } from "@tanstack/react-table";
import React, { memo, useCallback, useMemo, useState } from "react";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import {
  ActionType,
  FilterConfig,
} from "@/components/common/TablePageLayout/type";
import { ApiConstant } from "@/constant";
import { useFieldArray, useFormContext } from "react-hook-form";
import { IChooseDeviceForm } from ".";
import { Typography } from "@mui/material";
import { formatNumber } from "@/utils/format.utils";

const DeviceTable = () => {
  const { control, getValues } = useFormContext<IChooseDeviceForm>();

  const [totalSelected, setTotalSelected] = useState(0);

  const { remove, append } = useFieldArray({
    control,
    name: "deviceSelected",
  });

  const handleRowSelect = useCallback(
    (selected: IChooseDeviceForm["deviceSelected"][number][]) => {
      const currentSelected = getValues("deviceSelected");
      setTotalSelected(selected.length);

      const currentIds = currentSelected.map((item) => item.id);
      const newIds = selected.map((item) => item.id);

      const addedItems = selected.filter(
        (item) => !currentIds.includes(item.id)
      );
      addedItems.forEach((item) => {
        append({ ...item, totalBorrow: 1 });
      });

      currentSelected.forEach((item, index) => {
        if (!newIds.includes(item.id)) {
          remove(index);
        }
      });
    },
    [append, remove, getValues]
  );
  const tableProps = useMemo(() => {
    const selected = getValues("deviceSelected");

    return {
      initialSelectedRows: selected,
      columns: COLUMNS,
      onRowSelectionChange: handleRowSelect,
      ...TABLE_MODAL_FULL_HEIGHT,
    };
  }, [handleRowSelect]);

  return (
    <TablePageLayout<IChooseDeviceForm["deviceSelected"][number]>
      apiUrl={DEVICE_LIST}
      filterConfig={FILTER_CONFIG}
      actions={ACTIONS}
      tableProps={tableProps}
      hasReload={false}
      customActions={
        <Typography
          lineHeight={"36px"}
          color="primary"
          fontWeight={500}
        >{`${totalSelected} thiết bị đã được chọn`}</Typography>
      }
    />
  );
};

export default memo(DeviceTable);
const ACTIONS: ActionType[] = ["check"];
const FILTER_CONFIG: FilterConfig[] = [
  {
    key: "searchKey",
    type: "text",
    label: "Tìm kiếm",
    size: 2.4,
  },
  {
    key: "roomId",
    type: "select",
    label: "Kho/Phòng",
    size: 2.4,
    apiListUrl: ApiConstant.ROOM,
  },
  {
    key: "schoolSubjectId",
    type: "select",
    label: "Môn học",
    size: 2.4,
    apiListUrl: ApiConstant.SUBJECT,
  },
  {
    key: "schoolDeviceTypeId",
    type: "select",
    label: "Loại thiết bị",
    size: 2.4,
    apiListUrl: ApiConstant.DEVICE_TYPE,
  },
  {
    key: "isAvailable",
    value: "true",
  },
];

export const COLUMNS: ColumnDef<IChooseDeviceForm["deviceSelected"][number]>[] =
  [
    {
      accessorKey: "deviceCode",
      header: "Mã thiết bị",
      size: 50,
    },
    {
      header: "Thiết bị",
      cell: ({ row }) => {
        return (
          <>
            <Typography>{row.original.deviceName}</Typography>
            <Typography sx={{ color: "grey.500", fontSize: 12 }}>
              {row.original.code}
            </Typography>
          </>
        );
      },
    },
    {
      accessorKey: "deviceUnitName",
      header: "Đơn vị tính",
    },
    {
      accessorKey: "roomName",
      header: "Kho/Phòng",
    },
    {
      accessorKey: "schoolSubjectName",
      header: "Môn học",
    },
    {
      accessorKey: "gradeCode",
      header: "Khối lớp",
    },
    {
      size: 60,
      accessorFn: (row) => formatNumber(row.totalBorrowReady),
      header: "Số lượng có sẵn",
      meta: {
        align: "right",
      },
    },
  ];
