import useActionSchoolWeek from "@/app/(main)/he-thong/tuan-hoc/hooks/useActionSchoolWeek";
import { selectorRowSelected } from "@/app/(main)/he-thong/tuan-hoc/schoolWeek.slice";
import { AppConfirmModal } from "@/components/common";
import { useAppSelector } from "@/redux/hook";
import { Button } from "@mui/material";
import { useState } from "react";
import { toast } from "sonner";

const DeleteSchoolWeek = ({ onSuccess }: { onSuccess: () => void }) => {
  const selectedRows = useAppSelector(selectorRowSelected);
  const [open, setOpen] = useState(false);

  const { handleDeleteMultiple } = useActionSchoolWeek();

  const handleDelete = async () => {
    if (!selectedRows.length) {
      toast.warning("Vui lòng chọn tuần học để xóa");
      return;
    }
    await handleDeleteMultiple(selectedRows, () => {
      setOpen(false);
      onSuccess?.();
    });
  };

  return (
    <>
      <Button variant="outlined" color="error" onClick={() => setOpen(true)}>
        Xóa
      </Button>
      <AppConfirmModal
        isOpen={open}
        onClose={() => setOpen(false)}
        onConfirm={handleDelete}
        modalTitleProps={{
          title: "Bạn có xác nhận muốn xóa cấu hình tuần học này không?",
        }}
      />
    </>
  );
};

export default DeleteSchoolWeek;
