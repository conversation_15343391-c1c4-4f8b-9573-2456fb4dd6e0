import {
  inventoryTransactionActions,
  inventoryTransactionSelectors,
} from "@/app/(main)/thiet-bi/kiem-ke/inventoryTransaction.slice";
import { AppCheckbox, AppTable } from "@/components/common";
import { AppConstant } from "@/constant";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import { ITeacher } from "@/models/system.model";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { selectTeacherComboList } from "@/redux/system.slice";
import { CommonUtils } from "@/utils";
import { formatDayjsWithType } from "@/utils/format.utils";
import { Stack } from "@mui/material";
import { ColumnDef } from "@tanstack/react-table";
import React, { memo, useMemo } from "react";

const ChooseTeacherTable = () => {
  const teachers = useAppSelector(selectTeacherComboList);

  return (
    <Stack height={"100%"}>
      <AppTable
        columns={columns}
        data={teachers}
        totalData={teachers.length}
        hasDefaultPagination
        {...TABLE_MODAL_FULL_HEIGHT}
      />
    </Stack>
  );
};

export default memo(ChooseTeacherTable);

const columns: ColumnDef<ITeacher>[] = [
  {
    id: "select",
    accessorKey: "select",
    meta: {
      align: "center",
    },
    size: 50,
    header: ({ table }) => <HeaderCheckbox table={table} />,
    cell: ({ row }) => <RowCheckbox code={row.original.code} />,
  },
  {
    id: "code",
    header: "Mã giáo viên",
    accessorKey: "code",
    size: 100,
  },
  {
    id: "identityNumber",
    header: "Số CCCD/CMND",
    accessorKey: "identityNumber",
    size: 100,
  },
  {
    id: "fullName",
    header: "Tên giáo viên",
    accessorKey: "fullName",
    size: 150,
  },
  {
    id: "teacherGroupSubjectName",
    header: "Tổ bộ môn",
    accessorKey: "teacherGroupSubjectName",
    size: 120,
  },
  {
    id: "phone",
    header: "Số điện thoại",
    accessorKey: "phone",
    size: 100,
  },
  {
    id: "dateOfBirth",
    header: "Ngày sinh",
    accessorFn: (row) => formatDayjsWithType(row.dateOfBirth),
    size: 100,
  },
];

const HeaderCheckbox = ({ table }) => {
  const dispatch = useAppDispatch();
  const teacherIds = useAppSelector(inventoryTransactionSelectors.teacherIds);
  const teachers = useAppSelector(selectTeacherComboList);

  const isCheckedAll = useMemo(() => {
    return (
      teachers.every((item) => teacherIds.includes(item.id)) &&
      teachers.length !== 0
    );
  }, [teacherIds, teachers]);

  const handleCheckedAll = (_, checked: boolean) => {
    table.toggleAllPageRowsSelected(!!checked);
    dispatch(
      inventoryTransactionActions.setSelectedAllTeacherIds({
        checked,
        teacherChooseList: teachers,
      })
    );
  };

  return <AppCheckbox checked={isCheckedAll} onChange={handleCheckedAll} />;
};

const RowCheckbox = ({ code }) => {
  const dispatch = useAppDispatch();
  const teacherIds = useAppSelector(inventoryTransactionSelectors.teacherIds);
  const transactionTeams = useAppSelector(
    inventoryTransactionSelectors.transactionTeams
  );

  const idsExists = useMemo(() => {
    return transactionTeams.map((item) => item.teacherCode);
  }, [transactionTeams]);

  const handleCheckedRow = (code) => {
    dispatch(inventoryTransactionActions.setTeacherIds(code));
  };

  const isChecked = useMemo(() => {
    return teacherIds.includes(code);
  }, [teacherIds, code]);

  return (
    <AppCheckbox
      onChange={(_, value) => handleCheckedRow(code)}
      checked={isChecked || idsExists.includes(code)}
      disabled={idsExists.includes(code)}
    />
  );
};
