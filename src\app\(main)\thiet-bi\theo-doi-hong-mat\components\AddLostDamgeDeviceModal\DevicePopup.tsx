"use client";

import React, { memo, useState, useEffect, useCallback } from "react";
import { Popover } from "@mui/material";
import DeviceTable from "./DeviceTable";

interface DevicePopupProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectDevice: (device: any) => void;
  anchorEl?: HTMLElement | null;
}

const DevicePopup = ({
  isOpen,
  onClose,
  onSelectDevice,
  anchorEl,
}: DevicePopupProps) => {
  const handleSelectDevice = useCallback(
    (device: any) => {
      onSelectDevice(device);
      onClose();
    },
    [onSelectDevice]
  );

  return (
    <Popover
      open={isOpen}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "left",
      }}
      slotProps={{
        paper: {
          sx: {
            width: 800,
          },
        },
      }}
    >
      <DeviceTable onSelectDevice={handleSelectDevice} />
    </Popover>
  );
};

export default memo(DevicePopup);
