"use client";

import { Grid } from "@mui/material";
import React, { memo } from "react";
import AppSearchDebounceTextFiled from "../../../AppSearchDebounceTextFiled";
import {
  AppAutoComplete,
  AppDateRangePicker,
  IOption,
} from "@/components/common";
import { DEFAULT_FILTER_SIZE } from ".";
import {
  AppAutoCompleteProps,
  DEFAULT_UNIX,
} from "@/components/common/AppAutoComplete";
import { FilterConfig, FilterValueType } from "../../type";
import AppDatePicker from "@/components/common/AppDatePicker";
import { DateRangeValue } from "@/models/types";
import { useTableStore } from "../../table-store/TableContext";

type ListFilterProps = {
  filter: FilterConfig[] | null;
};

const ListFilter = <T,>({ filter }: ListFilterProps) => {
  const store = useTableStore<T>();

  const handleChangeFilter = store((state) => state.handleChangeFilter);
  const filterOptions = store((state) => state.filterOptions);

  return (
    <>
      {filter?.map((item) => (
        <Grid
          size={item.size ?? DEFAULT_FILTER_SIZE}
          key={(item.key as string) || (item.key?.[0] as string)}
        >
          {item.type === "text" && (
            <AppSearchDebounceTextFiled
              label={item.label}
              onChangeValue={handleChangeFilter(item.key as string)}
              valueInput={item.value as string}
              {...item?.fieldProps}
            />
          )}
          {item.type === "select" && (
            <AutoListFilter
              options={filterOptions?.[item.key as string] ?? []}
              label={item.label || ""}
              value={item.value}
              isMulti={item.isMulti}
              selectedKey={item?.selectedKey}
              onChange={(_, data) =>
                handleChangeFilter(item.key as string)(
                  item.isMulti
                    ? data
                    : (data as IOption)?.[item.selectedKey || DEFAULT_UNIX] ??
                        null
                )
              }
              hasAllOption={item?.hasAllOption ?? false}
              {...item?.fieldProps}
            />
          )}
          {item.type === "date" && (
            <AppDatePicker
              label={item.label}
              value={item.value}
              onChange={(value) =>
                handleChangeFilter(item.key as string)(value)
              }
              {...item?.fieldProps}
            />
          )}
          {item.type === "dateRange" && (
            <AppDateRangePicker
              label={item.label}
              value={item.value as DateRangeValue}
              onDateChange={(date) => handleChangeFilter(item.key)(date)}
              {...item?.fieldProps}
            />
          )}
        </Grid>
      ))}
    </>
  );
};

export default memo(ListFilter);

const AutoListFilter = ({
  options,
  label,
  value,
  isMulti,
  selectedKey,
  ...otherProps
}: {
  options: IOption[];
  label: string;
  isMulti?: boolean;
  value?: FilterValueType;
  selectedKey?: string;
} & Omit<AppAutoCompleteProps, "options" | "value" | "label">) => {
  let selectedValue: FilterValueType = null;

  if (isMulti) {
    if (Array.isArray(value)) {
      selectedValue = value;
    } else {
      selectedValue = options.filter(
        (opt) => value && opt?.[selectedKey || DEFAULT_UNIX] === value
      );
    }
  } else {
    if (typeof value === "object" && value !== null && DEFAULT_UNIX in value) {
      selectedValue = value as IOption;
    } else {
      selectedValue =
        typeof value !== undefined
          ? options.find(
              (opt) => opt?.[selectedKey || DEFAULT_UNIX] === value
            ) ?? null
          : null;
    }
  }

  return (
    <AppAutoComplete
      options={options}
      label={label}
      multiple={isMulti}
      value={selectedValue}
      limitTags={isMulti ? 1 : undefined}
      {...otherProps}
    />
  );
};
