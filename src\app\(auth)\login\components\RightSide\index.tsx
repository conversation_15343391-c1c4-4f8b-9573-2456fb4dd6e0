"use client";

import { <PERSON>, <PERSON><PERSON>, <PERSON>ack, Typography } from "@mui/material";
import { useForm } from "react-hook-form";
import { ILogin } from "../../login.model";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { DON_VI_TYPE } from "@/constant/data.const";
import { loginActions, loginSelectors } from "../../login.slice";
import { PhoneIcon } from "@/components/icons";
import { AppConstant, DataConstant } from "@/constant";
import InfoAccountPanel from "./InfoAccountPanel";
import ChooseDonViSelects from "./ChooseDonViSelects";
import { useEffect, useState } from "react";
import CryptoJS from "crypto-js";
import useLogin from "../../hooks/useLogin";
import Cookies from "js-cookie";

const RightSide = () => {
  const domainInfo = useAppSelector((state) => state.appReducer.domainInfo);
  const isFetching = useAppSelector(loginSelectors.getFetching);
  const soList = useAppSelector(loginSelectors.selectSoList);
  const dispatch = useAppDispatch();
  const handleLogin = useLogin();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { handleSubmit, setError, getValues, control, setValue } =
    useForm<ILogin>({
      defaultValues,
    });

  const saveCurrentInfo = () => {
    const secretKey = process.env.NEXT_PUBLIC_SECRET_KEY_USER_INFO;

    if (!secretKey) return;

    const data: Partial<ILogin> = { ...getValues() };
    delete data.password;

    const encryptedData = CryptoJS.AES.encrypt(
      JSON.stringify(getValues()),
      secretKey
    ).toString();

    // Lưu vào Local
    localStorage.setItem(AppConstant.COOKIE_KEY.ud, encryptedData);
  };

  const submitData = (data: ILogin) => {
    let schoolId =
      data.unitLevel?.id === DON_VI_TYPE.so
        ? data.doetCode?.code
        : data.unitLevel?.id === DON_VI_TYPE.phong
        ? data.divisionCode?.code
        : data.unitLevel?.id === DON_VI_TYPE.doitac
        ? data.donVi?.code
        : typeof data.schoolId === "object"
        ? data.schoolId?.id
        : data.schoolId;

    if (domainInfo?.isShareDomain === 0) {
      schoolId = domainInfo.id;
    }
    if (!schoolId) return;

    const { username, password } = data;

    Cookies.set(AppConstant.COOKIE_KEY.orgId, schoolId.toString());

    handleLogin({
      data: { username, password, schoolId },
      setError,
      setIsSubmitting,
      saveCurrentInfo,
    });
  };

  useEffect(() => {
    const secretKey = process.env.NEXT_PUBLIC_SECRET_KEY_USER_INFO;

    const storedData = localStorage.getItem(AppConstant.COOKIE_KEY.ud);
    if (storedData) {
      const decryptedData: ILogin = JSON.parse(
        CryptoJS.AES.decrypt(storedData, secretKey as string).toString(
          CryptoJS.enc.Utf8
        )
      );

      setValue("unitLevel", decryptedData.unitLevel || null);
      setValue("username", decryptedData.username || "");
      setValue("donVi", decryptedData.donVi || null);
      setValue("doetCode", decryptedData.doetCode || null);
      setValue("schoolId", decryptedData.schoolId || null);
      setValue("divisionCode", decryptedData.divisionCode || null);
    }

    dispatch(loginActions.getSoList());

    return () => {
      dispatch(loginActions.resetSoPhongTruong());
    };
  }, []);

  useEffect(() => {
    if (soList.length === 0) return;
    if (
      domainInfo?.groupUnitCode === DON_VI_TYPE.so ||
      domainInfo?.groupUnitCode === DON_VI_TYPE.phong
    ) {
      const currentLevel = DataConstant.DON_VI_LIST.find(
        (item) => item.id === domainInfo?.groupUnitCode
      );
      if (currentLevel) {
        setValue("unitLevel", {
          id: currentLevel?.id,
          label: `${currentLevel?.name} (${currentLevel?.id})`,
        });
      }
      if (domainInfo.groupUnitCode === DON_VI_TYPE.phong) {
        const currentDoet = soList.find(
          (item) => item.doetCode === domainInfo.doetCode
        );

        setValue("doetCode", {
          id: currentDoet?.id as number,
          code: currentDoet?.schoolCode,
          label: currentDoet?.name + " (" + currentDoet?.schoolCode + ")",
        });
      }
    }
  }, [domainInfo, soList]);

  return (
    <Box
      sx={{
        position: "absolute",
        top: 0,
        left: 640,
        right: 0,
        bottom: 0,
        minHeight: "100%",
        backgroundColor: "common.white",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        px: 3,
      }}
    >
      <Box width="397px" component="form" onSubmit={handleSubmit(submitData)}>
        {(Boolean(domainInfo?.isShareDomain) ||
          domainInfo?.groupUnitCode === DON_VI_TYPE.phong) && (
          <Stack spacing={1.25}>
            <Typography fontWeight={700} fontSize={18} lineHeight="24px">
              THÔNG TIN ĐƠN VỊ
            </Typography>
            <ChooseDonViSelects control={control} onSetValueForm={setValue} />
          </Stack>
        )}
        <Stack spacing={1.25} mt={4}>
          <Typography fontWeight={700} fontSize={18} lineHeight="24px">
            THÔNG TIN TÀI KHOẢN
          </Typography>
          <InfoAccountPanel control={control} />
        </Stack>

        <Button
          type="submit"
          sx={{
            mt: 2.5,
            "&.MuiButtonBase-root": {
              bgcolor: "primary.main",
            },
            "& .MuiLoadingButton-loadingIndicator": {
              color: "common.white !important",
            },
          }}
          disabled={isSubmitting}
          fullWidth
          variant="contained"
        >
          Đăng nhập
        </Button>
      </Box>
      <Typography
        variant="h4"
        sx={{
          position: "absolute",
          bottom: "50px",
          left: "50%",
          transform: "translateX(-50%)",
          textAlign: "center",
        }}
      >
        Copy right @ 2023 QuangIch. All right reserved
      </Typography>
      <Stack
        direction="row"
        alignItems="center"
        spacing={3}
        sx={{
          position: "absolute",
          top: 40,
          right: 60,
        }}
      >
        <PhoneIcon sx={{ fontSize: 32 }} />
        <Stack>
          <Typography color="primary.main" fontSize={14}>
            Hỗ trợ trực tuyến
          </Typography>
          <Typography fontWeight={700} fontSize={20} lineHeight="20.4px">
            19004740
          </Typography>
        </Stack>
      </Stack>
    </Box>
  );
};

export default RightSide;

const DEFAULT_LEVEL = DataConstant.DON_VI_LIST.find(
  (item) => item.id === DON_VI_TYPE.truong
);

const defaultValues: ILogin = {
  username: "",
  password: "",
  schoolId: null,
  unitLevel: DEFAULT_LEVEL,
  doetCode: null,
  divisionCode: null,
};
