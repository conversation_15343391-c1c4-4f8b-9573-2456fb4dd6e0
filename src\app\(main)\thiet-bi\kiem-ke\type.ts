import { IOption } from "@/components/common";
import { Dayjs } from "dayjs";

export interface IInventoryTransactionItems {
  id: number;
  deviceDefinitionId: number;
  deviceId: number;
  quantity: number;
  beforeBrokenTotal: number;
  beforeLostTotal: number;
  beforeAvailableTotal: number;
  afterBrokenTotal: number | null;
  afterLostTotal: number | null;
  afterAvailableTotal: number | null;
  afterChangeTotal: number | null;
  changeBrokenTotal: number;
  changeLostTotal: number;
  notes: string;
  roomId?: number;
  roomName?: string;
  deviceCode: string;
  deviceName: string;
  deviceUnitId?: number;
  deviceUnitName?: string;
  schoolDeviceTypeId?: number;
  schoolDeviceTypeName?: string;
  deviceDTITypeId?: number;
  deviceDTITypeName?: string;
  schoolSubjectId?: number;
  schoolSubjectName?: string;
  createdBy?: number;
  updatedBy?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IInventoryTransaction {
  id: number;
  documentNumber: string;
  fromDate: string;
  toDate: string;
  inventoryName: string;
  isManageType: ManageTypeEnum;
  scopeIds: number[];
  totalDevices: number;
  notes: string;
  inventoryTransactionItems: IInventoryTransactionItems[];
  transactionTeams: ITransactionTeam[];
}

export enum ManageTypeEnum {
  Room = 1,
  Subject = 2,
}

export interface ITransactionTeam {
  stt: number;
  teacherCode: string;
  teacherName: string;
  position: string;
  role: string;
  note: string;
  isTeamlead: boolean;
}

export interface ITransactionActionTeam {
  teacherCode: string;
  position?: string;
  role?: string;
  note?: string;
  isTeamlead?: boolean;

  // Client field
  isError?: boolean;
}

export interface IInventoryTransactionAction {
  documentNumber: string;
  inventoryName: string;
  fromDate: null | Dayjs;
  toDate: null | Dayjs;
  isManageType: ManageTypeEnum;
  scopeIds?: number[] | IOption[];
  totalDevices?: number;
  totalPrices?: number;
  notes: string;
  inventoryTransactionItems: IInventoryItems[];
  transactionTeams: ITransactionTeam[];
  deleteInventoryTransactionItemIds: number[];
}

export interface IInventoryItems {
  id?: number;
  deviceId: number;
  deviceDefinitionId?: number;
  quantity?: number;
  beforeBrokenTotal?: number;
  beforeLostTotal?: number;
  beforeAvailableTotal?: number;
  afterBrokenTotal?: number | null;
  afterLostTotal?: number | null;
  afterAvailableTotal?: number | null;
  afterChangeTotal?: number | null;
  changeBrokenTotal?: number;
  changeLostTotal?: number;
  notes?: string;

  // Client field
  isError?: boolean;
  isChange?: boolean;
}

export interface IInventoryDeviceParams {
  searchKey: string;
  roomIds: number[];
  schoolSubjectIds: number[];
}
