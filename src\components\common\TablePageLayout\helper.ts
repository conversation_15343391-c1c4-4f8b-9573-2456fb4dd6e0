import {
  DataListResponseModel,
  DataResponseModel,
} from "@/models/response.model";
import http from "@/api";
import { convertDataToOptions } from "@/utils/format.utils";
import { FilterConfig, FilterValueType } from "./type";
import dayjs, { Dayjs } from "dayjs";
import { AppConstant } from "@/constant";
import { DateRangeValue } from "@/models/types";
import { DEFAULT_UNIX } from "../AppAutoComplete";

export const handleGetApiOptions = async (
  filterConfig: FilterConfig[],
  onSuccess: (arr: Record<string, any[]>) => void,
  onFinish?: () => void
) => {
  try {
    const itemsWithApi = filterConfig?.filter((item) => item.apiListUrl) || [];

    const responses = await Promise.all(
      itemsWithApi.map((item) =>
        http.get<DataListResponseModel<any> | DataResponseModel<any>>(
          item.apiListUrl as string
        )
      )
    );

    const extractData = (res: any) =>
      Array.isArray(res?.data) ? res.data : res?.data?.data || [];

    const optionsMap = itemsWithApi.reduce((acc, item, index) => {
      acc[item.key] = convertDataToOptions(extractData(responses[index]));
      return acc;
    }, {} as Record<string, any[]>);

    filterConfig.forEach((item) => {
      if (!item.apiListUrl) {
        optionsMap[item.key] = item.options || [];
      }
    });

    onSuccess(optionsMap);
  } catch (error) {
    console.error("Failed to fetch filter options:", error);
  } finally {
    onFinish?.();
  }
};

export const getValueDate = (
  value?: string | Date | null | Dayjs | FilterValueType
) => {
  if (value && dayjs(value as string).isValid()) {
    return dayjs(value as string).format(AppConstant.DATE_TIME_YYYYescape);
  }
  return null;
};

export const buildFilterParamsFromConfig = (
  filter: FilterConfig[] | null,
  defaultMultiKey = DEFAULT_UNIX
): Record<string, any> => {
  const params: Record<string, any> = {};

  if (!filter?.length) return params;

  for (const item of filter) {
    const { key, value, type } = item;

    switch (type) {
      case "text":
        params[key as string] = value;
        break;

      case "select":
        if (item.isMulti && Array.isArray(value)) {
          params[key as string] = value.length
            ? value.map((v) => v?.[item.multiKey || defaultMultiKey])
            : [];
        } else {
          if (typeof value !== undefined) {
            params[key as string] = value;
          }
        }
        break;

      case "date":
        params[key as string] = getValueDate(value);
        break;

      case "dateRange":
        const [startKey, endKey] = item.keyDateRange || [];
        const [start, end] = (value as DateRangeValue) ?? [];

        if (startKey) {
          params[startKey] = getValueDate(start);
        }
        if (endKey) {
          params[endKey] = getValueDate(end);
        }
        break;

      default:
        // type undefined hoặc không hỗ trợ hiển thị, vẫn đẩy vào API
        params[key as string] = value;
        break;
    }
  }

  return params;
};
