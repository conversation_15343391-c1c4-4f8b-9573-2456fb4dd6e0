import http from "@/api";
import { ApiConstant } from "@/constant";
import { DataListResponseModel } from "@/models/response.model";
import { IDeviceTransfer, IHistoryDevice } from "./type";

export const getDeviceTransferService = (params) => {
  return http.get<DataListResponseModel<IDeviceTransfer>>(
    ApiConstant.TRANSFER_DEVICE,
    { params }
  );
};

export const getHistoryDeviceService = (id, params) => {
  return http.get<DataListResponseModel<IHistoryDevice>>(
    `${ApiConstant.HISTORY_DEVICE}/${id}`,
    { params }
  );
};
