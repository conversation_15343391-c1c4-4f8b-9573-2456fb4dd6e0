"use client";

import http from "@/api";
import {
  permissionActions,
  permissionSelectors,
} from "@/app/(main)/he-thong/phan-quyen/permission.slice";
import { IApplicationFeature } from "@/app/(main)/he-thong/phan-quyen/type";
import AppFormLayoutPanel from "@/components/common/AppFormLayoutPanel";
import { ApiConstant } from "@/constant";
import { APPLICATION_PERMISSION } from "@/constant/api.const";
import { DataListResponseModel } from "@/models/response.model";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { CommonUtils } from "@/utils";
import { extractErrorMessage } from "@/utils/common.utils";
import {
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Typography,
} from "@mui/material";
import React, { memo, useEffect, useState } from "react";
import { toast } from "sonner";

const ApplicationFeaturePanel = () => {
  const dispatch = useAppDispatch();
  const [applicationFeature, setApplicationFeature] = useState<
    IApplicationFeature[]
  >([]);
  const applicationFeatureId = useAppSelector(
    permissionSelectors.applicationFeatureId
  );

  useEffect(() => {
    const fetchApplicationFeature = async () => {
      CommonUtils.toggleAppProgress(true);
      try {
        const response: DataListResponseModel<IApplicationFeature> =
          await http.get(APPLICATION_PERMISSION);
        if (response.code === ApiConstant.ERROR_CODE_OK) {
          setApplicationFeature(response.data.data);
          if (response.data.data.length > 0) {
            dispatch(
              permissionActions.setApplicationFeatureId(
                response.data.data?.[0]?.id
              )
            );
          }
        } else {
          throw new Error(response.message || "Đã có lỗi xảy ra");
        }
      } catch (error) {
        const description = extractErrorMessage(error);
        toast.error("Thất bại!", {
          description,
        });
      } finally {
        CommonUtils.toggleAppProgress(false);
      }
    };
    fetchApplicationFeature();
  }, []);

  return (
    <AppFormLayoutPanel
      title="Chức năng ứng dụng"
      isDoc
      titleProps={{
        sx: { height: 52.8 },
        alignItems: "center",
      }}
      height={"100%"}
    >
      <List
        sx={{
          bgcolor: "common.white",
          width: "100%",
        }}
      >
        {applicationFeature.map((item) => {
          return (
            <ListItem
              key={item.id}
              sx={{
                ".MuiListItemButton-root:hover": {
                  backgroundColor: "rgba(0, 0, 0, 0.08)",
                },
                "& .Mui-selected": {
                  color: "primary.main",
                  background: "rgba(22, 173, 91, 0.08)",
                  fontWeight: 500,
                },
              }}
              disablePadding
            >
              <ListItemButton
                onClick={() =>
                  dispatch(permissionActions.setApplicationFeatureId(item.id))
                }
                selected={applicationFeatureId === item.id}
              >
                <ListItemText
                  primary={<Typography fontSize={16}>{item.name}</Typography>}
                />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>
    </AppFormLayoutPanel>
  );
};

export default memo(ApplicationFeaturePanel);
