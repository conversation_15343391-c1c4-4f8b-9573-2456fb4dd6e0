"use client";

import React, { memo, useCallback } from "react";
import { Popover } from "@mui/material";
import DeviceTable from "./DeviceTable";
import { useAppSelector } from "@/redux/hook";
import { selectorDeviceFilter } from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.slice";

interface DevicePopupProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectDevice: (device: any) => void;
  anchorEl?: HTMLElement | null;
}

const DevicePopup = ({
  isOpen,
  onClose,
  onSelectDevice,
  anchorEl,
}: DevicePopupProps) => {
  const deviceFilter = useAppSelector(selectorDeviceFilter);

  const handleSelectDevice = useCallback(
    (device: any) => {
      onSelectDevice(device);
      onClose();
    },
    [onClose, onSelectDevice]
  );

  return (
    <Popover
      open={isOpen}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "left",
      }}
      slotProps={{
        paper: {
          sx: {
            width: 800,
          },
        },
      }}
    >
      <DeviceTable
        onSelectDevice={handleSelectDevice}
        deviceFilter={deviceFilter}
      />
    </Popover>
  );
};

export default memo(DevicePopup);
