import { deviceActions } from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.slice";
import { AppSearchDebounceTextFiled } from "@/components/common";
import { FilterCustomProps } from "@/components/common/TablePageLayout/ContentPage/HeaderFilter";
import { DataConstant } from "@/constant";
import { IDevice } from "@/models/eduDevice.model";
import { useAppDispatch } from "@/redux/hook";
import {
  Box,
  FormControlLabel,
  FormLabel,
  Grid,
  Radio,
  RadioGroup,
} from "@mui/material";
import React, { useCallback, useMemo } from "react";

const FilterCustom = ({ props }: { props: FilterCustomProps }) => {
  const dispatch = useAppDispatch();

  const value = useMemo(() => {
    return props.filter?.find((item) => item.key === "isGroupByDefinition")
      ?.value as number;
  }, [props.filter]);

  const handleChangeIsGroupByDefinition = useCallback(
    (value: number) => {
      dispatch(deviceActions.setIsGroupByDefinition(value));
      props.onChangeFilter("isGroupByDefinition")(value);
      dispatch(deviceActions.resetRowSelected());
    },
    [dispatch, props.onChangeFilter]
  );

  return (
    <>
      <Grid>
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            gap: 2,
            flexWrap: "wrap",
          }}
        >
          <FormLabel>Hiển thị DS theo:</FormLabel>
          <RadioGroup
            row
            value={value ?? DataConstant.BOOLEAN_TYPE.true}
            onChange={(_, value) => {
              handleChangeIsGroupByDefinition(Number(value));
            }}
          >
            <FormControlLabel
              value={DataConstant.BOOLEAN_TYPE.true}
              control={
                <Radio checked={value === DataConstant.BOOLEAN_TYPE.true} />
              }
              label="Tổng đầu TB"
            />
            <FormControlLabel
              value={DataConstant.BOOLEAN_TYPE.false}
              control={
                <Radio checked={value === DataConstant.BOOLEAN_TYPE.false} />
              }
              label="Kho/Phòng"
            />
          </RadioGroup>
        </Box>
      </Grid>
      <Grid size={3.5}>
        <AppSearchDebounceTextFiled
          placeholder="Tìm kiếm thiết bị"
          valueInput={
            props.filter?.find((item) => item.key === "searchKey")
              ?.value as string
          }
          onChangeValue={(value) => {
            props.onChangeFilter("searchKey")(value);
          }}
        />
      </Grid>
    </>
  );
};

export default FilterCustom;
