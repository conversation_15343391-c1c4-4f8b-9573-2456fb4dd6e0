"use client";

import { But<PERSON> } from "@mui/material";
import React, { memo, useState } from "react";
import CreateModal from "@/app/(main)/muon-tra/ke-hoach-muon/components/CreateModal";

const BorrowRoom = ({
  fetchCurrentData,
}: {
  fetchCurrentData?: () => void;
}) => {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Button variant="contained" color="primary" onClick={() => setOpen(true)}>
        Mượn kho/phòng
      </Button>
      <CreateModal
        isOpen={open}
        onClose={() => setOpen(false)}
        fetchCurrentData={fetchCurrentData}
        defaultDelayedTab={2}
      />
    </>
  );
};

export default memo(BorrowRoom);
