import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import { useField<PERSON>rray, useFormContext, useWatch } from "react-hook-form";
import { IBorrowRequestAction } from "../../../borrowRequestModel";
import { capitalizeFirstLetter, WeeklyBorrowDay } from "./WeekDeviceSelected";
import dayjs from "dayjs";
import AppAutoComplete, {
  getOptionId,
  IOption,
} from "@/components/common/AppAutoComplete";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
dayjs.extend(isSameOrBefore);
dayjs.locale("vi");
import { v4 as uuid } from "uuid";
import { Button, Stack, Typography } from "@mui/material";
import { PlusIcon } from "@/components/icons";
import DeleteCell from "@/components/common/table/cell/DeleteCell";
import { ColumnDef } from "@tanstack/react-table";
import AppTable from "@/components/common/table/AppTable";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import { AppFormAutocomplete } from "@/components/common";
import { useAppSelector } from "@/redux/hook";
import {
  selectClassList,
  selectPeriodList,
  selectRoomList,
} from "@/redux/system.slice";
import { BORROW_TYPE, BorrowStatusEnum } from "@/models/eduDevice.model";
import { ArrowDropDownIcon } from "@mui/x-date-pickers";
import { selectHasBorrowRequestOfTeacher } from "@/redux/device/borrowRequest.slice";

const WeekRoomSelected = ({ isEdit }) => {
  const { control } = useFormContext<IBorrowRequestAction>();
  const schoolWeek = useWatch({
    control,
    name: "schoolWeekConfigId",
  });
  const { fields, append, remove, update } = useFieldArray({
    control,
    name: "borrowRequestRooms",
  });
  const isShowCollapse = useAppSelector(selectHasBorrowRequestOfTeacher);
  const treeData: WeeklyBorrowDay<RoomBorrowItem>[] = useMemo(() => {
    if (!schoolWeek?.fromDate || !schoolWeek?.toDate) return [];

    const from = dayjs(schoolWeek.fromDate);
    const to = dayjs(schoolWeek.toDate);
    const days: WeeklyBorrowDay<RoomBorrowItem>[] = [];

    let curr = from;

    while (curr.isSameOrBefore(to, "day")) {
      const date = curr.toDate();
      const id = curr.format("YYYY-MM-DD");

      // lấy các phòng mượn đúng ngày
      const dayItems = fields
        .map((f, index) => ({
          ...f,
          _index: index,
          subjectId: getOptionId(f.subjectId),
          roomId: getOptionId(f.roomId),
          roomDeviceGuid: f.roomDeviceGuid ?? null,
        }))
        .filter((f) => dayjs(f.fromDate).isSame(date, "day"));

      // nếu rỗng → tạo hàng "ảo"
      const children =
        dayItems.length > 0
          ? dayItems.map((f) => ({
              ...f,
              id: uuid(),
              parentId: id,
            }))
          : [
              {
                id: uuid(),
                parentId: id,
                subjectId: null,
                periodIds: [],
                schoolClassIds: [],
                roomId: null,
                fromDate: date,
                toDate: date,
                roomDeviceGuid: null,
                status: BorrowStatusEnum.Register,
                borrowType: BORROW_TYPE.week,
                _index: -1, // Hàng ảo (không thuộc RHF)
              },
            ];

      days.push({
        id,
        parentId: 0,
        date,
        name: capitalizeFirstLetter(curr.format("dddd (DD/MM)")),
        children,
      });

      curr = curr.add(1, "day");
    }

    return days;
  }, [schoolWeek, fields]);

  const handleAddDevice = useCallback(
    (date: Date) => {
      const id = uuid();
      append({
        id,
        subjectId: null,
        periodIds: [],
        schoolClassIds: [],
        roomId: null,
        fromDate: date,
        toDate: date,
        roomName: "",
        purpose: null,
        roomDeviceGuid: id,
        borrowType: BORROW_TYPE.week,
        status: BorrowStatusEnum.Register,
      });
    },
    [append]
  );

  const handleDelete = useCallback(
    (index: number) => {
      remove(index);
    },
    [remove]
  );

  const expandedState = useMemo(() => {
    return treeData.reduce((acc, row) => {
      const hasRealChildren = row.children?.some((r) => r._index !== -1);
      if (hasRealChildren) acc[row.id] = true;
      return acc;
    }, {});
  }, [treeData]);

  const [expanded, setExpanded] = useState({});

  useEffect(() => {
    if (!isEdit && !isShowCollapse) return;

    setExpanded(expandedState);
  }, [expandedState, isEdit, isShowCollapse]);

  const columns = useMemo(
    () => getColumns(handleAddDevice, handleDelete, append, expanded),
    [handleAddDevice, handleDelete, append, expanded]
  );

  return (
    <AppTable
      columns={columns}
      data={treeData}
      totalData={treeData.length}
      options={{
        state: { expanded },
        onExpandedChange: setExpanded,
        getSubRows: (row) =>
          row.children as unknown as
            | WeeklyBorrowDay<RoomBorrowItem>[]
            | undefined,
      }}
      {...TABLE_MODAL_FULL_HEIGHT}
    />
  );
};

type RoomBorrowItem = {
  id: string;
  roomId: number | null;
  periodIds: number[];
  subjectId: number | null;
  schoolClassIds: number[];
  fromDate: Date;
  toDate: Date;
  roomDeviceGuid: string | null;
  _index: number;
};

export default WeekRoomSelected;

const getColumns = (
  onAdd: (date: Date) => void,
  onDelete: (index: number) => void,
  append,
  expanded
): ColumnDef<any>[] => [
  {
    id: "delete",
    header: "",
    size: 50,
    meta: {
      align: "center",
    },
    cell: ({ row }) =>
      row.depth === 1 ? (
        <DeleteCell
          disabled={row.original.status !== BorrowStatusEnum.Register}
          onClick={() => onDelete(row.original._index)}
        />
      ) : null,
  },
  {
    id: "periodIds",
    header: "Tiết học",
    accessorKey: "periodIds",
    size: 250,
    cell: ({ row }) => <RoomCell append={append} row={row} onAdd={onAdd} />,
    meta: {
      colSpanOnParentRow: 6,
    },
  },
  {
    id: "room",
    header: "Kho/phòng",
    cell: ({ row }) =>
      row.depth === 1 ? <RoomSelectEditForm row={row} append={append} /> : null,
    size: 350,
  },
  {
    id: "grade",
    header: "Lớp",
    accessorKey: "gradeCode",
    size: 250,
    cell: ({ row }) =>
      row.depth === 1 ? <GradeCell append={append} row={row} /> : null,
  },
  {
    id: "empty",
    accessorKey: "empty",
    header: "",
    size: 1,
    meta: {
      headerSx: {
        width: "100%",
      },
    },
  },
];

export const PeriodIdCell = memo(
  ({ row, append }: { row: any; append: any }) => {
    const periodList = useAppSelector(selectPeriodList);
    const { control } = useFormContext();

    const [selectedPeriods, setSelectedPeriods] = useState<any[]>([]);

    const handleAddRow = () => {
      if (selectedPeriods.length) {
        const newItem = {
          ...row.original,
          id: uuid(),
          periodIds: selectedPeriods ?? [],
        };
        append(newItem);
        setSelectedPeriods([]);
      }
    };

    if (row.original._index === -1) {
      return (
        <AppAutoComplete
          options={periodList}
          disabled={row.original.status !== BorrowStatusEnum.Register}
          hasAllOption={true}
          multiple={true}
          limitTags={1}
          disableCloseOnSelect={true}
          onChange={(_, value) => {
            setSelectedPeriods((value as any) ?? []);
          }}
          onClose={() => {
            handleAddRow();
          }}
        />
      );
    }

    return (
      <AppFormAutocomplete
        autocompleteProps={{
          hasAllOption: true,
          multiple: true,
          limitTags: 1,
          disableCloseOnSelect: true,
          disabled: row.original.status !== BorrowStatusEnum.Register,
        }}
        name={`borrowRequestRooms.${row.original._index}.periodIds`}
        control={control}
        options={periodList}
      />
    );
  }
);
export const GradeCell = memo(
  ({ row, append }: { row: any; append: (value: any) => void }) => {
    const classList = useAppSelector(selectClassList);
    const { control } = useFormContext();

    const isVirtual = row.original._index === -1;

    // ✅ Lưu lại các lớp đã chọn tạm
    const [selectedClasses, setSelectedClasses] = useState<any[]>([]);

    // ✅ Xử lý thêm dòng khi autocomplete đóng
    const handleAddRow = () => {
      const newItem = {
        ...row.original,
        id: uuid(),
        schoolClassIds: selectedClasses,
      };
      append(newItem);
      setSelectedClasses([]);
    };

    if (isVirtual) {
      return (
        <AppAutoComplete
          options={classList}
          hasAllOption={true}
          multiple={true}
          limitTags={1}
          disableCloseOnSelect={true}
          value={selectedClasses}
          onChange={(_, value) => {
            setSelectedClasses((value as any[]) ?? []);
          }}
          onClose={handleAddRow}
        />
      );
    }

    return (
      <AppFormAutocomplete
        autocompleteProps={{
          hasAllOption: true,
          multiple: true,
          limitTags: 1,
          disabled: row.original.status !== BorrowStatusEnum.Register,
        }}
        name={`borrowRequestRooms.${row.original._index}.schoolClassIds`}
        control={control}
        options={classList}
      />
    );
  }
);

export const RoomSelectEditForm = memo(
  ({ row, append }: { row: any; append: (value: any) => void }) => {
    const roomList = useAppSelector(selectRoomList);
    const { control } = useFormContext();

    const isVirtual = row.original._index === -1;

    if (isVirtual) {
      return (
        <AppAutoComplete
          options={roomList}
          disabled={row.original.status !== BorrowStatusEnum.Register}
          onChange={(_, value) => {
            if (!value) return;
            const newItem = {
              ...row.original,
              roomId: value,
              roomName: (value as IOption).label,
              _index: undefined,
            };
            append(newItem);
          }}
        />
      );
    }

    return (
      <AppFormAutocomplete
        control={control}
        name={`borrowRequestRooms.${row.original._index}.roomId`}
        autocompleteProps={{
          disabled: row.original.status !== BorrowStatusEnum.Register,
        }}
        options={roomList}
      />
    );
  }
);

const RoomCell = ({ row, onAdd, append }) => {
  const [_, forceRender] = useState({});

  const toggleExpand = () => {
    row.toggleExpanded();
    forceRender({});
  };

  return row.depth === 0 ? (
    <Stack
      onClick={toggleExpand}
      sx={{
        cursor: "pointer",
        borderLeft: "4px solid",
        borderColor: "primary.main",
        pl: 0.5,
      }}
      direction="row"
      alignItems="center"
      spacing={2}
    >
      <ArrowDropDownIcon
        sx={{
          color: "primary.main",
          transform: row.getIsExpanded() ? "rotate(180deg)" : "rotate(0)",
          transition: "transform 0.2s ease",
        }}
      />
      <Typography sx={{ width: 150, fontWeight: 500 }}>
        {row.original.name}
        <Typography color="primary" component="span" fontWeight={500}>
          {" "}
          (
          {row.original.children.filter((item) => item._index !== -1).length > 0
            ? row.original.children.length
            : 0}
          )
        </Typography>
      </Typography>
      {row.getIsExpanded() && (
        <Button
          variant="outlined"
          color="secondary"
          size="small"
          onClick={(e) => {
            e.stopPropagation();

            if (!row.getIsExpanded()) {
              row.toggleExpanded(true);
              forceRender({});
            }
            onAdd(row.original.date);
          }}
          startIcon={<PlusIcon />}
        >
          Thêm phòng
        </Button>
      )}
    </Stack>
  ) : (
    <PeriodIdCell row={row} append={append} />
  );
};
