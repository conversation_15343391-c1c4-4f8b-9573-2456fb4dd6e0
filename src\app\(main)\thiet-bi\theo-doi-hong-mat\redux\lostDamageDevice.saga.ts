import { createInjectable<PERSON>aga } from "@/saga/injectableSaga";
import { call, put, takeEvery, takeLatest } from "redux-saga/effects";
import { toast } from "sonner";
import { PayloadAction } from "@reduxjs/toolkit";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import { ApiConstant, EnvConstant } from "@/constant";
import {
  submitLostDamageDeviceService,
  ILostDamageDevicePayload,
  updateLostDamageDeviceService,
  fixLostDamageDeviceService,
} from "../services/lostDamagedDvice.service";
import {
  addLostDamageDevice,
  addLostDamageDeviceSuccess,
  updateLostDamageDevice,
  updateLostDamageDeviceSuccess,
  fixLostDamageDevice,
  fixLostDamageDeviceSuccess,
} from "./lostDamageDevice.slice";
import { IAddLostDamageDeviceAction } from "../lostDamgeDevice.model";
import { DataResponseModel } from "@/models/response.model";
import http from "@/api";
import { DEVICE_ISSUE_FIX } from "@/constant/api.const";
import stringFormat from "string-format";

function* handleAddLostDamageDevice(
  action: PayloadAction<IAddLostDamageDeviceAction>
) {
  try {
    toggleAppProgress(true);
    const { data, onSuccess } = action.payload;

    const payload: ILostDamageDevicePayload = {
      deviceId: Number(data.deviceId),
      //TODO: check later
      reportedDate: data.reportedDate,
      totalBroken: Number(data.totalBroken) || 0,
      totalLost: Number(data.totalLost) || 0,
      notes: data.notes || "",
    };

    const response: DataResponseModel<unknown> = yield call(
      submitLostDamageDeviceService,
      payload
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(addLostDamageDeviceSuccess(response));
      toast.success("Thêm thiết bị hỏng/mất thành công!");
      onSuccess?.();
    } else {
      throw response;
    }
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

function* handleUpdateLostDamageDevice(
  action: PayloadAction<{
    id: number;
    data: ILostDamageDevicePayload;
    onSuccess: () => void;
  }>
) {
  try {
    toggleAppProgress(true);
    const { id, data, onSuccess } = action.payload;

    const payload: ILostDamageDevicePayload = {
      deviceId: Number(data.deviceId),
      //TODO: check later
      reportedDate: data.reportedDate,
      totalBroken: Number(data.totalBroken) || 0,
      totalLost: Number(data.totalLost) || 0,
      notes: data.notes || "",
    };

    const response: DataResponseModel<unknown> = yield call(
      updateLostDamageDeviceService,
      id,
      payload
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(updateLostDamageDeviceSuccess(response));
      toast.success("Cập nhật thiết bị hỏng/mất thành công!");
      onSuccess?.();
    } else {
      throw response;
    }
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

function* handleFixLostDamageDevice(
  action: PayloadAction<{
    id: number;
    data: { totalFixed: number };
    onSuccess: () => void;
  }>
) {
  try {
    toggleAppProgress(true);
    const { id, data, onSuccess } = action.payload;

    const payload: { totalFixed: number } = {
      totalFixed: data.totalFixed,
    };

    const response: DataResponseModel<unknown> = yield call(
      fixLostDamageDeviceService,
      id,
      payload
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(fixLostDamageDeviceSuccess(response));
      toast.success("Sửa chữa thiết bị hỏng/mất thành công!");
      onSuccess?.();
    } else {
      throw response;
    }
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

export function* deviceSaga() {
  yield takeEvery(addLostDamageDevice.type, handleAddLostDamageDevice);
  yield takeEvery(updateLostDamageDevice.type, handleUpdateLostDamageDevice);
  yield takeLatest(fixLostDamageDevice.type, handleFixLostDamageDevice);
}

export const injectableDeviceSaga = createInjectableSaga("device", deviceSaga);
