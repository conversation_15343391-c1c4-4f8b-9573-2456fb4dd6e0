import RangeField from "@/app/(main)/thiet-bi/kiem-ke/components/form/RangeField";
import {
  IInventoryTransactionAction,
  ManageTypeEnum,
} from "@/app/(main)/thiet-bi/kiem-ke/type";
import {
  AppFormAutocomplete,
  AppFormRadio,
  AppFormTextField,
  GridFormContainer,
} from "@/components/common";
import AppFormLayoutPanel from "@/components/common/AppFormLayoutPanel";
import AppFormDatePicker from "@/components/common/form/AppFormDatePicker";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import {
  selectRoomList,
  selectSubjectList,
  systemActions,
} from "@/redux/system.slice";
import { Grid } from "@mui/material";
import { memo, useEffect } from "react";
import { useFormContext, useWatch } from "react-hook-form";

const InformationForm = () => {
  const dispatch = useAppDispatch();
  const {
    control,
    setValue,
    formState: { errors },
  } = useFormContext<IInventoryTransactionAction>();

  const fromDate = useWatch({
    control,
    name: "fromDate",
  });

  useEffect(() => {
    dispatch(systemActions.getRoomList());
    dispatch(systemActions.getSubjectList());
  }, []);

  return (
    <AppFormLayoutPanel alignItems="flex-start" title="Thông tin chứng từ">
      <GridFormContainer>
        <Grid size={3}>
          <AppFormTextField
            name="documentNumber"
            label="Số chứng từ"
            control={control}
            direction="row"
            labelProps={{
              sx: {
                overflow: "initial",
                minWidth: 90,
              },
            }}
            rules={{
              required: "Số chứng từ không được bỏ trống",
            }}
            textfieldProps={{
              error: Boolean(errors.documentNumber),
              helperText: errors.documentNumber?.message,
            }}
          />
        </Grid>
        <Grid size={3}>
          <AppFormTextField
            name="inventoryName"
            label="Tên đợt kiểm kê"
            control={control}
            direction="row"
            labelProps={{
              sx: {
                overflow: "initial",
                minWidth: 110,
              },
            }}
            rules={{
              required: "Tên đợt kiểm kê không được bỏ trống",
            }}
            textfieldProps={{
              error: Boolean(errors.inventoryName),
              helperText: errors.inventoryName?.message,
            }}
          />
        </Grid>
        <Grid size={3}>
          <AppFormDatePicker
            name="fromDate"
            label="Từ ngày"
            control={control}
            direction="row"
            labelProps={{
              sx: {
                overflow: "initial",
                minWidth: 80,
              },
            }}
            rules={{
              required: "Từ ngày không được bỏ trống",
            }}
            datePickerProps={{
              maxDate: null,
              slotProps: {
                textField: {
                  error: Boolean(errors.fromDate),
                  helperText: errors.fromDate?.message,
                },
              },
            }}
          />
        </Grid>
        <Grid size={3} />
        <Grid size={3}>
          <AppFormRadio
            name="isManageType"
            label="Kiểm kê theo"
            control={control}
            direction="row"
            formLabelProps={{
              sx: {
                overflow: "initial",
                minWidth: 90,
              },
            }}
            height="100%"
            spacing={1}
            onChangeValueForm={(_) => {
              setValue("scopeIds", []);
            }}
            radioList={[
              {
                label: "Kho/Phòng",
                id: ManageTypeEnum.Room,
              },
              {
                label: "Môn học",
                id: ManageTypeEnum.Subject,
              },
            ]}
          />
        </Grid>
        <Grid size={3}>
          <RangeField />
        </Grid>
        <Grid size={3}>
          <AppFormDatePicker
            name="toDate"
            label="Đến ngày"
            control={control}
            rules={{
              required: "Đến ngày không được bỏ trống",
              validate: (value) => {
                if (value && fromDate && value < fromDate) {
                  return "Đến ngày phải lớn hơn từ ngày";
                }
                return true;
              },
            }}
            direction="row"
            labelProps={{
              sx: {
                overflow: "initial",
                minWidth: 80,
              },
            }}
            datePickerProps={{
              minDate: fromDate,
              maxDate: null,
              slotProps: {
                textField: {
                  error: Boolean(errors.toDate),
                  helperText: errors.toDate?.message,
                },
              },
            }}
          />
        </Grid>
        <Grid size={3} />
        <Grid size={6}>
          <AppFormTextField
            direction="row"
            labelProps={{
              sx: {
                overflow: "initial",
                minWidth: 90,
              },
            }}
            name="notes"
            label="Nội dung"
            control={control}
          />
        </Grid>
      </GridFormContainer>
    </AppFormLayoutPanel>
  );
};

export default memo(InformationForm);
