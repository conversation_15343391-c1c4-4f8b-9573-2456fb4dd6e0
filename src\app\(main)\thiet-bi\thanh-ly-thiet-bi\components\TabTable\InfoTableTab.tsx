import { AppTable, AppTextField } from "@/components/common";
import DeleteCell from "@/components/common/table/cell/DeleteCell";
import { AppConstant } from "@/constant";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { CommonUtils, FormatUtils } from "@/utils";
import { Typography } from "@mui/material";
import { ColumnDef } from "@tanstack/react-table";
import { memo, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { IDevicesAction } from "../../deviceLiquidation.model";
import {
  deviceLiquidationActions,
  selectDevices,
  selectorDeviceAction,
} from "../../deviceLiquidation.slice";

const DeviceTable = ({ tableProps }) => {
  const dispatch = useAppDispatch();
  const devices = useAppSelector(selectDevices);

  const columns = getColumns((id) => {
    dispatch(deviceLiquidationActions.deleteDevice(id));
  });

  return (
    <AppTable
      columns={columns}
      data={devices}
      totalData={devices?.length}
      hasDefaultPagination
      {...tableProps}
    />
  );
};

export default memo(DeviceTable);

const getColumns = (onDelete): ColumnDef<IDevicesAction>[] => [
  {
    id: "index",
    header: "STT",
    cell: ({ row }) => {
      return <Typography>{row.index + 1}</Typography>;
    },
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "delete",
    header: "Xóa",
    cell: ({ row }) => <DeleteCell onClick={() => onDelete(row.original.id)} />,
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "code",
    header: "Mã thiết bị",
    accessorKey: "code",
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
  },
  {
    id: "roomName",
    header: "Kho/phòng",
    accessorKey: "roomName",
  },
  {
    id: "deviceUnitName",
    header: "Đơn vị tính",
    accessorKey: "deviceUnitName",
  },
  {
    id: "deviceInput",
    header: "SL giảm theo",
    meta: {
      align: "center",
    },
    columns: [
      {
        id: "totalBroken",
        header: "Hỏng",
        accessorKey: "totalBroken",
        cell: ({ row }) => <BrokenInput rowId={row.original.id} />,
      },
      {
        id: "totalAvailable",
        header: "Còn SD",
        accessorKey: "totalAvailable",
        cell: ({ row }) => <AvailableInput rowId={row.original.id} />,
      },
    ],
  },
  {
    id: "total",
    header: "Tổng SL giảm",
    cell: ({ row }) => <TotalInput rowId={row.original.id} />,
    meta: {
      align: "right",
    },
  },
];

const BrokenInput = memo(({ rowId }: { rowId: number }) => {
  const dispatch = useAppDispatch();
  const device = useAppSelector(selectorDeviceAction(rowId));
  const [value, setValue] = useState(device?.totalBroken ?? 0);
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    if (device?.totalBroken !== undefined && device.totalBroken !== value) {
      setValue(device.totalBroken);
    }
  }, [device?.totalBroken]);

  const changeValueDebounce = useMemo(
    () =>
      CommonUtils.debounce((newValue: number) => {
        if (!device) return;

        if (newValue > (device.canBroken ?? 0)) {
          setIsError(true);
          toast.warning("Cảnh báo!", {
            description: `Số lượng hỏng không được vượt quá ${
              device.canBroken ?? 0
            } cho thiết bị ${device.code ?? ""}`,
          });
          dispatch(
            deviceLiquidationActions.changeDeviceInput({
              id: rowId,
              isError: true,
            })
          );
        } else {
          setIsError(false);
          dispatch(
            deviceLiquidationActions.changeDeviceInput({
              id: rowId,
              totalBroken: newValue,
              isError: false,
            })
          );
        }
      }, AppConstant.DEBOUNCE_TIME_IN_MILLISECOND),
    [device, rowId, dispatch]
  );

  const handleChange = (val: string) => {
    const number = Number(val);
    setValue(number);
    changeValueDebounce(number);
  };

  return (
    <AppTextField
      type="number"
      value={value}
      error={isError}
      disabled={device?.canBroken === 0}
      slotProps={{ htmlInput: { min: 0 } }}
      onChange={(e) => handleChange(e.currentTarget.value)}
    />
  );
});

const AvailableInput = memo(({ rowId }: { rowId: number }) => {
  const dispatch = useAppDispatch();
  const device = useAppSelector(selectorDeviceAction(rowId));
  const [value, setValue] = useState(device?.totalAvailable ?? 0);
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    if (
      device?.totalAvailable !== undefined &&
      device.totalAvailable !== value
    ) {
      setValue(device.totalAvailable);
    }
  }, [device?.totalAvailable]);

  const changeValueDebounce = useMemo(
    () =>
      CommonUtils.debounce((newValue: number) => {
        if (!device) return;

        if (newValue > (device.canAvailable ?? 0)) {
          setIsError(true);
          toast.warning("Cảnh báo!", {
            description: `Số lượng còn SD không được vượt quá ${
              device.canAvailable ?? 0
            } cho thiết bị ${device.code}`,
          });
          dispatch(
            deviceLiquidationActions.changeDeviceInput({
              id: rowId,
              isError: true,
            })
          );
        } else {
          setIsError(false);
          dispatch(
            deviceLiquidationActions.changeDeviceInput({
              id: rowId,
              totalAvailable: newValue,
              isError: false,
            })
          );
        }
      }, AppConstant.DEBOUNCE_TIME_IN_MILLISECOND),
    [device, rowId, dispatch]
  );

  const handleChange = (val: string) => {
    const number = Number(val);
    setValue(number);
    changeValueDebounce(number);
  };

  return (
    <AppTextField
      type="number"
      value={value}
      error={isError}
      disabled={device?.canAvailable === 0}
      slotProps={{ htmlInput: { min: 0 } }}
      onChange={(e) => handleChange(e.currentTarget.value)}
    />
  );
});

const TotalInput = memo(({ rowId }: { rowId: number }) => {
  const device = useAppSelector(selectorDeviceAction(rowId));

  return (
    <Typography>
      {FormatUtils.formatNumber(
        (device?.totalAvailable || 0) + (device?.totalBroken || 0) + 0,
        0
      )}
    </Typography>
  );
});
