import http from "@/api";
import { convertToInventoryItemPayload } from "@/app/(main)/thiet-bi/kiem-ke/helper";
import { inventoryTransactionSelectors } from "@/app/(main)/thiet-bi/kiem-ke/inventoryTransaction.slice";
import { ApiConstant, AppConstant, EnvConstant } from "@/constant";
import { DataResponseModel } from "@/models/response.model";
import { useAppSelector } from "@/redux/hook";
import { CommonUtils } from "@/utils";
import dayjs from "dayjs";
import { toast } from "sonner";

const useInventoryActions = () => {
  const deleteInventoryTransactionItemIds = useAppSelector(
    inventoryTransactionSelectors.deleteInventoryTransactionItemIds
  );
  const inventoryActionItems = useAppSelector(
    inventoryTransactionSelectors.inventoryActionItems
  );
  const transactionActionTeams = useAppSelector(
    inventoryTransactionSelectors.transactionActionTeams
  );

  const validateData = () => {
    // if (!inventoryActionItems.length) {
    //   toast.warning("Vui lòng chọn thiết bị để kiểm kê");
    //   return false;
    // }

    // if (!transactionActionTeams.length) {
    //   toast.warning("Vui lòng chọn thành viên của ban kiểm kê");
    //   return false;
    // }

    const isError =
      inventoryActionItems.some((item) => item.isError) ||
      transactionActionTeams.some((item) => item.isError);
    if (isError) {
      toast.warning(
        "Số lượng thiết bị hỏng hoặc mất nhập không hợp lệ, vui lòng kiểm tra lại."
      );
      return false;
    }

    return true;
  };

  const handleCreateInventory = async (data, onSuccess?: () => void) => {
    try {
      if (!validateData()) {
        return;
      }

      const response: DataResponseModel<any> = await http.post(
        ApiConstant.INVENTORY_TRANSACTION,
        {
          ...data,
          isManageType: Number(data.isManageType),
          fromDate: data.fromDate
            ? dayjs(data.fromDate).format(AppConstant.DATE_TIME_YYYYescape)
            : null,
          toDate: data.toDate
            ? dayjs(data.toDate).format(AppConstant.DATE_TIME_YYYYescape)
            : null,
          scopeIds: data.scopeIds.map((item) => Number(item.id)),
          transactionTeams: transactionActionTeams,
          inventoryTransactionItems: convertToInventoryItemPayload(
            inventoryActionItems.filter((item) => item.isChange)
          ),
          deleteInventoryTransactionItemIds,
        }
      );
      if (response.code === ApiConstant.ERROR_CODE_OK) {
        toast.success("Thành công", {
          description: "Tạo phiếu kiểm kê thiết bị thành công",
        });
        onSuccess?.();
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      EnvConstant.IS_DEV && console.log(error);
      toast.error("Thất bại!", {
        description: CommonUtils.extractErrorMessage(error),
      });
    }
  };

  const handleUpdateInventory = async (data, onSuccess?: () => void) => {
    try {
      if (!validateData()) {
        return;
      }

      if (!data.id) {
        throw new Error("Phiếu kiểm kê không tồn tại");
      }

      const response: DataResponseModel<any> = await http.put(
        `${ApiConstant.INVENTORY_TRANSACTION}/${data.id}`,
        {
          ...data,
          isManageType: Number(data.isManageType),
          fromDate: data.fromDate
            ? dayjs(data.fromDate).format(AppConstant.DATE_TIME_YYYYescape)
            : null,
          toDate: data.toDate
            ? dayjs(data.toDate).format(AppConstant.DATE_TIME_YYYYescape)
            : null,
          scopeIds: data.scopeIds.map((item) => Number(item.id)),
          transactionTeams: transactionActionTeams,
          inventoryTransactionItems: convertToInventoryItemPayload(
            inventoryActionItems.filter((item) => item.isChange)
          ),
          deleteInventoryTransactionItemIds,
        }
      );
      if (response.code === ApiConstant.ERROR_CODE_OK) {
        toast.success("Thành công", {
          description: "Cập nhật phiếu kiểm kê thiết bị thành công",
        });
        onSuccess?.();
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      EnvConstant.IS_DEV && console.log(error);
      toast.error("Thất bại!", {
        description: CommonUtils.extractErrorMessage(error),
      });
    }
  };

  return {
    handleCreateInventory,
    handleUpdateInventory,
  };
};

export default useInventoryActions;
