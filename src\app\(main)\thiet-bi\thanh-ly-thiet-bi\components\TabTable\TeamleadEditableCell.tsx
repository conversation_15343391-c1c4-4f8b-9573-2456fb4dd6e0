import { useRef, useState } from "react";
import { AppTextField } from "@/components/common";
import { AppConstant } from "@/constant";
import { CommonUtils } from "@/utils";
import { useAppDispatch } from "@/redux/hook";
import { deviceLiquidationActions } from "../../deviceLiquidation.slice";

type Props = {
  teacherCode: string;
  field: "position" | "role" | "note";
  defaultValue: string;
};

export const TeamleadEditableCell = ({
  teacherCode,
  field,
  defaultValue,
}: Props) => {
  const dispatch = useAppDispatch();
  const [value, setValue] = useState(defaultValue);
  const debounceRef = useRef(
    CommonUtils.debounce((newValue: string) => {
      dispatch(
        deviceLiquidationActions.updateTeamleadField({
          teacherCode,
          field,
          value: newValue,
        })
      );
    }, AppConstant.DEBOUNCE_TIME_IN_MILLISECOND)
  );

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVal = e.target.value;
    setValue(newVal);
    debounceRef.current(newVal);
  };

  return <AppTextField value={value} onChange={handleChange} />;
};
