import { IDeviceParams } from "@/models/eduDevice.model";
import { IPaginationModel } from "@/models/response.model";
import { rootReducer } from "@/redux/reducer";
import { RootState } from "@/redux/store";
import {
  createSelector,
  createSlice,
  PayloadAction,
  WithSlice,
} from "@reduxjs/toolkit";
import {
  IDeviceItem,
  IDeviceLiquidationAction,
  IDeviceLiquidationEdit,
  IDevicesAction,
  ITransactionTeams,
} from "./deviceLiquidation.model";
import { ISearchParams, ITeacher } from "@/models/system.model";
import { convertDataToOptions } from "@/utils/format.utils";

export interface IInitialState {
  deviceChooseList: IDeviceItem[];
  totalDeviceChoose: number;

  devices: IDevicesAction[];
  deviceDeleted: number[];
  deviceSelected: number[];

  teacherChooseList: ITeacher[];
  teacherChooseTotal: number;
  teacherSelected: number[];
  teamlead: ITransactionTeams[];
}

const initialState: IInitialState = {
  deviceChooseList: [],
  totalDeviceChoose: 0,
  devices: [],
  deviceDeleted: [],
  deviceSelected: [],

  teacherChooseList: [],
  teacherChooseTotal: 0,
  teacherSelected: [],
  teamlead: [],
};

const deviceLiquidationSelector = {
  deviceChooseList: (state: IInitialState) => state.deviceChooseList,
  totalDeviceChoose: (state: IInitialState) => state.totalDeviceChoose,
  deviceSelected: (state: IInitialState) => state.deviceSelected,
};

export const selectorDeviceAction = (id: number) =>
  createSelector(
    [(state: RootState) => state.deviceLiquidation?.devices ?? []],
    (devices) => devices.find((item) => item.id === id)
  );

export const selectorDeviceDeleted = createSelector(
  [(state: RootState) => state.deviceLiquidation?.deviceDeleted ?? []],
  (deviceDeleted) => [...(deviceDeleted ?? [])]
);

export const selectTeacherChooseList = createSelector(
  (state: RootState) => state.deviceLiquidation?.teacherChooseList,
  (list) => convertDataToOptions(list)
);

export const selectTeacherChooseTotal = createSelector(
  (state: RootState) => state.deviceLiquidation?.teacherChooseTotal,
  (total) => total
);

export const selectTeacherSelected = createSelector(
  (state: RootState) => state.deviceLiquidation?.teacherSelected,
  (list) => [...(list ?? [])]
);

export const selectTeamlead = createSelector(
  (state: RootState) => state.deviceLiquidation?.teamlead,
  (list) => [...(list ?? [])]
);

export const selectDeviceDeleted = createSelector(
  (state: RootState) => state.deviceLiquidation?.deviceDeleted,
  (list) => [...(list ?? [])]
);

export const selectDevices = createSelector(
  (state: RootState) => state.deviceLiquidation?.devices,
  (list) => [...(list ?? [])]
);

const deviceLiquidationReducer = {
  initEditForm: (
    state: IInitialState,
    action: PayloadAction<IDeviceLiquidationEdit>
  ) => {
    const data = action.payload;

    state.teamlead = data.transactionTeams;
    state.devices = data.devices.map((item) => ({
      id: item.id,
      code: item.code || "",
      deviceDefinitionId: item.deviceDefinitionId || 0,
      roomId: item.roomId || 0,
      quantity: item.quantity || 0,
      price: item.price || 0,
      serial: item.serial || "",
      totalBroken: item.transactionTotalBroken,
      totalAvailable: item.transactionTotalAvailable,
      deviceTransactionItemId: item.deviceTransactionItemId,

      // Hiển thị client
      deviceName: item.deviceName,
      roomName: item.roomName,
      deviceUnitName: item.deviceUnitName,
      deviceInput: item.deviceInput,
      canBroken: item.totalBroken + item.transactionTotalBroken,
      canAvailable: item.totalAvailable + item.transactionTotalAvailable,
    }));
  },

  addDevices: (state: IInitialState) => {
    const selectedIds = new Set(state.deviceSelected);
    const existingIds = new Set(state.devices.map((d) => d.id));

    const mergedDevices = state.deviceChooseList.reduce<IDevicesAction[]>(
      (acc, item) => {
        if (selectedIds.has(item.id) && !existingIds.has(item.id)) {
          acc.push({
            id: item.id,
            code: item.code || "",
            deviceDefinitionId: item.deviceDefinitionId || 0,
            roomId: item.roomId || 0,
            quantity: item.quantity || 0,
            price: item.price || 0,
            serial: item.serial || "",
            totalBroken: 0,
            totalAvailable: 0,
            deviceTransactionItemId: 0,

            // Hiển thị client
            deviceName: item.deviceName,
            roomName: item.roomName,
            deviceUnitName: item.deviceUnitName,
            deviceInput: item.deviceInput,
            canBroken: item.totalBroken,
            canAvailable: item.totalAvailable,
          });
        }
        return acc;
      },
      []
    );

    state.devices.push(...mergedDevices);
    state.deviceSelected = [];
  },

  changeDeviceInput: (
    state,
    action: PayloadAction<{
      id: number;
      isError: boolean;
      totalBroken?: number;
      totalAvailable?: number;
    }>
  ) => {
    const { id, ...changes } = action.payload;
    const device = state.devices.find((item) => item.id === id);
    if (device) {
      Object.assign(device, changes);
    }
  },

  deleteDevice: (state: IInitialState, action: PayloadAction<number>) => {
    const deviceFind = state.devices.find((item) => item.id === action.payload);
    state.devices = state.devices.filter((item) => item.id !== action.payload);
    if (deviceFind && deviceFind.deviceTransactionItemId !== 0) {
      state.deviceDeleted = [
        ...state.deviceDeleted,
        deviceFind.deviceTransactionItemId,
      ];
    }
  },

  // Danh sách thiết bị đã chọn ở chọn thêm thiết bị
  setDeviceSelected: (
    state: IInitialState,
    action: PayloadAction<number | Array<number>>
  ) => {
    const id = action.payload;
    if (Array.isArray(id)) {
      state.deviceSelected = action.payload as Array<number>;
    } else {
      let newIds: Array<number> = [];
      const isExisted = state.deviceSelected.includes(id);
      if (isExisted) {
        newIds = state.deviceSelected.filter((item) => item !== id);
      } else {
        newIds = [...state.deviceSelected, id];
      }
      state.deviceSelected = newIds;
    }
  },

  setDeviceSelectedAll: (
    state: IInitialState,
    action: PayloadAction<{
      checked: boolean;
      deviceChooseList: IDeviceItem[];
    }>
  ) => {
    let newIds: Array<number> = [];
    const deviceChooseList = action.payload.deviceChooseList;
    const numberIds = deviceChooseList.map((item) => item.id);

    if (action.payload.checked) {
      newIds = [...state.deviceSelected, ...numberIds];
    } else {
      newIds = state.deviceSelected.filter((item) => !numberIds.includes(item));
    }
    newIds = [...new Set(newIds)];
    state.deviceSelected = newIds;
  },

  getDeviceChoose: (
    state: IInitialState,
    action: PayloadAction<IDeviceParams & IPaginationModel>
  ) => {},

  getDeviceChooseSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: IDeviceItem[];
      totalCount: number;
    }>
  ) => {
    state.deviceChooseList = action.payload.data;
    state.totalDeviceChoose = action.payload.totalCount;
  },

  getTeacherChooseList: (
    state: IInitialState,
    action: PayloadAction<ISearchParams>
  ) => {},

  getTeacherChooseListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<ITeacher>;
      totalCount: number;
    }>
  ) => {
    state.teacherChooseList = action.payload.data || [];
    state.teacherChooseTotal = action.payload.totalCount;
  },

  setTeacherSelectedAll: (
    state: IInitialState,
    action: PayloadAction<{
      checked: boolean;
      teacherChooseList: ITeacher[];
    }>
  ) => {
    let newIds: Array<number> = [];
    const teacherChooseList = action.payload.teacherChooseList;
    const numberIds = teacherChooseList.map((item) => item.id);

    if (action.payload.checked) {
      newIds = [...state.teacherSelected, ...numberIds];
    } else {
      newIds = state.teacherSelected.filter(
        (item) => !numberIds.includes(item)
      );
    }
    newIds = [...new Set(newIds)];
    state.teacherSelected = newIds;
  },

  setTeacherSelected: (
    state: IInitialState,
    action: PayloadAction<number | Array<number>>
  ) => {
    const id = action.payload;
    if (Array.isArray(id)) {
      state.teacherSelected = action.payload as Array<number>;
    } else {
      let newIds: Array<number> = [];
      const isExisted = state.teacherSelected.includes(id);
      if (isExisted) {
        newIds = state.teacherSelected.filter((item) => item !== id);
      } else {
        newIds = [...state.teacherSelected, id];
      }
      state.teacherSelected = newIds;
    }
  },

  addTeamlead: (state: IInitialState) => {
    const existingIds = new Set(state.teamlead.map((t) => t.id));

    const newTeamlead = state.teacherChooseList.reduce<ITransactionTeams[]>(
      (acc, teacher) => {
        if (
          state.teacherSelected.includes(teacher.id) &&
          !existingIds.has(teacher.id)
        ) {
          acc.push({
            id: teacher.id,
            stt: state.teamlead.length + acc.length + 1,
            teacherCode: teacher.code,
            teacherName: `${teacher.lastName ?? ""} ${
              teacher.firstName ?? ""
            }`.trim(),
            position: "",
            role: "",
            note: "",
            isTeamlead: false,
          });
        }
        return acc;
      },
      []
    );

    state.teamlead.push(...newTeamlead);
    state.teacherSelected = [];
  },

  updateTeamleadField: (
    state: IInitialState,
    action: PayloadAction<{
      teacherCode: string;
      field: keyof Pick<
        ITransactionTeams,
        "position" | "role" | "note" | "isTeamlead"
      >;
      value: any;
    }>
  ) => {
    const { teacherCode, field, value } = action.payload;

    const index = state.teamlead.findIndex(
      (item) => item.teacherCode === teacherCode
    );

    if (index === -1) return;

    if (field === "isTeamlead") {
      state.teamlead = state.teamlead.map((item, i) => ({
        ...item,
        isTeamlead: i === index ? value : false,
      }));
    } else {
      state.teamlead[index] = {
        ...state.teamlead[index],
        [field]: value,
      };
    }
  },

  deleleTeamlead: (state: IInitialState, action: PayloadAction<number>) => {
    state.teamlead = state.teamlead.filter(
      (item) => item.id !== action.payload
    );
  },

  addDeviceLiquidation: (
    state: IInitialState,
    action: PayloadAction<{
      data: IDeviceLiquidationAction;
      onSuccess: () => void;
    }>
  ) => {},

  updateDeviceLiquidation: (
    state: IInitialState,
    action: PayloadAction<{
      id?: number;
      data: IDeviceLiquidationAction;
      onSuccess?: () => void;
    }>
  ) => {},

  resetModalSelected: (state: IInitialState) => {
    state.deviceSelected = [];
    state.totalDeviceChoose = 0;
    state.deviceChooseList = [];
  },

  resetSelectedTeacherModal: (state: IInitialState) => {
    state.teacherSelected = [];
    state.teacherChooseTotal = 0;
    state.teacherChooseList = [];
  },

  resetModalDetails: (state: IInitialState) => {
    state.deviceDeleted = [];
    state.devices = [];
  },
  resetdeviceLiquidation: (state: IInitialState) => {
    state.deviceChooseList = [];
    state.totalDeviceChoose = 0;
    state.devices = [];
    state.deviceDeleted = [];
    state.deviceSelected = [];
    state.teacherChooseList = [];
    state.teamlead = [];
    state.teacherSelected = [];
    state.teacherChooseTotal = 0;
  },
};

export const reducerDeviceSlice = createSlice({
  name: "deviceLiquidation",
  initialState,
  reducers: deviceLiquidationReducer,
  selectors: deviceLiquidationSelector,
});

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices
    extends WithSlice<typeof reducerDeviceSlice> {}
}

const injecteddeviceLiquidationSlice =
  reducerDeviceSlice.injectInto(rootReducer);

export const deviceLiquidationSelectors =
  injecteddeviceLiquidationSlice.selectors;
export const deviceLiquidationActions = injecteddeviceLiquidationSlice.actions;
