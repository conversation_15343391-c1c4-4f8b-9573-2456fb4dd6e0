import { DataConstant } from "@/constant";

export interface IUnitParams {
  doetCode?: string;
  divisionCode?: string | null;
  groupUnitCode?: string;
  schoolLevel?: number | null;
  searchKey?: string;
  status?: DataConstant.BOOLEAN_TYPE;
  isGetDivision?: number;
  skip?: number;
  take?: number;
}

export interface IDoet {
  code: string;
  name: string;
  provinceCode: string;
  address: string;
  phone: string;
  email: string;
  fax: string;
  website: string;
  logo: string;
  director: string;
  directorMobile: string;
  directorEmail: string;
  order: number;
  status: DataConstant.BOOLEAN_TYPE;
}

export interface IDivision {
  address: string;
  code: string;
  districtCode: string;
  doetCode: string;
  email: string;
  fax: string;
  id: number;
  logo: string;
  name: string;
  order: number;
  phone: string;
  provinceCode: string;
  status: DataConstant.BOOLEAN_TYPE;
  website: string;
}
