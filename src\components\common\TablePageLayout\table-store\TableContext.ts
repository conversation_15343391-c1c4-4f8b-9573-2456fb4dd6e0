import React from "react";
import { createTableStore, TableStoreState } from "./storeFactory";

const tableStore = createTableStore<any>(); // or createStore

export const TableStoreContext = React.createContext<typeof tableStore | null>(
  null
);

export function useTableStore<T>() {
  const store = React.useContext(TableStoreContext);
  if (!store) throw new Error("useTableStore must be inside TableProvider");
  return store as ReturnType<typeof createTableStore<T>>; // This is the Zustand hook
}
