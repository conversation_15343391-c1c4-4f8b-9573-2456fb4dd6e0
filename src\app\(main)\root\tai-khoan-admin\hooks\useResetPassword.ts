import http from "@/api";
import { ApiConstant } from "@/constant";
import { RESET_PASSWORD_ADMIN_ACCOUNT } from "@/constant/api.const";
import { DataResponseModel } from "@/models/response.model";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import React from "react";
import { toast } from "sonner";
import stringFormat from "string-format";

const useResetPassword = () => {
  const handleResetPass = async (
    body: { newPassword?: string },
    id?: number | string,
    onSuccess?: () => void
  ) => {
    toggleAppProgress(true);
    try {
      if (!id) throw Error(`id undefined`);

      const response: DataResponseModel<unknown> = await http.put(
        stringFormat(RESET_PASSWORD_ADMIN_ACCOUNT, { id }),
        body
      );

      if (response.code === ApiConstant.ERROR_CODE_OK) {
        onSuccess?.();
        toast.success("Thành công!", {
          description: "Khởi tạo mật khẩu thành công.",
        });
        return response.data;
      }

      throw response;
    } catch (error: any) {
      const description = extractErrorMessage(error);
      toast.error("Thất bại!", {
        description,
      });
    } finally {
      toggleAppProgress(false);
    }
  };
  return handleResetPass;
};

export default useResetPassword;
