import { useEffect } from "react";
import { UseFormSetValue } from "react-hook-form";
import { mapIdsToOptions, toOption } from "@/components/common/AppAutoComplete";
import {
  IBorrowRequestAction,
  PURPOSE_USE_ROOM_LIST,
} from "../../borrowRequestModel";
import { useAppSelector } from "@/redux/hook";
import { selectClassList, selectPeriodList } from "@/redux/system.slice";
import { IBorrowRequest } from "@/models/eduDevice.model";

interface Props {
  modalData: IBorrowRequest | null;
  setValue: UseFormSetValue<IBorrowRequestAction>;
}

export const useInitBorrowRequestForm = ({ modalData, setValue }: Props) => {
  const periodList = useAppSelector(selectPeriodList);
  const classList = useAppSelector(selectClassList);

  useEffect(() => {
    if (modalData?.id) {
      setValue("borrowFromDate", modalData.borrowFromDate);
      setValue("borrowFromDate", modalData.borrowFromDate);
      setValue("borrowToDate", modalData.borrowToDate);
      setValue("borrowType", modalData.borrowType);
      setValue("schoolWeekConfigId", {
        id: modalData.schoolWeekConfigId as any,
        label: modalData.schoolWeekConfigName as string,
        fromDate: modalData.borrowFromDate,
        toDate: modalData.borrowToDate,
      });
      setValue("schoolWeekConfigName", modalData.schoolWeekConfigName);
      setValue(
        "teacherId",
        toOption(modalData.teacherId as any, modalData.teacherName)
      );
      setValue("teacherName", modalData.teacherName);
      setValue(
        "borrowRequestDevices",
        modalData.borrowRequestDevices.map((item) => ({
          ...item,
          borrowType: modalData.borrowType,
          roomId: toOption(item.roomId as any, item.roomName) as any,
          subjectId: toOption(item.subjectId as any, item.subjectName) as any,
        })) || []
      );
    }
  }, [modalData]);

  useEffect(() => {
    if (modalData?.id) {
      setValue(
        "borrowRequestRooms",
        modalData.borrowRequestRooms.map((item) => ({
          ...item,
          periodIds: mapIdsToOptions(
            item.periodIds as number[],
            periodList
          ) as any,
          schoolClassIds: mapIdsToOptions(
            item.schoolClassIds as number[],
            classList
          ) as any,
          subjectId: toOption(item.subjectId as any, item.subjectName) as any,
          roomId: toOption(item.roomId as any, item.roomName) as any,
          borrowType: modalData.borrowType,
          purpose:
            PURPOSE_USE_ROOM_LIST.find(
              (purpose) => purpose.id === (item.purpose as any)
            ) ?? null,
        })) || []
      );
    }
  }, [modalData, periodList, classList]);
};
