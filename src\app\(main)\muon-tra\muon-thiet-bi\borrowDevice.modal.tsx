import { BorrowStatusEnum } from "@/models/eduDevice.model";

export interface IBorrowDeviceModal {
  borrowRequestId: number;
  teacherId: number;
  teacherName: string;
  deviceId: number;
  deviceCode: string;
  deviceName: string;
  subjectId: number;
  subjectName: string;
  roomId: number;
  roomName: string;
  fromDate: Date;
  toDate: Date;
  deviceUnitId: number;
  deviceUnitName: string;
  gradeCodes: number[];
  gradeCode: string;
  gradeName: string;
  quantity: number;
  totalBorrowReady: number;
  roomDeviceGuid: string;
  notes: string;
  status: number;
  statusName: string;
  id: number;
  createdBy: number;
  updatedBy: number;
  createdAt: Date;
  updatedAt: Date;
  schoolDeviceTypeId: number;
  schoolDeviceTypeName: string;
}

export const BORROW_DEVICE_STATUS_LIST = [
  {
    id: BorrowStatusEnum.Register,
    label: "Đăng ký",
  },
  {
    id: BorrowStatusEnum.Borrowing,
    label: "<PERSON>ang mượn",
  },
  {
    id: BorrowStatusEnum.Returned,
    label: "Đã trả",
  },
  {
    id: BorrowStatusEnum.Reject,
    label: "Từ chối",
  },
];
