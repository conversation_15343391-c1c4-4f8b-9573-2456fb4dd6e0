import { Grid, Stack, Typography } from "@mui/material";
import React, { memo, useCallback, useMemo } from "react";
import { useFieldArray, useFormContext, useWatch } from "react-hook-form";
import { IChooseDeviceForm } from "./";
import {
  AppDateRangePicker,
  AppFormTextField,
  AppTable,
} from "@/components/common";
import { DateRangeValue } from "@/models/types";
import { ColumnDef } from "@tanstack/react-table";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import { BORROW_TYPE } from "@/models/eduDevice.model";
import { formatNumber } from "@/utils/format.utils";

const InfoTable = () => {
  const { control, setValue } = useFormContext<IChooseDeviceForm>();
  const { fields } = useFieldArray({ control, name: "deviceSelected" });

  const [borrowType, fromDate, toDate] = useWatch({
    control,
    name: ["borrowType", "fromDate", "toDate"],
  });

  const dateRange = useMemo(() => [fromDate, toDate], [fromDate, toDate]);

  const handleDateChange = useCallback((dateRange: DateRangeValue) => {
    setValue("fromDate", dateRange?.[0] ?? null);
    setValue("toDate", dateRange?.[1] ?? null);
  }, []);

  return (
    <Stack height="100%" spacing={1}>
      <Grid container px={3}>
        <Grid size={2.4}>
          <AppDateRangePicker
            hasPlugin={false}
            maxDate={dateRange[1] ?? undefined}
            minDate={dateRange[0] ?? undefined}
            disabled={borrowType === BORROW_TYPE.longTerm}
            onDateChange={handleDateChange}
            label="Thời hạn đăng ký"
            value={dateRange}
          />
        </Grid>
      </Grid>
      <AppTable
        columns={COLUMN}
        data={fields}
        totalData={fields.length}
        hasDefaultPagination
        {...TABLE_MODAL_FULL_HEIGHT}
      />
    </Stack>
  );
};

const COLUMN: ColumnDef<IChooseDeviceForm["deviceSelected"][number]>[] = [
  {
    header: "STT",
    size: 50,
    id: "index",
    cell: ({ row }) => row.index + 1,
    meta: {
      align: "center",
    },
  },
  { accessorKey: "deviceCode", header: "Mã thiết bị", size: 50 },
  {
    header: "Thiết bị",
    size: 200,
    cell: ({ row }) => {
      return (
        <>
          <Typography>{row.original.deviceName}</Typography>
          <Typography sx={{ color: "grey.500", fontSize: 12 }}>
            {row.original.code}
          </Typography>
        </>
      );
    },
  },
  {
    accessorKey: "deviceUnitName",
    header: "Đơn vị tính",
    size: 200,
  },
  {
    accessorKey: "roomName",
    header: "Kho/Phòng",
    size: 200,
  },
  {
    accessorKey: "schoolSubjectName",
    header: "Môn học",
    size: 200,
  },
  {
    size: 60,
    accessorFn: (row) => formatNumber(row.totalBorrowReady),
    header: "Số lượng có sẵn",
    meta: {
      align: "right",
    },
  },
  {
    header: "Số lượng ĐK mượn",
    size: 150,
    cell: ({ row }) => {
      return <QuantityInputCell index={row.index} />;
    },
  },
  {
    id: "empty",
    accessorKey: "empty",
    header: "",
    meta: {
      headerSx: {
        width: "100%",
      },
    },
  },
];

export default memo(InfoTable);

const QuantityInputCell = memo(({ index }: { index: number }) => {
  const { control } = useFormContext<IChooseDeviceForm>();

  return (
    <AppFormTextField
      name={`deviceSelected.${index}.totalBorrow`}
      control={control}
      textfieldProps={{
        type: "number",
        inputProps: { min: 1 },
      }}
    />
  );
});
