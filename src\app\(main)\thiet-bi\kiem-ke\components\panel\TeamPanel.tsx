import {
  inventoryTransactionActions,
  inventoryTransactionSelectors,
  selectorTransactionActionTeams,
} from "@/app/(main)/thiet-bi/kiem-ke/inventoryTransaction.slice";
import { ITransactionTeam } from "@/app/(main)/thiet-bi/kiem-ke/type";
import { AppCheckbox, AppTable, AppTextField } from "@/components/common";
import DeleteCell from "@/components/common/table/cell/DeleteCell";
import { AppConstant } from "@/constant";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { CommonUtils } from "@/utils";
import { Typography } from "@mui/material";
import { ColumnDef } from "@tanstack/react-table";
import React, { memo, useEffect, useMemo, useState } from "react";

const TeamPanel = () => {
  const dispatch = useAppDispatch();
  const transactionTeams = useAppSelector(
    inventoryTransactionSelectors.transactionTeams
  );

  const columns = getColumns((id) => {
    dispatch(inventoryTransactionActions.deleteTransactionActionTeams(id));
  });

  return (
    <AppTable
      tableContainerProps={{
        sx: {
          height: 400,
        },
      }}
      columns={columns}
      data={transactionTeams}
      totalData={transactionTeams.length}
      hasDefaultPagination
    />
  );
};

export default memo(TeamPanel);

const getColumns = (onDelete): ColumnDef<ITransactionTeam>[] => [
  {
    id: "index",
    header: "STT",
    cell: ({ row }) => {
      return <Typography>{row.index + 1}</Typography>;
    },
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "delete",
    header: "Xóa",
    cell: ({ row }) => (
      <DeleteCell onClick={() => onDelete(row.original.teacherCode)} />
    ),
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "teacherCode",
    header: "Mã giáo viên",
    accessorKey: "teacherCode",
    size: 100,
  },
  {
    id: "teacherName",
    header: "Tên giáo viên",
    accessorKey: "teacherName",
    size: 150,
  },
  {
    id: "position",
    header: "Chức vụ",
    accessorKey: "position",
    cell: ({ row }) => (
      <TeamInputCell
        code={row.original.teacherCode}
        currentValue={row.original.position}
        field="position"
        isEdit={false}
      />
    ),
  },
  {
    id: "role",
    header: "Vai trò",
    accessorKey: "role",
    cell: ({ row }) => (
      <TeamInputCell
        code={row.original.teacherCode}
        currentValue={row.original.role}
        field="role"
        isEdit={false}
      />
    ),
  },
  {
    id: "note",
    header: "Ghi chú",
    accessorKey: "note",
    cell: ({ row }) => (
      <TeamInputCell
        code={row.original.teacherCode}
        currentValue={row.original.note}
        field="note"
        isEdit={false}
      />
    ),
  },
  {
    id: "teamlead",
    header: "Đại diện ban",
    accessorKey: "isTeamlead",
    cell: ({ row }) => (
      <TeamleadCell
        code={row.original.teacherCode}
        valueInitial={row.original.isTeamlead}
      />
    ),
    meta: {
      align: "center",
    },
    size: 100,
  },
];

const TeamInputCell = memo(
  ({
    code,
    field,
    currentValue,
    isEdit,
  }: {
    code: string;
    field: string;
    currentValue: string;
    isEdit: boolean;
  }) => {
    const dispatch = useAppDispatch();
    const [value, setValue] = useState(isEdit ? currentValue : "");
    const teamFind = useAppSelector(selectorTransactionActionTeams(code));

    const changeValueDebounce = useMemo(
      () =>
        CommonUtils.debounce((newValue: string) => {
          dispatch(
            inventoryTransactionActions.changeTransactionActionTeams({
              teacherCode: code,
              [field]: newValue,
            })
          );
        }, AppConstant.DEBOUNCE_TIME_IN_MILLISECOND),
      [dispatch, code]
    );

    const handleChange = (val: string) => {
      setValue(val);
      changeValueDebounce(val);
    };

    useEffect(() => {
      if (teamFind?.[field]) {
        setValue(teamFind[field]);
      }
    }, []);

    return (
      <AppTextField
        value={value}
        onChange={(e) => handleChange(e.currentTarget.value)}
      />
    );
  }
);

const TeamleadCell = memo(
  ({ code, valueInitial }: { code: string; valueInitial: boolean }) => {
    const dispatch = useAppDispatch();
    const [value, setValue] = useState(false);
    const teamFind = useAppSelector(selectorTransactionActionTeams(code));

    const changeValueDebounce = useMemo(
      () =>
        CommonUtils.debounce((newValue: boolean) => {
          dispatch(
            inventoryTransactionActions.changeTransactionActionTeams({
              teacherCode: code,
              isTeamlead: newValue,
            })
          );
        }, AppConstant.DEBOUNCE_TIME_IN_MILLISECOND),
      [dispatch, code]
    );

    const handleChange = (val: boolean) => {
      setValue(val);
      changeValueDebounce(val);
    };

    useEffect(() => {
      if (teamFind?.isTeamlead) {
        setValue(teamFind.isTeamlead);
      }
    }, []);

    return (
      <AppCheckbox checked={value} onChange={(_, val) => handleChange(val)} />
    );
  }
);
