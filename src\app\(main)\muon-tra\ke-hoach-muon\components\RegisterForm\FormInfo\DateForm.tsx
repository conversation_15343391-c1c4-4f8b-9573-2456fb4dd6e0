import React, { memo, useEffect } from "react";
import { useFormContext, useFormState, useWatch } from "react-hook-form";
import { IBorrowRequestAction } from "../../../borrowRequestModel";
import AppFormDatePicker from "@/components/common/form/AppFormDatePicker";
import { Grid, IconButton, InputAdornment } from "@mui/material";
import { AppFormAutocomplete } from "@/components/common";
import { useAppSelector } from "@/redux/hook";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import { ArrowIcon } from "@/components/icons";
import { BORROW_TYPE } from "@/models/eduDevice.model";
import { selectSchoolWeekList } from "@/redux/system.slice";
dayjs.extend(isBetween);
dayjs.extend(isSameOrAfter);

const DateForm = ({ isEdit }) => {
  const { control, setValue, getValues } =
    useFormContext<IBorrowRequestAction>();
  const borrowType = useWatch({ control, name: "borrowType" });
  const { errors } = useFormState({ control });
  const schoolWeek = useAppSelector(selectSchoolWeekList);

  const handleChangeWeek = (direction: "prev" | "next") => {
    const selectedWeek = getValues("schoolWeekConfigId");

    if (!selectedWeek) return;
    setValue("borrowRequestDevices", []);
    setValue("borrowRequestRooms", []);

    const currentIndex = schoolWeek.findIndex((w) => w.id === selectedWeek.id);
    if (currentIndex === -1) return;

    const nextIndex =
      direction === "prev"
        ? (currentIndex - 1 + schoolWeek.length) % schoolWeek.length
        : (currentIndex + 1) % schoolWeek.length;

    const nextWeek = schoolWeek[nextIndex];

    setValue("schoolWeekConfigId", nextWeek);
    setValue("schoolWeekConfigName", nextWeek.label);
  };

  return Number(borrowType) === BORROW_TYPE.longTerm ? (
    <>
      <Grid size={3}>
        <AppFormDatePicker
          direction="row"
          labelProps={{
            sx: {
              minWidth: 100,
            },
          }}
          control={control}
          name="borrowFromDate"
          label="Đăng ký mượn"
          rules={{
            required: "Ngày đăng ký mượn không được bỏ trống",
          }}
          datePickerProps={{
            disabled: isEdit,
            maxDate: null,
            slotProps: {
              textField: {
                helperText: errors?.borrowFromDate?.message,
                error: Boolean(errors?.borrowFromDate),
              },
            },
          }}
        />
      </Grid>
      <Grid size={3}>
        <AppFormDatePicker
          direction="row"
          labelProps={{ sx: { minWidth: 90 } }}
          control={control}
          name="borrowToDate"
          label="Ngày hẹn trả"
          rules={{
            required: "Ngày hẹn trả không được bỏ trống",
            validate: (value) => {
              const from = getValues("borrowFromDate");
              if (!value || !from) return true;
              return dayjs(value).isSameOrAfter(dayjs(from))
                ? true
                : "Ngày hẹn trả phải lớn hơn hoặc bằng ngày đăng ký mượn";
            },
          }}
          datePickerProps={{
            disabled: isEdit,
            maxDate: null,
            slotProps: {
              textField: {
                helperText: errors?.borrowToDate?.message,
                error: Boolean(errors?.borrowToDate),
              },
            },
          }}
        />
      </Grid>
    </>
  ) : (
    <>
      <Grid size={3.5}>
        <AppFormAutocomplete
          direction="row"
          labelProps={{
            sx: {
              minWidth: 20,
            },
          }}
          label="Tuần"
          options={schoolWeek}
          control={control}
          name="schoolWeekConfigId"
          rules={{
            required: "Tuần không được bỏ trống",
          }}
          onChangeValueForm={() => {
            setValue("borrowRequestDevices", []);
            setValue("borrowRequestRooms", []);
          }}
          startAdornment={
            <IconButton
              disabled={isEdit}
              onClick={() => handleChangeWeek("prev")}
            >
              <ArrowIcon />
            </IconButton>
          }
          actionsButton={
            <IconButton
              disabled={isEdit}
              onClick={() => handleChangeWeek("next")}
            >
              <ArrowIcon
                sx={{
                  transform: "rotate(180deg)",
                }}
              />
            </IconButton>
          }
          autocompleteProps={{
            disabled: isEdit,
            disableClearable: true,
            slotProps: {
              paper: {
                sx: {
                  minWidth: 240,
                },
              },
            },
          }}
        />
      </Grid>
    </>
  );
};

export default memo(DateForm);
