export interface IDeviceLiquidation {
  documentNumber: string;
  documentDate: string;
  schoolBudgetCategoryId: number;
  schoolBudgetCategoryName: string;
  totalDevices: number;
  totalPrices: number;
  notes: string;
  deviceTransactionItems: IDeviceItem[];
  id: number;
}

export interface ITransactionTeams {
  id: number;
  stt: number;
  teacherCode: string;
  teacherName: string;
  position: string;
  role: string;
  note: string;
  isTeamlead: boolean;
}

export interface IDevicesAction {
  id: number;
  code: string;
  deviceDefinitionId: number;
  roomId: number;
  teacherId?: number;
  quantity: number;
  price: number;
  countryId?: number;
  entryDate?: string;
  serial: string;
  expireDate?: string;
  codeIndex?: number;
  totalBroken: number;
  totalLost?: number;
  totalAvailable: number;
  deviceTransactionItemId: number;

  // Hiển thị client
  deviceName?: string;
  roomName?: string;
  deviceUnitName?: string;
  deviceInput?: string;
  canBroken?: number;
  canAvailable?: number;
}

export interface IDeviceLiquidationAction {
  documentNumber: string;
  documentDate: string;
  notes: string;
  devices: IDevicesAction[];
  transactionTeams: ITransactionTeams[];
  deleteDeviceTransactionItemIds: number[];
}

export interface IDeviceInput {
  id: number;
  totalBroken?: number;
  totalAvailable?: number;
  isError?: boolean;
}

export interface IDeviceItem {
  id: number;
  code: string;
  deviceTransactionId: number;
  deviceDefinitionId: number;
  roomId: number;
  roomName: string;
  teacherId: number;
  teacherName: string;
  quantity: number;
  price: number;
  totalPrices: number;
  countryId: number;
  countryName: string;
  entryDate: string;
  serial: string;
  expireDate: string;
  totalBroken: number;
  totalLost: number;
  totalAvailable: number;
  statisticCode: string;
  deviceCode: string;
  deviceName: string;
  deviceUnitId: number;
  deviceUnitName: string;
  schoolDeviceTypeId: number;
  schoolDeviceTypeName: string;
  deviceDTITypeId: number;
  deviceDTITypeName: string;
  schoolSubjectId: number;
  schoolSubjectName: string;
  gradeCodes: string[];
  gradeCode: string;
  userType: string;
  userTypes: string[];
  documentNumber: string;
  documentDate: string;
  deviceTransactionItemId: number;
  transactionTotalBroken: number;
  transactionTotalLost: number;
  transactionTotalAvailable: number;
  deviceInput: string;
}

export interface IDeviceLiquidationEdit {
  id: number;
  documentNumber: string;
  documentDate: string;
  schoolBudgetCategoryId: number;
  schoolBudgetCategoryName: string;
  totalDevices: number;
  totalPrices: number;
  notes: string;
  devices: IDeviceItem[];
  transactionTeams: ITransactionTeams[];
  createdBy: number;
  updatedBy: number | null;
  createdAt: string;
  updatedAt: string | null;
}
