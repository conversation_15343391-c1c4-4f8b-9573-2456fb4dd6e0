"use client";
import { useCallback, useEffect, useMemo, useState } from "react";
import { VisibilityState } from "@tanstack/react-table";
import { AppConstant } from "@/constant";
import { removeVietnameseTones } from "@/utils/format.utils";
import { usePathname } from "next/navigation";

export interface IColumnVisible {
  name: string;
  id: string;
}

interface UseColumnToggleProps {
  columns: IColumnVisible[];
  columnVisibleDefault: VisibilityState;
  onChangeColumnVisibility: (state: VisibilityState) => void;
}

export const useColumnToggle = ({
  columns,
  columnVisibleDefault,
  onChangeColumnVisibility,
}: UseColumnToggleProps) => {
  const pathname = usePathname();
  const localKey = `${AppConstant.COOKIE_KEY.COLUMNS_CONFIG_VISIBLE}_${pathname}`;
  const [searchKey, setSearchKey] = useState("");
  const [ids, setIds] = useState<string[]>([]);

  const columnSearched = useMemo(() => {
    const normalizedSearchKey = removeVietnameseTones(searchKey.toLowerCase());
    return columns.filter((item) =>
      removeVietnameseTones(item.name.toLowerCase()).includes(
        normalizedSearchKey
      )
    );
  }, [columns, searchKey]);

  const updateVisibility = useCallback(
    (newIds: string[]) => {
      localStorage.setItem(localKey, JSON.stringify(newIds));
      const visibility = columns.reduce((acc, col) => {
        acc[col.id] = newIds.includes(col.id);
        return acc;
      }, {} as VisibilityState);
      onChangeColumnVisibility(visibility);
    },
    [columns, localKey, onChangeColumnVisibility]
  );

  const handleReset = useCallback(
    (custom?: string[]) => {
      const idsToSet =
        custom ??
        (columns
          .map((col) => {
            if (columnVisibleDefault?.[col.id] !== undefined) {
              return columnVisibleDefault[col.id] ? col.id : null;
            }
            return col.id;
          })
          .filter(Boolean) as string[]);
      setIds(idsToSet);
      updateVisibility(idsToSet);
    },
    [columns, columnVisibleDefault, updateVisibility]
  );

  useEffect(() => {
    const saved = localStorage.getItem(localKey);
    handleReset(saved ? JSON.parse(saved) : undefined);
  }, [localKey, handleReset]);

  useEffect(() => {
    return () => {
      localStorage.removeItem(localKey);
    };
  }, []);

  return {
    ids,
    setSearchKey,
    searchKey,
    columnSearched,
    handleReset,
    toggleId: (id: string, isChecked: boolean) => {
      const newIds = isChecked ? [...ids, id] : ids.filter((i) => i !== id);
      setIds(newIds);
      updateVisibility(newIds);
    },
    toggleAll: (checked: boolean) => {
      const newIds = checked ? columns.map((c) => c.id) : [];
      setIds(newIds);
      updateVisibility(newIds);
    },
  };
};
