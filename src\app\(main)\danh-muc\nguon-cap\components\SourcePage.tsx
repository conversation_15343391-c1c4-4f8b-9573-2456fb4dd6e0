"use client";

import { TablePageLayout } from "@/components/common";
import StatusCell from "@/components/common/table/cell/StatusCell";
import {
  FormFieldConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import { SOURCE, STATUS_SOURCE } from "@/constant/api.const";
import { ISource } from "@/models/system.model";
import { ColumnDef } from "@tanstack/react-table";
import React, { useRef } from "react";
import { STATUS_TYPE_LIST } from "@/constant/data.const";
import { updateStatusService } from "@/services/app.service";
import SyncModal from "./SyncModal";
import { CheckIcon } from "@/components/icons";

const SourcePage = () => {
  const tableRef = useRef<ITableRef>(null);
  const columns = getColumns(tableRef);

  return (
    <TablePageLayout<ISource>
      ref={tableRef}
      fetchAll
      visibleCol={VISIBLE_COL}
      apiUrl={SOURCE}
      tableProps={{
        columns,
        hasDefaultPagination: true,
      }}
      customActions={
        <SyncModal
          onFetchData={() => {
            tableRef.current?.fetchCurrentData?.();
          }}
        />
      }
      filterConfig={[
        {
          key: "searchKey",
          type: "text",
          label: "Tìm kiếm",
          size: 2.4,
        },
        {
          key: "status",
          type: "select",
          label: "Trạng thái hiển thị",
          size: 2.4,
          options: STATUS_TYPE_LIST,
        },
      ]}
      actions={["create", "update", "delete"]}
      formConfig={{
        deleteUrl: SOURCE,
        detailUrl: SOURCE,
        createUrl: SOURCE,
        updateUrl: SOURCE,
        createFields: CREATE_CONFIG,
        updateFields: UPDATE_CONFIG,
      }}
    />
  );
};

export default SourcePage;

const getColumns = (
  tableRef: React.RefObject<ITableRef | null>
): ColumnDef<ISource>[] => [
  {
    id: "code",
    header: "Mã",
    accessorKey: "code",
    size: 60,
  },
  {
    id: "name",
    header: "Tên nguồn cấp",
    accessorKey: "name",
    size: 150,
  },
  {
    id: "note",
    accessorKey: "note",
    header: "Ghi chú",
    size: 200,
  },
  {
    id: "isSystem",
    header: "Nguồn hệ thống",
    size: 60,
    meta: { align: "center" },
    cell: ({ row }) =>
      Boolean(row.original.isSystem) && (
        <CheckIcon sx={{ fontSize: 24, color: "primary.main" }} />
      ),
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Hiển thị",
    size: 60,
    cell: ({ row }) => (
      <StatusCell
        status={row.original.status}
        onStatusChange={(_, value) => {
          updateStatusService({
            id: row.original.id,
            status: Number(value),
            onSuccess: tableRef?.current?.fetchCurrentData,
            url: STATUS_SOURCE,
          });
        }}
      />
    ),
    meta: { align: "center" },
  },
];

const VISIBLE_COL = [
  { id: "code", name: "Mã" },
  { id: "name", name: "Tên nguồn cấp" },
  { id: "note", name: "Ghi chú" },
  { id: "status", name: "Hiển thị" },
];

const CREATE_CONFIG: FormFieldConfig<ISource>[] = [
  {
    key: "code",
    type: "text",
    label: "Mã nguồn cấp",
    size: 12,
    rules: {
      required: "Mã nguồn cấp không được để trống",
      maxLength: {
        value: 50,
        message: "Mã không được dài quá 50 ký tự",
      },
    },
  },
  {
    key: "name",
    type: "text",
    label: "Tên nguồn cấp",
    size: 12,
    rules: {
      required: "Tên nguồn cấp không được để trống",
      maxLength: {
        value: 500,
        message: "Tên nguồn cấp không được dài quá 500 ký tự",
      },
    },
  },
  {
    key: "note",
    type: "text",
    label: "Ghi chú",
    textFieldProps: {
      multiline: true,
      minRows: 3,
    },
    rules: {
      maxLength: {
        value: 500,
        message: "Ghi chú không được dài quá 500 ký tự",
      },
    },
    size: 12,
  },
  {
    key: "status",
    type: "toggle",
    label: "Trạng thái hiển thị",
    size: 12,
  },
];

const UPDATE_CONFIG: FormFieldConfig<ISource>[] = [
  {
    key: "code",
    type: "text",
    label: "Mã nguồn cấp",
    size: 12,
    rules: { required: "Mã nguồn cấp không được để trống" },
    disabled: true,
  },
  {
    key: "name",
    type: "text",
    label: "Tên nguồn cấp",
    size: 12,
    rules: { required: "Tên nguồn cấp không được để trống" },
  },
  {
    key: "note",
    type: "text",
    label: "Ghi chú",
    textFieldProps: {
      multiline: true,
      minRows: 3,
    },
    size: 12,
  },
  {
    key: "status",
    type: "toggle",
    label: "Trạng thái hiển thị",
    size: 12,
  },
];
