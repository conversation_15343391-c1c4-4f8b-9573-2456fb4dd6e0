import http from "@/api";
import {
  featureActions,
  featureSelectors,
} from "@/app/(main)/root/quan-ly-tinh-nang/store/feature.slice";
import {
  IFeature,
  IFeatureFilter,
} from "@/app/(main)/root/quan-ly-tinh-nang/type";
import { ApiConstant, DataConstant } from "@/constant";
import { FEATURE } from "@/constant/api.const";
import { DataListResponseModel } from "@/models/response.model";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { extractErrorMessage } from "@/utils/common.utils";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const useFeature = () => {
  const dispatch = useAppDispatch();
  const filter = useAppSelector(featureSelectors.filter);

  const fetchFeatures = async () => {
    try {
      const res = await http.get<DataListResponseModel<IFeature>>(FEATURE, {
        params: filter,
      });

      if (res.code === ApiConstant.ERROR_CODE_OK) {
        dispatch(featureActions.setFeatures(res.data.data));
      } else {
        throw new Error(res.message || "Đã có lỗi xảy ra");
      }
    } catch (error: any) {
      const description = extractErrorMessage(error);
      toast.error("Thất bại!", {
        description,
      });
    }
  };

  return {
    fetchFeatures,
  };
};

export default useFeature;
