import { IOption } from "@/components/common";
import { AppConstant } from "@/constant";
import { IPaginationModel } from "@/models/response.model";
import { rootReducer } from "@/redux/reducer";
import { RootState } from "@/redux/store";
import {
  PayloadAction,
  WithSlice,
  createSelector,
  createSlice,
} from "@reduxjs/toolkit";
import { ISchoolConfig } from "./schoolConfig.model";

export interface IMoetSchoolFilter {
  schoolCode?: string;
  doetCode?: IOption | string | null;
  divisionCode?: IOption | string | null;
  schoolLevelMoet?: IOption | string | null;
}

export const DEFAULT_MOET_SCHOOL_FILTER: IMoetSchoolFilter = {
  doetCode: null,
  divisionCode: null,
  schoolLevelMoet: null,
  schoolCode: "",
};

/* ------------- Initial State ------------- */
export interface IInitialState {
  isFetchingModal: boolean;
  error: object | string | null;
  pagination: IPaginationModel;
  totalDataModal: number;

  filterModal: IMoetSchoolFilter;
  moetSchoolList: ISchoolConfig[];
  renderTableList: ISchoolConfig[];
  selectedList: ISchoolConfig[];

  isSelectAll: boolean;
}

const initialState: IInitialState = {
  isFetchingModal: false,
  isSelectAll: false,
  error: null,
  pagination: AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
  totalDataModal: 0,

  filterModal: DEFAULT_MOET_SCHOOL_FILTER,
  moetSchoolList: [],
  renderTableList: [],
  selectedList: [],
};

/* ------------- Selector ------------- */
export const selectFilterModal = createSelector(
  [(state: RootState) => state.schoolConfigReducer?.filterModal ?? {}],
  (filterModal) => filterModal
);

export const selectTotalCountModal = createSelector(
  [(state: RootState) => state.schoolConfigReducer?.totalDataModal ?? 0],
  (totalDataModal) => totalDataModal
);

export const selectMoetSchoolList = createSelector(
  [(state: RootState) => state.schoolConfigReducer?.moetSchoolList ?? []],
  (moetSchoolList) => moetSchoolList
);

export const selectIsFetchingModal = createSelector(
  [(state: RootState) => state.schoolConfigReducer?.isFetchingModal ?? false],
  (isFetchingModal) => isFetchingModal
);

export const selectPagination = createSelector(
  [(state: RootState) => state.schoolConfigReducer?.pagination],
  (pagination) => pagination as IPaginationModel
);

export const selectRenderTableList = createSelector(
  [(state: RootState) => state.schoolConfigReducer?.renderTableList ?? []],
  (renderTableList) => renderTableList
);

export const selectIsSelectAll = createSelector(
  [(state: RootState) => state.schoolConfigReducer?.isSelectAll ?? false],
  (isSelectAll) => isSelectAll
);

export const selectSelectedList = createSelector(
  [(state: RootState) => state.schoolConfigReducer?.selectedList ?? []],
  (selectedList) => selectedList
);

/* ------------- Reducers ------------- */
const reducers = {
  getMoetSchoolDataList: (
    state: IInitialState,
    action: PayloadAction<IPaginationModel & any>
  ) => {
    state.isFetchingModal = true;
  },

  getMoetSchoolDataListSuccess: (
    state: IInitialState,
    action: PayloadAction<{ data: ISchoolConfig[]; totalCount: number }>
  ) => {
    state.isFetchingModal = false;
    state.moetSchoolList = action.payload.data;
    state.totalDataModal = action.payload.totalCount;
    state.renderTableList = getRenderTableList(
      action.payload.data,
      AppConstant.DEFAULT_PAGINATION_SKIP_TAKE
    );
  },

  postMultiMoetSchool: (
    state: IInitialState,
    action: PayloadAction<{ data: ISchoolConfig[]; onSuccess: () => void }>
  ) => {},

  handleChangePage: (
    state: IInitialState,
    action: PayloadAction<IPaginationModel>
  ) => {
    state.pagination = action.payload;
    state.renderTableList = getRenderTableList(
      state.moetSchoolList,
      action.payload
    );
  },

  handleChangeSelectAllCheckbox: (
    state: IInitialState,
    action: PayloadAction<boolean>
  ) => {
    state.isSelectAll = action.payload;
  },

  handleChangeSelectedCheckbox: (
    state: IInitialState,
    action: PayloadAction<ISchoolConfig>
  ) => {
    const index = state.selectedList.findIndex(
      (item) => item.schoolCode === action.payload.schoolCode
    );

    if (index > -1) {
      state.selectedList.splice(index, 1);
    } else {
      state.selectedList.push(action.payload);
    }
  },

  changeFilterModalWithKey: (
    state: IInitialState,
    action: PayloadAction<{ key: keyof IMoetSchoolFilter; value?: any }>
  ) => {
    const objectData = action.payload;
    state.filterModal[objectData.key] = objectData.value;
    state.pagination.skip = AppConstant.DEFAULT_PAGINATION_SKIP_TAKE.skip;
  },
};

export const schoolConfigSlice = createSlice({
  name: "schoolConfigReducer",
  initialState,
  reducers,
});

export const schoolConfigActions = schoolConfigSlice.actions;

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices
    extends WithSlice<typeof schoolConfigSlice> {}
}

// Inject reducer
const injectedschoolConfigSlice = schoolConfigSlice.injectInto(rootReducer);

export const schoolConfigSelectors = injectedschoolConfigSlice.selectors;

const getRenderTableList = (
  data: ISchoolConfig[],
  pagination: IPaginationModel
): ISchoolConfig[] => {
  const { skip, take } = pagination;
  return data.slice(skip, skip + take).map((item, index) => ({
    ...item,
    id: skip + index,
  }));
};
