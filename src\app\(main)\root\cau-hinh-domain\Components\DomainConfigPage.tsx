"use client";

import { TablePageLayout } from "@/components/common";
import StatusCell from "@/components/common/table/cell/StatusCell";
import { ITableRef } from "@/components/common/TablePageLayout/type";
import { ApiConstant, DataConstant } from "@/constant";
import { IDomainConfigModel } from "@/app/(main)/root/cau-hinh-domain/domain.model";
import { updateStatusService } from "@/services/app.service";
import { Box, Button, Typography } from "@mui/material";
import { ColumnDef } from "@tanstack/react-table";
import { useEffect, useRef, useState } from "react";
import FilterCustom from "./FilterCustom";
import { updateShareDomain } from "../hooks/updateShareDomain";
import ConfigDomainModalAction from "./ConfigDomainModalAction";
import { educationUnitsActions } from "@/redux/educationUnits.slice";
import { useAppDispatch, useAppStore } from "@/redux/hook";
import { createInjectableSaga } from "@/saga/injectableSaga";
import { educationUnitsSaga } from "@/saga/educationUnits.saga";

const DomainConfigPage = () => {
  const dispatch = useAppDispatch();
  const store = useAppStore();
  const [isClient, setIsClient] = useState(false);
  const tableRef = useRef<ITableRef>(null);
  const columns = getColumns(tableRef);

  useEffect(() => {
    createInjectableSaga(
      "educationUnitsReducer",
      educationUnitsSaga
    ).injectInto(store);
    setIsClient(true);

    return () => {
      dispatch(educationUnitsActions.educationUnitsReset());
    };
  }, []);

  if (!isClient) return null;

  return (
    <TablePageLayout<IDomainConfigModel>
      ref={tableRef}
      fetchAll={false}
      visibleCol={VISIBLE_COL}
      apiUrl={ApiConstant.DOMAIN_CONFIG}
      tableProps={{
        columns,
      }}
      filterConfig={[
        {
          key: "searchKey",
          type: "text",
          label: "Tìm kiếm",
          size: 2.4,
        },
        {
          key: "groupUnitCode",
          type: "select",
          label: "Đối tượng sử dụng",
          size: 2.4,
          options: DataConstant.DON_VI_LIST,
        },
        {
          key: "doetCode",
        },
        {
          key: "divisionCode",
        },
        {
          key: "schoolCode",
        },
      ]}
      collapseFilterCustom={(props) => <FilterCustom props={props} />}
      actions={["create", "update", "delete"]}
      formConfig={{
        deleteUrl: ApiConstant.DOMAIN_CONFIG,
        detailUrl: ApiConstant.DOMAIN_CONFIG,
      }}
      CreateModalComponent={({ isOpen, onClose }) => (
        <ConfigDomainModalAction
          open={isOpen}
          onClose={onClose}
          onSuccess={tableRef?.current?.fetchCurrentData}
        />
      )}
      EditModalComponent={({ isOpen, onClose, modalData }) => (
        <ConfigDomainModalAction
          open={isOpen}
          onClose={onClose}
          dataSelected={modalData}
          onSuccess={tableRef?.current?.fetchCurrentData}
        />
      )}
    />
  );
};

export default DomainConfigPage;

const getColumns = (
  tableRef: React.RefObject<ITableRef | null>
): ColumnDef<IDomainConfigModel>[] => [
  {
    id: "name",
    header: "Tên",
    accessorKey: "name",
    size: 220,
  },
  {
    id: "domainUrl",
    header: "Domain",
    accessorKey: "domainUrl",
    size: 300,
  },
  {
    id: "doetName",
    header: "Sở",
    accessorKey: "doetName",
    size: 300,
  },
  {
    id: "divisionName",
    header: "Phòng",
    accessorKey: "divisionName",
    size: 220,
  },
  {
    id: "schoolName",
    header: "Trường",
    accessorKey: "schoolName",
    size: 250,
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Kích hoạt",
    size: 60,
    cell: ({ row }) => (
      <StatusCell
        status={row.original.status}
        onStatusChange={(_, value) => {
          updateStatusService({
            id: row.original.id,
            status: Number(value),
            onSuccess: tableRef?.current?.fetchCurrentData,
            url: ApiConstant.STATUS_DOMAIN,
          });
        }}
      />
    ),
    meta: { align: "center" },
  },
  {
    id: "statusView",
    header: "Hiển thị",
    size: 150,
    meta: {
      align: "center",
    },
    cell: ({ row }) => {
      return (
        <Button
          onClick={() => {
            updateShareDomain({
              id: row.original.id,
              isShareDomain:
                row.original.isShareDomain === DataConstant.BOOLEAN_TYPE.true
                  ? DataConstant.BOOLEAN_TYPE.false
                  : DataConstant.BOOLEAN_TYPE.true,
              onSuccess: tableRef?.current?.fetchCurrentData,
              url: ApiConstant.SHARE_DOMAIN,
            });
          }}
          sx={{
            padding: "8px 16px",
            borderRadius: "8px",
            width: "fit-content",
            ...(row.original.isShareDomain === DataConstant.BOOLEAN_TYPE.true
              ? {
                  color: "success.main",
                  backgroundColor: "rgba(42, 171, 74, 0.10)",
                }
              : {
                  color: "warning.main",
                  backgroundColor: "rgba(252, 190, 32, 0.10)",
                }),
          }}
        >
          <Box
            sx={{
              width: 8,
              height: 8,
              borderRadius: "50%",
              backgroundColor:
                row.original.isShareDomain === DataConstant.BOOLEAN_TYPE.true
                  ? "success.main"
                  : "warning.main",
            }}
          />
          <Typography ml={1}>
            {row.original.isShareDomain === DataConstant.BOOLEAN_TYPE.true
              ? "Chung"
              : "Riêng"}
          </Typography>
        </Button>
      );
    },
  },
];

const VISIBLE_COL = [
  { id: "name", name: "Tên" },
  { id: "domainUrl", name: "Domain" },
  { id: "doetName", name: "Sở" },
  { id: "divisionName", name: "Phòng" },
  { id: "schoolName", name: "Trường" },
  { id: "status", name: "Kích hoạt" },
  { id: "statusView", name: "Hiển thị" },
];
