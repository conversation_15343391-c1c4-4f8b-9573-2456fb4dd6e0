import http from "@/api";
import { ApiConstant } from "@/constant";
import { MENU_COMBO } from "@/constant/api.const";
import { IMenuTree } from "@/models/menu.model";
import { DataListResponseModel } from "@/models/response.model";
import { extractErrorMessage } from "@/utils/common.utils";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import stringFormat from "string-format";

const useMenuOptions = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [menuOptions, setMenuOptions] = useState<IMenuTree[]>([]);
  const [menuTypeId, setMenuTypeId] = useState<number | null>(null);

  useEffect(() => {
    if (menuTypeId) {
      const fetchMenuOptions = async () => {
        try {
          const res: DataListResponseModel<IMenuTree> = await http.get(
            stringFormat(MENU_COMBO, { menuTypeId })
          );

          if (res.code === ApiConstant.ERROR_CODE_OK) {
            setMenuOptions(res.data.data);
          } else {
            throw new Error(res.message);
          }
        } catch (error) {
          const description = extractErrorMessage(error);
          toast.error("Thất bại!", {
            description,
          });
        } finally {
          setLoading(false);
        }
      };
      fetchMenuOptions();
    }
  }, [menuTypeId]);

  return {
    menuOptions,
    loading,
    menuTypeId,
    setMenuTypeId,
  };
};

export default useMenuOptions;
