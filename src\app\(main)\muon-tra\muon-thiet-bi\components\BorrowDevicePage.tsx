"use client";
import { TablePageLayout } from "@/components/common";
import {
  BORROW_DEVICE_STATUS_LIST,
  IBorrowDeviceModal,
} from "../borrowDevice.modal";
import { BORROW_DEVICE } from "@/constant/api.const";
import { ColumnDef } from "@tanstack/react-table";
import {
  ActionType,
  FilterConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import {
  SUBJECT,
  ROOM,
  DEVICE_TYPE,
  GET_GRADE,
  TEACHER_COMBO,
} from "@/constant/api.const";
import { useEffect, useRef, useCallback, useMemo, memo, useState } from "react";
import { useAppDispatch, useAppStore, useAppSelector } from "@/redux/hook";
import { createInjectableSaga } from "@/saga/injectableSaga";
import FilterCustom from "./FilterCustom";
import CollapseFilterCustom from "./CollapseFilterCustom";
import { borrowRequestSaga } from "@/saga/device/borrowRequest.saga";
import { borrowRequestActions } from "@/redux/device/borrowRequest.slice";
import { systemSaga } from "@/saga/system.saga";
import { selectSchoolWeekList, systemActions } from "@/redux/system.slice";
import dynamic from "next/dynamic";
import dayjs from "dayjs";
import { BORROW_TYPE, BorrowStatusEnum } from "@/models/eduDevice.model";
import { Typography } from "@mui/material";
import { v4 as uuid } from "uuid";
import { BorrowStatusCell } from "@/components/sn-common/BorrowStatusCell";
import { formatNumber } from "@/utils/format.utils";
import { shallowEqual } from "react-redux";

const BorrowDevices = dynamic(
  () =>
    import("@/app/(main)/thiet-bi/danh-sach-thiet-bi/components/BorrowDevices"),
  {
    ssr: false,
  }
);

const ApproveBorrow = dynamic(
  () => import("@/app/(main)/muon-tra/muon-thiet-bi/components/ApproveBorrow"),
  {
    ssr: false,
  }
);

const BorrowDevicePage = () => {
  const store = useAppStore();
  const dispatch = useAppDispatch();
  const schoolYearSelected = useAppSelector(
    (state) => state.appReducer.schoolYearSelected,
    shallowEqual
  );
  const schoolWeekList = useAppSelector(selectSchoolWeekList);

  const tableRef = useRef<ITableRef>(null);

  const [borrowType, setBorrowType] = useState(BORROW_TYPE.week);
  const [selectedRows, setSelectedRows] = useState<IBorrowDeviceModal[]>([]);
  const [tableSelection, setTableSelection] = useState<IBorrowDeviceModal[]>(
    []
  );

  useEffect(() => {
    createInjectableSaga("systemReducer", systemSaga).injectInto(store);
    createInjectableSaga("borrowRequestReducer", borrowRequestSaga).injectInto(
      store
    );

    return () => {
      dispatch(borrowRequestActions.reset());
      dispatch(systemActions.systemReset());
    };
  }, []);

  useEffect(() => {
    if (schoolYearSelected) {
      dispatch(systemActions.getSchoolWeekList({}));
    }
  }, [dispatch, schoolYearSelected]);

  useEffect(() => {
    if (schoolWeekList.length && borrowType === BORROW_TYPE.week) {
      setBorrowType(BORROW_TYPE.week);
      tableRef.current?.handleChangeFilter?.("fromDate")(
        schoolWeekList[schoolWeekList.length - 1].fromDate
      );
      tableRef.current?.handleChangeFilter?.("toDate")(
        schoolWeekList[schoolWeekList.length - 1].toDate
      );
    }
  }, [schoolWeekList]);

  const changeBorrowType = useCallback(
    (value) => setBorrowType(Number(value)),
    []
  );

  const filterConfig: FilterConfig[] = useMemo(() => {
    const currentDate = dayjs();
    let defaultWeek = schoolWeekList.find((week) => {
      const weekStart = dayjs(week.fromDate);
      const weekEnd = dayjs(week.toDate);
      return currentDate.isBetween(weekStart, weekEnd, "day", "[]");
    });
    if (!defaultWeek) {
      defaultWeek = schoolWeekList?.[schoolWeekList.length - 1];
    }
    return [
      {
        key: "borrowType",
        value: BORROW_TYPE.week,
        onChangeValue: changeBorrowType,
      },
      {
        key: "fromDate",
        value: defaultWeek?.fromDate,
        required: true,
      },
      {
        key: "toDate",
        value: defaultWeek?.toDate,
        required: true,
      },
      {
        key: "schoolSubjectIds",
        type: "select",
        label: "Môn học",
        size: 6,
        apiListUrl: SUBJECT,
        isAdvanced: true,
        isMulti: true,
        hasAllOption: true,
        fieldProps: {
          limitTags: 3,
        },
      },
      {
        key: "roomIds",
        type: "select",
        label: "Kho/Phòng",
        size: 6,
        apiListUrl: ROOM,
        isAdvanced: true,
        isMulti: true,
        hasAllOption: true,
        fieldProps: {
          limitTags: 2,
        },
      },
      {
        key: "schoolDeviceTypeIds",
        type: "select",
        label: "Loại thiết bị",
        size: 6,
        apiListUrl: DEVICE_TYPE,
        isAdvanced: true,
        isMulti: true,
        hasAllOption: true,
        fieldProps: {
          limitTags: 2,
        },
      },
      {
        key: "gradeCodes",
        type: "select",
        label: "Khối lớp",
        size: 6,
        apiListUrl: GET_GRADE,
        selectedKey: "code",
        isAdvanced: true,
        isMulti: true,
        hasAllOption: true,
        fieldProps: {
          limitTags: 3,
        },
      },
      {
        key: "status",
        type: "select",
        label: "Trạng thái",
        size: 6,
        isAdvanced: true,
        options: BORROW_DEVICE_STATUS_LIST,
      },
      {
        key: "teacherIds",
        type: "select",
        label: "Giáo viên",
        size: 6,
        isAdvanced: true,
        isMulti: true,
        hasAllOption: true,
        apiListUrl: TEACHER_COMBO,
      },
      {
        key: "searchKey",
        type: "text",
        label: "Tìm kiếm",
        isCollapse: true,
      },
    ];
  }, [schoolWeekList]);

  const formatData = useCallback(
    (data: IBorrowDeviceModal[], filter) => {
      if (filter.borrowType !== BORROW_TYPE.week) {
        return data;
      }

      if (!data || data.length === 0) {
        return [];
      }

      const groups: Record<string, IBorrowDeviceModal[]> = {};

      data.forEach((device) => {
        const dateKey = dayjs(device.fromDate).format("YYYY-MM-DD");
        if (!groups[dateKey]) groups[dateKey] = [];
        groups[dateKey].push(device);
      });

      return Object.entries(groups).map(([dateStr, items]) => {
        const d = dayjs(dateStr);
        return {
          id: uuid(),
          teacherName: `${capitalize(d.format("dddd"))} (${d.format("DD/MM")})`,
          isHeader: true,
          children: items,
        } as any;
      });
    },
    [borrowType]
  );

  const fetchCurrentData = useCallback(() => {
    tableRef.current?.fetchCurrentData?.();
  }, []);

  const collapseFilterCustomRender = useCallback((props: any) => {
    return <CollapseFilterCustom props={props} />;
  }, []);

  const customActions = useMemo(
    () => (
      <>
        <BorrowDevices fetchCurrentData={fetchCurrentData} />
        <ApproveBorrow
          fetchCurrentData={fetchCurrentData}
          selectedRows={selectedRows}
          borrowType={borrowType}
          onClearSelectedRows={() => {
            setTableSelection([]);
          }}
        />
      </>
    ),
    [fetchCurrentData, selectedRows, borrowType]
  );

  const tableProps = useMemo(
    () => ({
      columns: getColumns(borrowType),
      rowSelected: tableSelection,
      options: {
        getSubRows: (row) =>
          borrowType === BORROW_TYPE.week && "children" in row
            ? (row.children as IBorrowDeviceModal[])
            : undefined,
        enableRowSelection: (row) => {
          return row.original.status === BorrowStatusEnum.Register;
        },
      },
      onRowSelectionChange: (selectedRowsData: IBorrowDeviceModal[]) => {
        if (borrowType === BORROW_TYPE.week) {
          const actualSelectedRows = selectedRowsData.filter(
            (row: any) => !row.isHeader && row.deviceCode
          );
          setSelectedRows(actualSelectedRows);
        } else {
          setSelectedRows(selectedRowsData);
        }
      },
    }),
    [borrowType, tableSelection]
  );

  return (
    <TablePageLayout<IBorrowDeviceModal>
      ref={tableRef}
      onResetFilter={(v) => {
        setBorrowType(Number(v?.find((f) => f.key === "borrowType")?.value));
      }}
      apiUrl={BORROW_DEVICE}
      methodFetch="POST"
      hasBaseCol={false}
      tableProps={tableProps}
      filterConfig={filterConfig}
      filterCustom={(props) => <FilterCustom props={props} />}
      collapseFilterCustom={collapseFilterCustomRender}
      customActions={customActions}
      formatData={formatData}
      actions={ACTIONS}
    />
  );
};

const ACTIONS: ActionType[] = ["check"];

const Header = memo(({ row }: { row }) => {
  useEffect(() => {
    row.toggleExpanded(true);
  }, []);

  if ((row.original as any).isHeader) {
    return (
      <Typography
        sx={{
          color: "primary.main",
          fontWeight: 600,
        }}
      >
        {row.original.teacherName}
      </Typography>
    );
  }
  return row.original.teacherName;
});

export const capitalize = (str: string) =>
  str.charAt(0).toUpperCase() + str.slice(1);

export default memo(BorrowDevicePage);

const getColumns = (borrowType): ColumnDef<IBorrowDeviceModal>[] => [
  {
    header: "Giáo viên",
    accessorKey: "teacherName",
    cell: ({ row }) => <Header row={row} />,
    meta: {
      colSpanOnParentRow: borrowType === BORROW_TYPE.week ? 13 : undefined,
    },
    size: 60,
  },
  {
    header: "Mã thiết bị",
    accessorKey: "deviceCode",
    cell: ({ row }) => {
      if ((row.original as any).isHeader) return null;
      return row.original.deviceCode;
    },
    size: 60,
  },
  {
    header: "Tên thiết bị",
    accessorKey: "deviceName",
    cell: ({ row }) =>
      (row.original as any).isHeader ? null : row.original.deviceName,
    size: 60,
  },
  {
    header: "Loại thiết bị",
    accessorKey: "schoolDeviceTypeName",
    cell: ({ row }) =>
      (row.original as any).isHeader ? null : row.original.schoolDeviceTypeName,
    size: 60,
  },
  {
    header: "SL đăng ký",
    accessorKey: "quantity",
    cell: ({ row }) =>
      (row.original as any).isHeader
        ? null
        : formatNumber(row.original.quantity || 0),
    meta: {
      align: "right",
    },
    size: 60,
  },
  {
    header: "SL trong kho",
    accessorKey: "totalBorrowReady",
    cell: ({ row }) =>
      (row.original as any).isHeader
        ? null
        : formatNumber(row.original.totalBorrowReady || 0),
    meta: {
      align: "right",
    },
    size: 60,
  },
  {
    header: "Đơn vị tính",
    accessorKey: "deviceUnitName",
    cell: ({ row }) =>
      (row.original as any).isHeader ? null : row.original.deviceUnitName,
    size: 60,
  },
  {
    header: "Môn học",
    accessorKey: "subjectName",
    cell: ({ row }) =>
      (row.original as any).isHeader ? null : row.original.subjectName,
    size: 60,
  },
  {
    header: "Khối lớp",
    accessorKey: "gradeName",
    cell: ({ row }) =>
      (row.original as any).isHeader
        ? null
        : row.original.gradeName || row.original.gradeCode,
    size: 60,
  },
  {
    header: "Phòng học",
    accessorKey: "roomName",
    cell: ({ row }) =>
      (row.original as any).isHeader ? null : row.original.roomName,
    size: 60,
  },
  {
    header: "Trạng thái",
    accessorKey: "status",
    cell: ({ row }) =>
      (row.original as any).isHeader ? null : (
        <BorrowStatusCell status={row.original.status} />
      ),
    size: 60,
    meta: {
      align: "center",
    },
  },
];
