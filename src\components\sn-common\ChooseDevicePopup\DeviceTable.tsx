"use client";

import { TablePageLayout } from "@/components/common";
import { DEVICE_LIST } from "@/constant/api.const";
import { ColumnDef } from "@tanstack/react-table";
import React, { memo } from "react";
import { Button } from "@mui/material";
import { formatNumber } from "@/utils/format.utils";
import { IDeviceParams, IEduDevice } from "@/models/eduDevice.model";

interface DeviceTableProps {
  onSelectDevice?: (device: any) => void;
  deviceFilter?: IDeviceParams;
}

const DeviceTable = ({ onSelectDevice, deviceFilter }: DeviceTableProps) => {
  const columns = getColumns(onSelectDevice);

  return (
    <TablePageLayout<IEduDevice>
      apiUrl={DEVICE_LIST}
      tableProps={{
        columns,
        tableContainerProps: {
          sx: {
            height: "400px",
          },
        },
      }}
      filterConfig={[
        {
          key: "searchKey",
          type: "text",
          label: "<PERSON><PERSON><PERSON> kiếm",
          size: 4,
        },
        {
          key: "isAvailable",
          value: "true",
        },
        {
          key: "deviceDefinitionId",
          value: deviceFilter?.deviceDefinitionId,
        },
      ]}
    />
  );
};

export default memo(DeviceTable);

const getColumns = (
  onSelectDevice?: (device: any) => void
): ColumnDef<IEduDevice>[] => [
  {
    id: "select",
    header: "",
    size: 60,
    meta: { align: "center" },
    cell: ({ row }) => (
      <Button
        variant="contained"
        size="small"
        onClick={() => onSelectDevice?.(row.original)}
      >
        Chọn
      </Button>
    ),
  },
  {
    id: "code",
    header: "Mã thiết bị",
    accessorKey: "code",
    size: 40,
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
    size: 150,
  },
  {
    id: "roomName",
    accessorKey: "roomName",
    header: "Kho/phòng",
    size: 60,
  },
  {
    id: "schoolSubjectName",
    accessorKey: "schoolSubjectName",
    header: "Môn học",
    size: 60,
  },
  {
    id: "totalAvailable",
    accessorKey: "totalAvailable",
    header: "Số lượng",
    size: 40,
    meta: { align: "right" },
    cell: ({ row }) => formatNumber(row.original.totalAvailable),
  },
];
