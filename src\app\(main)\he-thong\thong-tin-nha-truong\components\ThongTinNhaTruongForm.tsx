"use client";

import { <PERSON>, <PERSON><PERSON>, <PERSON>ack, Typography } from "@mui/material";
import {
  AppFormAutocomplete,
  AppFormTextField,
  GridFormContainer,
  AppFormToggle,
  AppFormRadio,
} from "@/components/common";
import { useForm, FormProvider } from "react-hook-form";
import AppFormTextEditor from "@/components/common/form/AppFormTextEditor";
import AppUploadFileControl from "@/components/common/form/AppUploadFileControl";

export enum VIEW_BOOK_BY_BOOK_TYPE {
  KHO_SACH = 1,
  THU_MUC = 0,
}

export const VIEW_BOOK_BY_BOOK_TYPE_LIST = [
  { id: VIEW_BOOK_BY_BOOK_TYPE.KHO_SACH, label: "Kho sách" },
  { id: VIEW_BOOK_BY_BOOK_TYPE.THU_MUC, label: "Th<PERSON> mục" },
];

export enum SCHOOL_TYPE {
  CONG_LAP = "01",
  DAN_LAP = "03",
  TU_THUC = "09",
  NGOAI_CONG_LAP = "02",
}

export const SCHOOL_TYPE_LIST = [
  {
    id: SCHOOL_TYPE.CONG_LAP,
    label: "Trường công lập",
  },
  {
    id: SCHOOL_TYPE.DAN_LAP,
    label: "Trường dân lập",
  },
  {
    id: SCHOOL_TYPE.TU_THUC,
    label: "Trường tư thục",
  },
  {
    id: SCHOOL_TYPE.NGOAI_CONG_LAP,
    label: "Trường ngoại công lập",
  },
];

const ThongTinNhaTruongForm = () => {
  const methods = useForm({
    values: {
      address: "",
      email: "",
      doetCode: "",
      divisionName: "",
      linkFacebook: "",
      linkInstagram: "",
      linkTiktok: "",
      linkYoutube: "",
      linkZalo: "",
      loginToRead: 0,
      logo: "",
      managementOrganization: "",
      name: "",
      phone: "",
      principal: "",
      quote: "",
      schoolCode: "",
      schoolType: "",
      shortName: "",
      viewBookByBookType: 0,
      website: "",
    },
  });

  const { control, handleSubmit } = methods;

  const onSubmit = () => {
    console.log(methods.getValues());
  };

  return (
    <FormProvider {...methods}>
      <Box sx={{ p: 3 }}>
        <form onSubmit={handleSubmit(onSubmit)}>
          {/* Header */}
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            sx={{
              mb: 3,
              borderBottom: "1px solid #e0e0e0",
              paddingBottom: 2,
            }}
          >
            <Box
              sx={{
                display: "flex",
                gap: 1,
                width: "100%",
                justifyContent: "flex-end",
              }}
            >
              <Button variant="contained" color="success" type="submit">
                Ghi
              </Button>
            </Box>
          </Stack>

          <Stack spacing={4} direction="row">
            <Box sx={{ width: "100%" }}>
              <GridFormContainer sx={{ width: "100%" }}>
                <Stack spacing={2} sx={{ width: "100%" }}>
                  {/* Thông tin chung */}
                  <Typography variant="h3" fontWeight={600} sx={{ pt: 2 }}>
                    Thông tin chung
                  </Typography>
                  <Stack direction="row" spacing={2}>
                    <AppFormTextField
                      label="Tên trường"
                      control={control}
                      name="name"
                      rules={{ required: "Vui lòng nhập tên trường" }}
                      sx={{
                        width: "100%",
                      }}
                    />
                    <AppFormTextField
                      label="Mã trường BGD"
                      control={control}
                      name="schoolCode"
                      controlProps={{
                        disabled: true,
                      }}
                      sx={{
                        width: "100%",
                      }}
                    />
                  </Stack>
                  <Stack direction="row" spacing={2}>
                    <AppFormTextField
                      label="Sở giáo dục"
                      control={control}
                      name="doetCode"
                      controlProps={{
                        disabled: true,
                      }}
                      sx={{
                        width: "100%",
                      }}
                    />
                    <AppFormTextField
                      label="Phòng giáo dục"
                      control={control}
                      name="divisionName"
                      controlProps={{
                        disabled: true,
                      }}
                      sx={{
                        width: "100%",
                      }}
                    />
                  </Stack>
                  <Stack direction="row" spacing={2}>
                    <AppFormTextField
                      label="Tên viết tắt"
                      control={control}
                      name="shortName"
                      sx={{
                        width: "100%",
                      }}
                      rules={{ required: "Vui lòng nhập tên viết tắt" }}
                    />
                    <AppFormAutocomplete
                      label="Loại hình trường"
                      control={control}
                      name="schoolType"
                      options={SCHOOL_TYPE_LIST}
                      sx={{
                        width: "100%",
                      }}
                      rules={{ required: "Vui lòng chọn loại hình trường" }}
                    />
                  </Stack>
                  <Stack direction="row" spacing={2}>
                    <AppFormTextField
                      label="Hiệu trưởng"
                      control={control}
                      name="principal"
                      sx={{
                        width: "100%",
                      }}
                      rules={{ required: "Vui lòng nhập hiệu trưởng" }}
                    />
                    <AppFormTextField
                      label="Điện thoại nhà trường"
                      control={control}
                      name="phone"
                      sx={{
                        width: "100%",
                      }}
                      rules={{ required: "Vui lòng nhập số điện thoại" }}
                    />
                  </Stack>
                  <Stack direction="row" spacing={2}>
                    <AppFormTextField
                      label="Email"
                      control={control}
                      name="email"
                      sx={{
                        width: "100%",
                      }}
                      rules={{ required: "Vui lòng nhập email" }}
                    />
                    <AppFormTextField
                      label="Website trang chủ thư viện"
                      control={control}
                      name="website"
                      sx={{
                        width: "100%",
                      }}
                      controlProps={{
                        disabled: true,
                      }}
                    />
                  </Stack>
                  <AppFormTextField
                    label="Địa chỉ"
                    control={control}
                    name="address"
                    sx={{
                      width: "100%",
                    }}
                    rules={{ required: "Vui lòng nhập địa chỉ" }}
                  />
                  <AppFormTextField
                    label="Đơn vị chủ quản"
                    control={control}
                    name="managementOrganization"
                    sx={{
                      width: "100%",
                    }}
                  />

                  {/* Cấu hình truy cập sách */}
                  <Typography variant="h3" fontWeight={600} sx={{ pt: 2 }}>
                    Cấu hình truy cập sách
                  </Typography>
                  <Stack direction="row" spacing={2}>
                    <AppFormToggle
                      label="Yêu cầu đăng nhập cho tất cả sách"
                      control={control}
                      name="loginToRead"
                      sx={{
                        width: "100%",
                      }}
                    />
                    <AppFormRadio
                      label="Hiển thị sách giấy theo"
                      control={control}
                      name="viewBookByBookType"
                      radioList={VIEW_BOOK_BY_BOOK_TYPE_LIST}
                      sx={{
                        width: "100%",
                      }}
                    />
                  </Stack>

                  {/* Thông tin mạng xã hội */}
                  <Typography variant="h3" fontWeight={600} sx={{ pt: 2 }}>
                    Thông tin mạng xã hội
                  </Typography>
                  <Stack direction="row" spacing={2}>
                    <AppFormTextField
                      label="Facebook"
                      control={control}
                      name="linkFacebook"
                      sx={{
                        width: "100%",
                      }}
                    />
                    <AppFormTextField
                      label="Youtube"
                      control={control}
                      name="linkYoutube"
                      sx={{
                        width: "100%",
                      }}
                    />
                  </Stack>
                  <Stack direction="row" spacing={2}>
                    <AppFormTextField
                      label="Tiktok"
                      control={control}
                      name="linkTiktok"
                      sx={{
                        width: "100%",
                      }}
                    />
                    <AppFormTextField
                      label="Instagram"
                      control={control}
                      name="linkInstagram"
                      sx={{
                        width: "100%",
                      }}
                    />
                  </Stack>
                  <Stack direction="row" spacing={2}>
                    <AppFormTextField
                      label="Zalo"
                      control={control}
                      name="linkZalo"
                      sx={{
                        width: "100%",
                      }}
                    />
                    <Box sx={{ width: "100%" }} />
                  </Stack>
                  {/* Trích dẫn (footer) */}
                  <Typography variant="h3" fontWeight={600} sx={{ pt: 2 }}>
                    Trích dẫn (footer)
                  </Typography>
                  <AppFormTextEditor
                    label="Quote "
                    control={control}
                    name="quote"
                    sx={{
                      width: "100%",
                    }}
                  />
                </Stack>
              </GridFormContainer>
            </Box>

            {/* Logo */}
            <Box sx={{ width: "40%" }}>
              <GridFormContainer>
                <AppUploadFileControl
                  control={control}
                  name="logo"
                  sx={{
                    width: "100%",
                    aspectRatio: "1/1",
                    pt: 4,
                  }}
                  title="Tải lên ảnh Logo trường"
                  description="Hỗ trợ các định dạng đuôi JPG, PNG, JPEG"
                  dropzoneOptions={{
                    accept: {
                      "image/*": [".jpg", ".jpeg", ".png"],
                    },
                  }}
                />
              </GridFormContainer>
            </Box>
          </Stack>
        </form>
      </Box>
    </FormProvider>
  );
};

export default ThongTinNhaTruongForm;
