import React, { memo, useState } from "react";
import { PlusIcon } from "@/components/icons";
import { Button, ButtonProps } from "@mui/material";
import dynamic from "next/dynamic";
const ChooseDeviceModal = dynamic(() => import("./ChooseDeviceModal"), {
  ssr: false,
});
const ChooseButton = ({
  onClick,
  children = "Chọn thiết bị",
  onSuccess,
  initDate,
  ...otherProps
}: ButtonProps & {
  onSuccess?: () => void;
  initDate?;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <Button
        startIcon={<PlusIcon />}
        onClick={(e) => {
          e.stopPropagation();
          setIsOpen(true);
          onClick?.(e);
        }}
        variant="contained"
        size="small"
        {...otherProps}
      >
        {children}
      </Button>
      <ChooseDeviceModal
        onSuccess={onSuccess}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        initDate={initDate}
      />
    </>
  );
};

export default memo(ChooseButton);
