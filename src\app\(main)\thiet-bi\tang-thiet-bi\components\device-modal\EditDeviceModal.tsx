import { AppModal } from "@/components/common";
import { AppModalProps } from "@/components/common/modal/AppModal";
import { Button } from "@mui/material";
import React, { memo, useCallback } from "react";
import {
  FormProvider,
  useForm,
  useFormContext,
  useWatch,
} from "react-hook-form";
import DeviceForm from "./DeviceForm";
import {
  IDeviceRoomGroup,
  IDeviceTransactionAction,
} from "../../equipmentDocument.model";
import { useAppDispatch } from "@/redux/hook";
import { equipmentDocumentActions } from "../../equipmentDocument.slice";
import { getEntriesEquipmentDocumentAndDevice } from "../../helper";
import useValidForm from "./hooks/useValidForm";
import { INIT_VALUE } from "./AddDeviceModal";
import { useInitializeForm } from "./hooks/useInitializeForm";
import { toast } from "sonner";

const EditDeviceModal = ({
  onClose,
  data,
  ...otherProps
}: EditDeviceModalProps) => {
  const { control: controlParent } = useFormContext<IDeviceTransactionAction>();
  const documentDate = useWatch({
    control: controlParent,
    name: "documentDate",
  });

  const dispatch = useAppDispatch();
  const handleValid = useValidForm();
  const methods = useForm({
    defaultValues: INIT_VALUE,
  });
  const { handleSubmit, setValue, reset } = methods;
  useInitializeForm({ data, setValue });

  const handleClose = () => {
    reset(INIT_VALUE);

    onClose();
    reset(INIT_VALUE);
  };

  const handleSubmitData = useCallback(
    (dataForm) => {
      const isError = dataForm.devices.some((item) => !item.roomId);
      if (isError) {
        toast.warning("Cảnh báo", {
          description: "Vui lòng chọn Kho phòng cho tất cả thiết bị!",
        });
        return;
      }
      const { equipmentDocumentEntry, device } =
        getEntriesEquipmentDocumentAndDevice({
          ...dataForm,
          id: data?.deviceDefinitionId,
        });

      if (device.length === 0) {
        toast.warning("Cảnh báo", {
          description: "Danh sách thiết bị không được bỏ trống!",
        });
        return;
      }

      handleValid(equipmentDocumentEntry, () => {
        dispatch(
          equipmentDocumentActions.editEquipmentDocumentEntry({
            equipmentDocumentEntry,
            device,
          })
        );
        handleClose();
      });
    },
    [data?.id]
  );

  return (
    <FormProvider {...methods}>
      <AppModal
        fullScreen
        component="form"
        onSubmit={(e) => {
          e.stopPropagation();
          handleSubmit(handleSubmitData)(e);
        }}
        modalTitleProps={{
          title: "Sửa thiết bị",
        }}
        maxWidth="lg"
        fullWidth
        onClose={handleClose}
        slotProps={{
          paper: {
            sx: {
              height: "100%",
            },
          },
        }}
        modalContentProps={{
          sx: {
            px: "12px",
            py: "12px",
            bgcolor: "background.grey",
          },
          content: (
            <DeviceForm
              isEdit={typeof data?.id === "number"}
              documentDate={documentDate}
            />
          ),
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                onClick={handleClose}
                color="secondary"
                variant="outlined"
              >
                Đóng
              </Button>
              <Button variant="contained" type="submit">
                Ghi
              </Button>
            </>
          ),
        }}
        {...otherProps}
      />
    </FormProvider>
  );
};

type EditDeviceModalProps = AppModalProps & {
  data: null | IDeviceRoomGroup;
};

export default memo(EditDeviceModal);
