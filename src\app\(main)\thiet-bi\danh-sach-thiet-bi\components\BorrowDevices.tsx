"use client";

import { But<PERSON> } from "@mui/material";
import React, { memo, useState } from "react";
import CreateModal from "@/app/(main)/muon-tra/ke-hoach-muon/components/CreateModal";

const BorrowDevices = ({
  fetchCurrentData,
}: {
  fetchCurrentData?: () => void;
}) => {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Button variant="contained" color="primary" onClick={() => setOpen(true)}>
        <PERSON><PERSON><PERSON><PERSON> thiết bị
      </Button>
      <CreateModal
        isOpen={open}
        onClose={() => setOpen(false)}
        fetchCurrentData={fetchCurrentData}
      />
    </>
  );
};

export default memo(BorrowDevices);
