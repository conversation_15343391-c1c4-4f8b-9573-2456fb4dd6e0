"use client";

import AppModal, { AppModalProps } from "@/components/common/modal/AppModal";
import React, { memo, useCallback, useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { Box, Button } from "@mui/material";
import { useAppDispatch } from "@/redux/hook";
import { systemActions } from "@/redux/system.slice";
import { deviceLiquidationActions } from "../deviceLiquidation.slice";
import InformationForm from "./InformationForm";
import TabTable from "./TabTable";
import { IDeviceLiquidationEdit } from "../deviceLiquidation.model";

const EditModal = ({
  fetchCurrentData,
  onClose,
  modalData,
  ...otherProps
}: EditModalProps) => {
  const dispatch = useAppDispatch();

  const methods = useForm({
    defaultValues: INIT_VALUE,
  });

  const { handleSubmit, reset, setValue, control } = methods;

  const handleClose = () => {
    dispatch(deviceLiquidationActions.resetModalDetails());
    dispatch(deviceLiquidationActions.resetSelectedTeacherModal());
    reset(INIT_VALUE);
    onClose();
  };

  const handleSubmitData = useCallback(
    (data) => {
      dispatch(
        deviceLiquidationActions.updateDeviceLiquidation({
          id: modalData?.id,
          data,
          onSuccess: () => {
            fetchCurrentData?.();
            handleClose();
          },
        })
      );
    },
    [fetchCurrentData, modalData]
  );

  useEffect(() => {
    if (modalData?.id) {
      setValue("documentNumber", modalData.documentNumber);
      setValue("documentDate", modalData.documentDate);
      setValue("notes", modalData.notes);
      dispatch(deviceLiquidationActions.initEditForm(modalData));
    }
  }, [modalData?.id]);

  return (
    <FormProvider {...methods}>
      <AppModal
        component="form"
        onClose={handleClose}
        onSubmit={handleSubmit(handleSubmitData)}
        fullScreen
        modalTitleProps={{
          title: "Cập nhật thanh lý thiết bị",
        }}
        modalContentProps={{
          sx: {
            display: "flex",
            flexDirection: "column",
            bgcolor: "background.grey",
          },
          content: (
            <>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 1.5 }}>
                <InformationForm control={control} />
                <TabTable />
              </Box>
            </>
          ),
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                onClick={handleClose}
                variant="outlined"
                color="secondary"
              >
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
        {...otherProps}
      />
    </FormProvider>
  );
};

export default memo(EditModal);

type EditModalProps = AppModalProps & {
  fetchCurrentData?: () => void;
  modalData: IDeviceLiquidationEdit | null;
};

const INIT_VALUE = {
  documentNumber: "",
  documentDate: "",
  notes: "",
};
