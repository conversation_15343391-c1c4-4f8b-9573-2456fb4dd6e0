import { AppTable } from "@/components/common";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import { BORROW_TYPE, IBorrowRequestRoom } from "@/models/eduDevice.model";
import { ColumnDef } from "@tanstack/react-table";
import React, { memo, useEffect, useMemo } from "react";
import { BorrowStatusCell } from "@/components/sn-common/BorrowStatusCell";
import { useAppSelector } from "@/redux/hook";
import { selectDeviceNameAttach } from "@/redux/device/borrowRequest.slice";
import dayjs from "dayjs";
import { capitalize, Typography } from "@mui/material";
import { v4 as uuid } from "uuid";

dayjs.locale("vi");

const DeviceRegisterRoom = ({ data, borrowType }: { data; borrowType }) => {
  const dataView = useMemo(
    () => groupRoomsByDate(data, borrowType),
    [data, borrowType]
  );

  return (
    <AppTable
      columns={getColumns(borrowType)}
      data={dataView}
      totalData={data?.length ?? 0}
      options={{
        getSubRows: (row) =>
          borrowType === BORROW_TYPE.week && "children" in row
            ? row.children
            : undefined,
      }}
      {...TABLE_MODAL_FULL_HEIGHT}
    />
  );
};

export default memo(DeviceRegisterRoom);

const getColumns = (
  borrowType: number
): ColumnDef<GroupedRoom | IBorrowRequestRoom>[] => [
  {
    id: "periodName",
    accessorKey: "periodName",
    header: "Tiết học",
    size: 100,
    cell: ({ row }) => <PeriodCell row={row} borrowType={borrowType} />,
    meta: {
      colSpanOnParentRow: borrowType === BORROW_TYPE.week ? 4 : undefined,
    },
  },
  {
    id: "roomId",
    accessorKey: "roomName",
    header: "Kho/phòng",
    size: 150,
  },
  {
    id: "deviceName",
    accessorKey: "deviceName",
    size: 150,
    header: "Mượn kèm thiết bị",
    cell: ({ row }) => (
      <DeviceAttach roomDeviceGuid={row.original.roomDeviceGuid} />
    ),
  },
  {
    header: "Trạng thái",
    meta: {
      align: "center",
    },
    id: "status",
    size: 50,
    cell: ({ row }) => <BorrowStatusCell status={row.original.status} />,
  },
];

const DeviceAttach = ({
  roomDeviceGuid,
}: {
  roomDeviceGuid?: string | null;
}) => {
  const attachedDeviceText = useAppSelector(
    selectDeviceNameAttach(roomDeviceGuid)
  );
  return <>{attachedDeviceText}</>;
};

// Type for parent row
interface GroupedRoom extends IBorrowRequestRoom {
  text: string;
  children: IBorrowRequestRoom[];
}

// Grouping logic giống như bên thiết bị
export const groupRoomsByDate = (
  rooms: IBorrowRequestRoom[],
  borrowType?: number
): (IBorrowRequestRoom | GroupedRoom)[] => {
  if (typeof borrowType !== "number") return [];

  if (borrowType === BORROW_TYPE.longTerm) return rooms;

  const groups: Record<string, IBorrowRequestRoom[]> = {};

  rooms.forEach((room) => {
    const dateKey = dayjs(room.fromDate).format("YYYY-MM-DD");
    if (!groups[dateKey]) groups[dateKey] = [];
    groups[dateKey].push(room);
  });

  return Object.entries(groups).map(([dateStr, items]) => {
    const ref = items[0];
    const d = dayjs(dateStr);
    return {
      ...ref,
      id: uuid(),
      text: `${capitalize(d.format("dddd"))} (${d.format("DD/MM")})`,
      children: items,
    };
  });
};

const PeriodCell = ({ row, borrowType }: { row: any; borrowType: number }) => {
  useEffect(() => {
    row.toggleExpanded(true);
  }, []);

  if (borrowType === BORROW_TYPE.longTerm || row.depth !== 0) {
    return row.original.periodName;
  }

  return (
    <Typography color="primary" fontWeight={500}>
      {row.original.text}
    </Typography>
  );
};
