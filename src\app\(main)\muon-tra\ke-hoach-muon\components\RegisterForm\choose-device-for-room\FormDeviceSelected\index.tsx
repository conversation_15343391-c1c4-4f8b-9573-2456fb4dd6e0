import { Grid, Stack } from "@mui/material";
import React, { memo } from "react";
import DeviceTable from "./DeviceTable";
import DeviceSelectedTable from "./DeviceSelectedTable";
import { useFieldArray, useFormContext } from "react-hook-form";
import { ModalSelectedType } from "../ChooseDeviceOfRoomModal";

const FormDeviceSelected = () => {
  const { control } = useFormContext<ModalSelectedType>();

  const { fields, append, replace, remove } = useFieldArray({
    control,
    name: "deviceSelected",
  });

  return (
    <Grid container spacing={3} height="100%">
      <Grid size={6} height="100%">
        <DeviceTable onSelected={append} fields={fields} />
      </Grid>
      <Grid size={6} height="100%">
        <DeviceSelectedTable
          fields={fields}
          onRemove={remove}
          onRemoveAll={replace}
        />
      </Grid>
    </Grid>
  );
};

export default memo(FormDeviceSelected);
