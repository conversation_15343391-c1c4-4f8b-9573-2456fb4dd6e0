import TransferDeviceTable from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/components/TransferDevice/TransferDeviceTable";
import {
  deviceActions,
  selectorTransferInput,
} from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.slice";
import useTransferDevice from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/hooks/useTransferDevice";
import { AppModal } from "@/components/common";
import { handleConfigHeightOfTableInModal } from "@/hooks/useConfigHeightOfTableInModal";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { systemActions } from "@/redux/system.slice";
import { Button } from "@mui/material";
import React, { useCallback, useEffect } from "react";

const TransferDevice = ({
  open,
  onClose,
  fetchCurrentData,
}: {
  open: boolean;
  onClose: () => void;
  fetchCurrentData?: () => void;
}) => {
  const dispatch = useAppDispatch();
  const deviceTransfer = useAppSelector(selectorTransferInput);

  const { handleTransferDevice } = useTransferDevice();

  const handleClose = useCallback(() => {
    dispatch(deviceActions.resetTransfer());
    onClose();
  }, [dispatch, onClose]);

  const onTransferDevice = useCallback(() => {
    handleTransferDevice(deviceTransfer, () => {
      handleClose();
      fetchCurrentData?.();
    });
  }, [deviceTransfer, fetchCurrentData, handleClose]);

  useEffect(() => {
    if (open) {
      dispatch(systemActions.getRoomList());
    }
  }, [open]);

  return (
    <AppModal
      isOpen={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      sx={{
        "& .MuiDialog-paper": {
          height: "100%",
        },
        "& .MuiDialogContent-root": {
          px: 0,
        },
      }}
      modalTitleProps={{
        title: "Điều chuyển đến",
      }}
      modalContentProps={{
        content: <TransferDeviceTable />,
      }}
      modalActionsProps={{
        children: (
          <>
            <Button variant="outlined" color="secondary" onClick={handleClose}>
              Đóng
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={onTransferDevice}
            >
              Điều chuyển
            </Button>
          </>
        ),
      }}
    />
  );
};

export default TransferDevice;
