import { IFunction } from "@/app/(main)/he-thong/phan-quyen/type";
import { IFeature } from "@/app/(main)/root/quan-ly-tinh-nang/type";
import { AppConstant, PathConstant } from "@/constant";
import {
  IMenu,
  IMenuItemTree,
  IMenuTree,
  IMenuGroup,
} from "@/models/menu.model";
import { ITreeData } from "@/models/types";

/**
 * Tìm kiếm menu theo id
 * @param items - Danh sách menu
 * @param itemId - Id của menu cần tìm
 * @returns Menu tìm thấy hoặc undefined nếu không tìm thấy
 */
export const findMenuConfigTree = (
  items: ITreeData[],
  itemId?: number
): ITreeData | undefined => {
  if (!itemId) {
    return undefined;
  }
  for (const item of items) {
    if (item.id === itemId) {
      return item;
    }
    const foundInChildren = findMenuConfigTree(item.children || [], itemId);
    if (foundInChildren) {
      return foundInChildren;
    }
  }
  return undefined;
};

/**
 * Chuyển đổi dữ liệu thành cây
 * @param inputData - Danh sách menu hoặc feature
 * @param parentId - Id của menu cha
 * @returns Danh sách cây
 */
export const convertDataTreeItem = (
  inputData: (IMenuTree | IFeature | IFunction)[],
  parentId: number | string = AppConstant.NONE_ID
): ITreeData[] => {
  const result: ITreeData[] = [];

  inputData.forEach((item) => {
    // Kiểm tra đúng cha
    if (item.parentId !== parentId) return;

    // Type guard: Kiểm tra xem có phải là IFeature
    const isFeature = (obj: any): obj is IFeature | IFunction =>
      "applicationFeatures" in obj;

    const childrenRaw = isFeature(item)
      ? item.applicationFeatures
      : item.children;

    // Đệ quy xây cây con
    const children = convertDataTreeItem(
      (childrenRaw || []) as (IMenuTree | IFeature | IFunction)[],
      item.id
    );

    const { id, name, status, order, parentId: _parentId, ...rest } = item;

    const treeItem: ITreeData = {
      id,
      name,
      status,
      order,
      parentId,
      children,
      ...rest,
    };

    result.push(treeItem);
  });

  return result;
};

export const transformMenuItemTreeToMenu = (
  menuItem: IMenuItemTree,
  parentId: number
): IMenu => {
  return {
    id: menuItem.id,
    name: menuItem.name,
    icon: menuItem.icon,
    href: menuItem.href,
    order: menuItem.order,
    parentId: parentId,
  };
};

export const transformMenuTreeToGroups = (
  menuItems: IMenuItemTree[]
): IMenuGroup[] => {
  if (!menuItems || menuItems.length === 0) {
    return [];
  }

  const groups: IMenuGroup[] = [];

  menuItems.forEach((parentMenu) => {
    if (parentMenu.children && parentMenu.children.length > 0) {
      const group: IMenuGroup = {
        id: parentMenu.id,
        name: parentMenu.name,
        order: parentMenu.order,
        items: parentMenu.children
          .map((child) => transformMenuItemTreeToMenu(child, parentMenu.id))
          .sort((a, b) => a.order - b.order),
      };
      groups.push(group);
    } else {
      const group: IMenuGroup = {
        id: parentMenu.id,
        name: parentMenu.name,
        order: parentMenu.order,
        items: [transformMenuItemTreeToMenu(parentMenu, parentMenu.id)],
      };
      groups.push(group);
    }
  });

  return groups.sort((a, b) => a.order - b.order);
};

export const breadthFirstSearch = (graph, targetId) => {
  const queue = [...graph];

  while (queue.length > 0) {
    const currNode = queue.shift();
    if (currNode.id === targetId) {
      return currNode;
    }
    if (currNode.children) {
      queue.push(...currNode.children);
    }

    if (currNode.applicationFeatures) {
      queue.push(...currNode.applicationFeatures);
    }
  }
  return [];
};

export const checkIndeterminate = (id, data, selectedNodes) => {
  const allChild = handelGetAllChild(id, data);
  if (isFullSibling(selectedNodes, allChild)) {
    return false;
  }
  if (selectedNodes.some((item) => allChild.includes(item))) {
    return true;
  }
  return false;
};

export const convertData = (dataCheckedBox, data) => {
  const checked: number[] = [];
  let expandCheckbox: string[] = [];

  for (let i = 0; i < dataCheckedBox.length; i++) {
    if (dataCheckedBox[i].applicationFeatureId) {
      checked.push(findIdInData(dataCheckedBox[i].applicationFeatureId, data));
      expandCheckbox = [
        ...expandCheckbox,
        ...findParentIdToExpand(dataCheckedBox[i].applicationFeatureId, data),
      ];
    }
    if (dataCheckedBox[i].menuConfigId) {
      checked.push(findIdInData(dataCheckedBox[i].menuConfigId, data));
      expandCheckbox = [
        ...expandCheckbox,
        ...findParentIdToExpand(dataCheckedBox[i].menuConfigId, data),
      ];
    }
  }

  return {
    checkedCheckBox: checked.filter((item) => item !== undefined),
    expandCheckbox,
  };
};

const findIdInData = (id, data) => {
  for (const item of data) {
    if (item.id === id) {
      return id;
    }
    if (item.children) {
      const found = findIdInData(id, item.children);
      if (found !== undefined) {
        return found;
      }
    }
    if (item.applicationFeatures) {
      const found = findIdInData(id, item.applicationFeatures);
      if (found !== undefined) {
        return found;
      }
    }
  }
  return undefined;
};

export const convertDataExpand = (
  nodeIds: (string | number)[],
  data: any[]
) => {
  const expandSet = new Set<string>();

  for (const nodeId of nodeIds) {
    const parentIds = findParentIdToExpand(nodeId, data);
    parentIds.forEach((parentId) => expandSet.add(parentId));
  }

  return Array.from(expandSet);
};

export const handleGetAllIds = (node, idList: any = []) => {
  idList.push(node.id);
  if (node.children) {
    node.children.forEach((child) => handleGetAllIds(child, idList));
  }

  if (node.applicationFeatures) {
    node.applicationFeatures.forEach((child) => handleGetAllIds(child, idList));
  }

  return idList;
};

export const checkSiblingsId = (arr1, arr2) => {
  for (let i = 0; i < arr2.length; i++) {
    if (arr1.includes(arr2[i])) {
      return true;
    }
  }
  return false;
};

export const getAllFathers = (id, data, list: any = []) => {
  const node = breadthFirstSearch(data, id);
  if (node.parentId !== 0) {
    list.push(node.parentId);
    return getAllFathers(node.parentId, data, list);
  }
  return list;
};

export const handelGetAllChild = (id, data) => {
  return handleGetAllIds(breadthFirstSearch(data, id));
};

export const isFullSibling = (selectedNodes, siblingsId) =>
  siblingsId.every((element) => selectedNodes.includes(element));

export const findParentNodeById = (arr, targetId) => {
  if (targetId === undefined) return null;

  for (const item of arr) {
    if (item.id === targetId) {
      return item;
    }

    const children = [
      ...(item.applicationFeatures || []),
      ...(item.children || []),
    ];
    const result = findParentNodeById(children, targetId);

    if (result) {
      return result;
    }
  }

  return null;
};

export const findParentIdToExpand = (id, data) => {
  const stack: any[] = [];
  stack.push(...data);
  const visited: string[] = [];
  while (stack.length > 0) {
    const node = stack.pop();
    if (node.id == id) {
      visited[node.id] = node.parentId;
      break;
    } else if (node.children && node.children.length > 0) {
      for (const item of node.children) {
        visited[item.id] = node.id;
        stack.push(item);
      }
    } else if (
      node.applicationFeatures &&
      node.applicationFeatures.length > 0
    ) {
      for (const item of node.applicationFeatures) {
        visited[item.id] = node.id;
        stack.push(item);
      }
    }
  }

  let lastVisited = id;
  const result: string[] = [];
  while (visited[lastVisited]) {
    result.push(visited[lastVisited].toString());
    lastVisited = visited[lastVisited];
  }
  return result;
};
