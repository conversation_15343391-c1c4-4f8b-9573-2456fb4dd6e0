import http from "@/api";
import { IDonviParams } from "@/app/(auth)/login/login.model";
import { ApiConstant, DataConstant } from "@/constant";
import {
  IDomainInfo,
  IDonVi,
  ISchoolLevel,
  ISchoolYear,
  IUserInfo,
} from "@/models/app.model";
import { IMenuItemTree } from "@/models/menu.model";
import {
  DataListResponseModel,
  DataResponseModel,
} from "@/models/response.model";
import { CommonUtils } from "@/utils";
import { extractErrorMessage } from "@/utils/common.utils";
import { toast } from "sonner";
import stringFormat from "string-format";

export const getMenuSideBar = (id: number) => {
  return http.get<DataResponseModel<IMenuItemTree[]>>(
    stringFormat(ApiConstant.GET_MENU_SIDE_BAR, { id })
  );
};

export const getDomainInfo = () => {
  return http.get<DataResponseModel<IDomainInfo>>(ApiConstant.GET_DOMAIN_INFO, {
    isBreakOrgId: true,
  });
};

export const getDonViListService = (params: IDonviParams) => {
  return http.get<DataListResponseModel<IDonVi>>(ApiConstant.GET_DON_VI, {
    params,
    isBreakOrgId: true,
  });
};

export const getSchoolLevelListService = () => {
  return http.get<DataListResponseModel<ISchoolLevel>>(
    ApiConstant.GET_SCHOOL_LEVEL_LIST
  );
};

export const getUserInfoService = () => {
  return http.get<DataResponseModel<IUserInfo>>(ApiConstant.GET_USER_INFO, {});
};

export const getSchoolYearListService = () => {
  return http.get<DataListResponseModel<ISchoolYear>>(
    ApiConstant.GET_SCHOOL_YEAR_LIST
  );
};

export const changePasswordService = (data: {
  password: string;
  newPassword: string;
  confirmPassword: string;
}) => {
  return http.post(ApiConstant.CHANGE_PASSWORD, data);
};

export const changeSchoolYearService = (data: {
  schoolYear: number;
  semester: number;
}) => {
  return http.post(ApiConstant.SCHOOL_YEAR_CHANGE, data);
};

/**
 * Update status
 * @param id -
 * @param status - STATUS_TYPE
 * @param url - url of api
 * @param onSuccess - func callback when success
 * @returns Formatted error description string
 */
export const updateStatusService = async ({
  id,
  status,
  url,
  onSuccess,
}: {
  id: string | number;
  status: DataConstant.STATUS_TYPE;
  url: string;
  onSuccess?: () => void;
}) => {
  try {
    CommonUtils.toggleAppProgress(true);

    const response: DataResponseModel<any> = await http.patch(
      stringFormat(url, { status, id }),
      { status, id }
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      toast.success("Thành công!", {
        description: "Dữ liệu đã cập nhật thành công.",
      });
      onSuccess?.();
    } else {
      throw new Error(response?.message || "Đã có lỗi xảy ra");
    }
  } catch (error: any) {
    const description = extractErrorMessage(error);
    toast.error("Thất bại!", {
      description,
    });
  } finally {
    CommonUtils.toggleAppProgress(false);
  }
};
