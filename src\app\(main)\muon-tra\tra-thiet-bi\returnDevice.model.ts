export interface IReturnDeviceList {
  id: number;
  createdBy: number;
  updatedBy: number;
  createdAt: string;
  updatedAt: string;
  borrowRequestId: number;
  teacherId: number;
  teacherName: string;
  deviceId: number;
  deviceCode: string;
  deviceName: string;
  subjectId: number;
  subjectName: string;
  roomId: number;
  roomName: string;
  fromDate: string;
  toDate: string;
  deviceUnitId: number;
  deviceUnitName: string;
  schoolDeviceTypeId: number;
  schoolDeviceTypeName: string;
  gradeCodes: number[];
  gradeCode: string;
  gradeName: string;
  quantity: number;
  totalBorrowReady: number;
  roomDeviceGuid: string;
  notes: string;
  status: number;
  statusName: string;
  totalLost: number;
  totalBroken: number;
  totalConsumed: number;
  isConsumable: boolean;
  isSelfMade: boolean;
  borrowReturnDate: string;
}

export interface IReturnDeviceHistory {
  id: number;
  createdBy: number;
  updatedBy: number;
  createdAt: string;
  updatedAt: string;
  borrowRequestId: number;
  teacherId: number;
  teacherName: string;
  deviceId: number;
  deviceCode: string;
  deviceName: string;
  subjectId: number;
  subjectName: string;
  roomId: number;
  roomName: string;
  fromDate: string;
  toDate: string;
  deviceUnitId: number;
  deviceUnitName: string;
  schoolDeviceTypeId: number;
  schoolDeviceTypeName: string;
  gradeCodes: number[];
  gradeCode: string;
  gradeName: string;
  quantity: number;
  totalBorrowReady: number;
  roomDeviceGuid: string;
  notes: string;
  status: number;
  statusName: string;
  totalLost: number;
  totalBroken: number;
  totalConsumed: number;
  isConsumable: boolean;
  isSelfMade: boolean;
  borrowReturnDate: string;
}

export interface IEditedDeviceData {
  totalBroken?: number;
  totalLost?: number;
  totalConsumed?: number;
  notes?: string;
  borrowReturnDate?: string;
}

export type EditedDataRecord = Record<number, IEditedDeviceData>;

export type ReturnDeviceFieldName =
  | "totalBroken"
  | "totalLost"
  | "totalConsumed"
  | "notes"
  | "borrowReturnDate";
