import React, { memo } from "react";
import { SvgIcon, SvgIconProps } from "@mui/material";

const LogoutIcon = ({ sx, ...otherProps }: SvgIconProps) => {
  return (
    <SvgIcon
      viewBox="0 0 18 19"
      width="18px"
      height="19px"
      sx={{ fontSize: "inherit", ...sx }}
      {...otherProps}
    >
      <path
        d="M8.99986 0C8.8341 0 8.67513 0.0658479 8.55792 0.183058C8.44071 0.300268 8.37486 0.45924 8.37486 0.625V10C8.37486 10.1658 8.44071 10.3247 8.55792 10.4419C8.67513 10.5592 8.8341 10.625 8.99986 10.625C9.16562 10.625 9.3246 10.5592 9.44181 10.4419C9.55902 10.3247 9.62486 10.1658 9.62486 10V0.625C9.62486 0.45924 9.55902 0.300268 9.44181 0.183058C9.3246 0.0658479 9.16562 0 8.99986 0ZM12.3461 1.91313C12.1931 1.84914 12.021 1.84856 11.8675 1.9115C11.7141 1.97444 11.592 2.09575 11.528 2.24875C11.464 2.40175 11.4634 2.57389 11.5264 2.72732C11.5893 2.88075 11.7106 3.00289 11.8636 3.06688L11.868 3.06813C14.5867 4.19438 16.4999 6.87375 16.4999 10C16.4999 14.1425 13.1424 17.5 8.99986 17.5C4.85736 17.5 1.49986 14.1425 1.49986 10C1.49986 6.87375 3.41236 4.19438 6.13174 3.06813C6.20903 3.03759 6.27946 2.99195 6.3389 2.93386C6.39834 2.87578 6.4456 2.80643 6.47791 2.72986C6.51023 2.6533 6.52695 2.57105 6.52709 2.48795C6.52724 2.40484 6.51081 2.32254 6.47877 2.24586C6.44672 2.16918 6.39971 2.09966 6.34047 2.04137C6.28124 1.98308 6.21097 1.93718 6.13379 1.90638C6.0566 1.87557 5.97405 1.86046 5.89096 1.86194C5.80787 1.86342 5.7259 1.88146 5.64986 1.915L5.65424 1.91375C2.48236 3.2275 0.250488 6.35375 0.250488 10.0006C0.250488 14.8331 4.16799 18.7506 9.00049 18.7506C13.833 18.7506 17.7505 14.8331 17.7505 10.0006C17.7505 6.35313 15.5192 3.2275 12.3467 1.91375L12.3461 1.91313Z"
        fill="currentColor"
      />
    </SvgIcon>
  );
};

export default memo(LogoutIcon);
