import {
  AppFormAutocomplete,
  AppForm<PERSON><PERSON>t<PERSON>ield,
  GridFormContainer,
} from "@/components/common";
import { AppConstant, DataConstant } from "@/constant";
import {
  educationUnitsActions,
  selectPhongList,
  selectSoList,
} from "@/redux/educationUnits.slice";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { CommonUtils } from "@/utils";
import { Grid } from "@mui/material";
import { useCallback } from "react";
import { Control, FieldErrors, UseFormSetValue } from "react-hook-form";

const AddNewTabContent = ({
  onSetValueForm,
  control,
  errors,
}: AddNewTabContentProps) => {
  const dispatch = useAppDispatch();
  const soList = useAppSelector(selectSoList);
  const phongList = useAppSelector(selectPhongList);

  const handleDoetChange = useCallback((data: any) => {
    onSetValueForm("divisionCode", null);
    if (data) {
      dispatch(
        educationUnitsActions.getPhongList({
          doetCode: data.code,
        })
      );
    } else {
      onSetValueForm("divisionCode", null);
    }
  }, []);

  return (
    <GridFormContainer>
      <Grid size={4}>
        <AppFormAutocomplete
          control={control}
          name="groupUnitCode"
          label="Cấp đơn vị"
          options={unitLevelList}
          rules={{
            required: "Cấp đơn vị không được để trống",
          }}
        />
      </Grid>

      <Grid size={4}>
        <AppFormAutocomplete
          control={control}
          label="Sở"
          name="doetCode"
          rules={{
            required: "Sở không được để trống",
          }}
          onChangeValueForm={handleDoetChange}
          options={soList}
        />
      </Grid>

      <Grid size={4}>
        <AppFormAutocomplete
          control={control}
          label="Phòng"
          name={"divisionCode"}
          options={phongList}
        />
      </Grid>

      <Grid size={4}>
        <AppFormTextField
          label="Tên đơn vị"
          control={control}
          name="name"
          rules={{
            required: "Tên đơn vị không được để trống",
          }}
        />
      </Grid>

      <Grid size={4}>
        <AppFormTextField
          label="Mã đơn vị"
          control={control}
          name="schoolCode"
          rules={{
            required: "Mã đơn vị không được để trống",
          }}
        />
      </Grid>

      <Grid size={4}>
        <AppFormAutocomplete
          control={control}
          name="schoolLevels"
          isMultiple
          label="Cấp học"
          options={DataConstant.SCHOOL_LEVEL_LIST}
          rules={{
            required: "Cấp học không được để trống",
          }}
        />
      </Grid>

      <Grid size={4}>
        <AppFormTextField
          label="Tên viết tắt"
          control={control}
          name="shortName"
          rules={{
            required: "Tên viết tắt không được để trống",
          }}
        />
      </Grid>

      <Grid size={4}>
        <AppFormTextField
          label="Hiệu trưởng"
          control={control}
          name="principal"
        />
      </Grid>

      <Grid size={4}>
        <AppFormTextField
          label="Điện thoại"
          control={control}
          name="phone"
          rules={{
            validate: (value) => {
              return (
                !value ||
                [AppConstant.PHONE_REGEX].every((pattern) =>
                  pattern.test(value)
                ) ||
                "Sai định dạng số điện thoại!"
              );
            },
            maxLength: {
              value: 20,
              message: "Số điện thoại không được vượt quá 20 ký tự",
            },
          }}
        />
      </Grid>

      <Grid size={4}>
        <AppFormTextField
          label="Email"
          control={control}
          name="email"
          rules={{
            validate: (value) => {
              return (
                !value ||
                [AppConstant.EMAIL_REGEX].every((pattern) =>
                  pattern.test(value)
                ) ||
                "Email không đúng định dạng"
              );
            },
            maxLength: {
              value: 250,
              message: "Email không vượt quá 250 ký tự!",
            },
          }}
        />
      </Grid>

      <Grid size={4}>
        <AppFormTextField label="Website" control={control} name="website" />
      </Grid>

      <Grid size={4}>
        <AppFormAutocomplete
          control={control}
          name="schoolType"
          label="Loại trường"
          options={SCHOOL_TYPE_LIST}
          rules={{
            required: "Loại trường không được để trống",
          }}
        />
      </Grid>

      <Grid size={12}>
        <AppFormTextField label="Địa chỉ" control={control} name="address" />
      </Grid>
    </GridFormContainer>
  );
};

export default AddNewTabContent;

type AddNewTabContentProps = {
  errors: FieldErrors<any>;
  control: Control<any>;
  onSetValueForm: UseFormSetValue<any>;
};

const unitLevelList = DataConstant.DON_VI_LIST.filter(
  (item) => item.id !== DataConstant.DON_VI_TYPE.bo
);

export enum SCHOOL_TYPE {
  CONG_LAP = "01",
  DAN_LAP = "03",
  TU_THUC = "09",
  NGOAI_CONG_LAP = "02",
}

export const SCHOOL_TYPE_LIST = [
  {
    id: SCHOOL_TYPE.CONG_LAP,
    label: "Trường công lập",
  },
  {
    id: SCHOOL_TYPE.DAN_LAP,
    label: "Trường dân lập",
  },
  {
    id: SCHOOL_TYPE.TU_THUC,
    label: "Trường tư thục",
  },
  {
    id: SCHOOL_TYPE.NGOAI_CONG_LAP,
    label: "Trường ngoại công lập",
  },
];
