import { AppModal } from "@/components/common";
import { AppModalProps } from "@/components/common/modal/AppModal";
import { useAppDispatch } from "@/redux/hook";
import { Box, Button } from "@mui/material";
import { useCallback } from "react";
import { FormProvider, useForm } from "react-hook-form";
import InformationForm from "./InformationForm";
import TabTable from "./TabTable";
import { deviceLiquidationActions } from "../deviceLiquidation.slice";

const CreateModal = ({
  fetchCurrentData,
  onClose,
  ...otherProps
}: CreateModalProps) => {
  const dispatch = useAppDispatch();
  const methods = useForm({
    defaultValues: INIT_VALUE,
  });
  const { control, handleSubmit, reset } = methods;

  const handleClose = useCallback(() => {
    dispatch(deviceLiquidationActions.resetModalDetails());
    dispatch(deviceLiquidationActions.resetSelectedTeacherModal());
    reset(INIT_VALUE);
    onClose();
  }, [onClose, reset]);

  const onSubmit = useCallback(
    (data) => {
      dispatch(
        deviceLiquidationActions.addDeviceLiquidation({
          data,
          onSuccess: () => {
            fetchCurrentData?.();
            handleClose();
          },
        })
      );
    },
    [fetchCurrentData]
  );

  return (
    <FormProvider {...methods}>
      <AppModal
        onClose={handleClose}
        modalTitleProps={{
          title: "Thêm phiếu ghi thanh lý thiết bị",
        }}
        fullScreen
        component="form"
        onSubmit={handleSubmit(onSubmit)}
        modalContentProps={{
          content: (
            <Box sx={{ display: "flex", flexDirection: "column", gap: 1.5 }}>
              <InformationForm control={control} />
              <TabTable />
            </Box>
          ),
          sx: {
            bgcolor: "background.grey",
            padding: "12px !important",
          },
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                onClick={handleClose}
                variant="outlined"
                color="secondary"
              >
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
        {...otherProps}
      />
    </FormProvider>
  );
};

export default CreateModal;

type CreateModalProps = AppModalProps & {
  fetchCurrentData?: () => void;
};

const INIT_VALUE = {
  documentNumber: "",
  documentDate: "",
  notes: "",
};
