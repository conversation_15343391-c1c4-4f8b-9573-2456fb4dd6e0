import { SvgIcon, SvgIconProps } from "@mui/material";
import { memo } from "react";

const PhoneIcon = ({ sx, ...otherProps }: SvgIconProps) => {
  return (
    <SvgIcon
      viewBox="0 0 32 32"
      sx={{ fontSize: "inherit", ...sx }}
      {...otherProps}
    >
      <path
        d="M16 29.6606C23.46 29.6606 29.6606 23.46 29.6606 16C29.6606 8.52686 23.4463 2.33943 15.9863 2.33943C8.51371 2.33943 2.34 8.52686 2.34 16C2.34 23.46 8.52685 29.6606 16 29.6606ZM12.5051 19.3349C9.18343 16.0263 7.06743 11.808 9.572 9.30343C9.71943 9.156 9.88 9.00915 10.0274 8.86172C10.7909 8.13829 11.5274 8.17886 12.1834 9.10286L13.9246 11.5806C14.5137 12.4377 14.3663 12.9194 13.7371 13.5891L13.188 14.192C12.9874 14.3794 13.068 14.6337 13.1611 14.7949C13.416 15.2766 14.1389 16.1337 14.9154 16.9109C15.7194 17.7143 16.536 18.3971 17.0451 18.6783C17.2326 18.7857 17.5006 18.8126 17.6749 18.652L18.2503 18.1029C18.8931 17.4731 19.416 17.3126 20.2463 17.9017C21.0851 18.4982 21.9289 19.0875 22.7777 19.6697C23.6211 20.2857 23.8217 21.0491 22.9646 21.8257C22.8177 21.9731 22.6834 22.1343 22.536 22.2811C20.032 24.7726 15.8263 22.656 12.5051 19.3349Z"
        fill="#459D7A"
      />
    </SvgIcon>
  );
};

export default memo(PhoneIcon);
