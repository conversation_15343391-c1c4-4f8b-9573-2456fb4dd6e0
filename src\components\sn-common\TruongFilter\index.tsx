"use client";

import { AppAutoComplete } from "@/components/common";
import { AppConstant } from "@/constant";
import {
  educationUnitsActions,
  selectTruongList,
  selectTruongValue,
} from "@/redux/educationUnits.slice";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { CommonUtils } from "@/utils";
import {
  AutocompleteCloseReason,
  AutocompleteInputChangeReason,
} from "@mui/material";
import { memo, useMemo } from "react";
import ListBoxInfiniteScroll from "./ListBoxInfiniteScroll";

const TruongFilter = ({
  onChangeTruong,
  groupUnitCode,
  divisionCode,
  doetCode,
}: {
  onChangeTruong: (_: any, data: any) => void;
  groupUnitCode?: string;
  divisionCode?: string;
  doetCode?: string;
}) => {
  const dispatch = useAppDispatch();
  const truongList = useAppSelector(selectTruongList);
  const truongValue = useAppSelector(selectTruongValue);

  const truongListOptions = useMemo(() => {
    const options = truongList;

    if (truongValue) {
      const newOptions = options.filter((item) => item.id !== truongValue.id);
      return [truongValue, ...newOptions];
    }

    return truongList;
  }, [truongList, truongValue]);

  const handleKeyDown = CommonUtils.debounce(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      const input = event.target as HTMLInputElement;
      if (event.key === "Escape") return;

      if (doetCode || divisionCode) {
        dispatch(
          educationUnitsActions.getTruongList({
            groupUnitCode,
            doetCode,
            divisionCode,
            searchKey: input.value,
            ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
          })
        );
      }
    },
    AppConstant.DEBOUNCE_TIME_IN_MILLISECOND
  );

  const handleClose = (
    event: React.SyntheticEvent,
    reason: AutocompleteCloseReason
  ) => {
    const input = event.target as HTMLInputElement;
    const notChoose = !truongListOptions.find(
      (item) => item.label === input.value
    );

    if ((reason === "blur" || reason === "escape") && notChoose) {
      if (doetCode || divisionCode) {
        dispatch(
          educationUnitsActions.getTruongList({
            groupUnitCode,
            doetCode,
            divisionCode,
            ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
          })
        );
      }
    }
  };

  const handleOnClickClear = (
    event: React.SyntheticEvent,
    value: string,
    reason: AutocompleteInputChangeReason
  ) => {
    if (reason === "clear") {
      if (doetCode || divisionCode) {
        dispatch(
          educationUnitsActions.getTruongList({
            groupUnitCode,
            doetCode,
            divisionCode,
            ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
          })
        );
      }
    }
  };

  const handleChangeValue = (_, value) => {
    onChangeTruong(_, value);
  };

  return (
    <AppAutoComplete
      options={truongListOptions}
      onClose={handleClose}
      onInputChange={handleOnClickClear}
      label="Trường"
      textFieldProps={{ onKeyDown: handleKeyDown }}
      value={truongValue}
      onChange={handleChangeValue}
      slotProps={{
        listbox: {
          component: (props) => (
            <ListBoxInfiniteScroll
              {...props}
              divisionCode={divisionCode}
              doetCode={doetCode}
            />
          ),
          sx: {
            overflow: "hidden !important",
            "& li": {
              whiteSpace: "normal !important",
            },
          },
        },
      }}
    />
  );
};

export default memo(TruongFilter);
