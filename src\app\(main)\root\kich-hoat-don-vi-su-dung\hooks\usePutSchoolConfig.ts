import http from "@/api";
import { ApiConstant } from "@/constant";
import { DataResponseModel } from "@/models/response.model";
import { CommonUtils } from "@/utils";
import { extractErrorMessage } from "@/utils/common.utils";
import { toast } from "sonner";
import stringFormat from "string-format";

export const putSchoolConfig = async ({
  id,
  body,
  onSuccess,
}: {
  id: number;
  body: any;
  onSuccess?: () => void;
}) => {
  try {
    CommonUtils.toggleAppProgress(true);

    const response: DataResponseModel<boolean> = await http.put(
      stringFormat(ApiConstant.PUT_SCHOOL_CONFIG, { id }),
      body
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      toast.success("Thành công!", {
        description: "Chỉnh sửa đơn vị thành công.",
      });
      onSuccess?.();
    } else {
      throw new Error(response?.message || "Đã có lỗi xảy ra");
    }
  } catch (error: any) {
    const description = extractErrorMessage(error);

    toast.error("Thất bại!", {
      description,
    });
  } finally {
    CommonUtils.toggleAppProgress(false);
  }
};
