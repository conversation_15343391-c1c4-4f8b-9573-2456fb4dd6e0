"use client";

import { IOption } from "@/components/common";
import { getPaginationInfo } from "@/components/common/table/AppTable/helper";
import { AppConstant } from "@/constant";
import {
  educationUnitsActions,
  selectPagination,
  selectTotalTruong,
  selectTruongList,
} from "@/redux/educationUnits.slice";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { Box, BoxProps } from "@mui/material";
import { forwardRef } from "react";
import dynamic from "next/dynamic";
const AppInfiniteScroll = dynamic(
  () => import("@/components/common/AppInfiniteScroll"),
  {
    ssr: false,
  }
);

const ListBoxInfiniteScroll = forwardRef(
  (
    {
      children,
      divisionCode,
      doetCode,
      groupUnitCode,
      schoolLevel,
      ...otherProps
    }: ListBoxInfiniteScrollProps,
    ref
  ) => {
    const dispatch = useAppDispatch();
    const totalTruong = useAppSelector(selectTotalTruong);
    const pagination = useAppSelector(selectPagination);
    const truongList = useAppSelector(selectTruongList);

    const handleGetMoreData = () => {
      const { currentPage, totalPage } = getPaginationInfo(
        pagination,
        totalTruong
      );
      if (currentPage >= totalPage) return;

      dispatch(
        educationUnitsActions.getTruongList({
          doetCode,
          divisionCode,
          groupUnitCode,
          ...pagination,
          skip: pagination.skip + AppConstant.PAGE_SIZE_OPTIONS[0],
          isScroll: true,
        })
      );
    };

    return (
      <Box ref={ref} height="100%" {...otherProps}>
        <AppInfiniteScroll
          height="100%"
          style={{ maxHeight: "300px" }}
          dataLength={truongList.length} //This is important field to render the next data
          next={handleGetMoreData}
          hasMore={true}
        >
          {children}
        </AppInfiniteScroll>
      </Box>
    );
  }
);

type ListBoxInfiniteScrollProps = BoxProps & {
  divisionCode?: IOption | null;
  doetCode?: IOption | null;
  groupUnitCode?: IOption | null;
  schoolLevel?: IOption | null;
};

export default ListBoxInfiniteScroll;
