import { AppTable } from "@/components/common";
import { CloseIcon } from "@/components/icons";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import {
  BORROW_TYPE,
  BorrowStatusEnum,
  IBorrowRequest,
  IBorrowRequestDevice,
  IBorrowRequestRoom,
} from "@/models/eduDevice.model";
import {
  borrowRequestActions,
  selectBorrowRequest,
  selectIsOpenDetail,
} from "@/redux/device/borrowRequest.slice";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { formatDayjsWithType, formatNumber } from "@/utils/format.utils";
import {
  Chip,
  Drawer,
  IconButton,
  Stack,
  Tab,
  Tabs,
  Typography,
} from "@mui/material";
import React, { memo, useState } from "react";
import DeviceRegister from "./DeviceRegister";
import DeviceRegisterRoom from "./DeviceRegisterRoom";

const DetailDrawer = () => {
  const dispatch = useAppDispatch();
  const isOpen = useAppSelector(selectIsOpenDetail);
  const dataSelected = useAppSelector(selectBorrowRequest);
  const [tabValue, setTabValue] = useState(1);

  const dateDisplay =
    dataSelected?.borrowType === BORROW_TYPE.week
      ? dataSelected?.schoolWeekConfigName
      : `${formatDayjsWithType(
          dataSelected?.borrowFromDate
        )} - ${formatDayjsWithType(dataSelected?.borrowToDate)}`;

  const handleClose = () => {
    dispatch(borrowRequestActions.toggleDetail(false));
    setTabValue(1);
    dispatch(borrowRequestActions.getBorrowRequestSuccess(null));
  };

  return (
    <Drawer
      open={isOpen}
      onClose={handleClose}
      anchor="right"
      hideBackdrop
      ModalProps={{
        keepMounted: true,
        sx: {
          pointerEvents: "none",
        },
      }}
      slotProps={{
        paper: {
          sx: {
            width: 650,
            pt: 2,
            px: 2,
            pointerEvents: "auto",
          },
        },
      }}
    >
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Typography variant="body2" px={2} fontWeight={700}>
          {dataSelected?.teacherName}
          {` `}
          {Boolean(dataSelected) && (
            <Typography
              component="span"
              variant="subtitle2"
              sx={{ color: "grey.600", fontWeight: 400 }}
            >
              ({dateDisplay})
            </Typography>
          )}
        </Typography>

        <IconButton onClick={handleClose}>
          <CloseIcon />
        </IconButton>
      </Stack>
      <Tabs
        sx={{
          mb: 0.5,
        }}
        value={tabValue}
        onChange={(_, value) => setTabValue(value)}
      >
        <Tab value={1} label="Danh sách thiết bị" />
        <Tab value={2} label="Danh sách kho/phòng" />
      </Tabs>
      {tabValue === 1 && (
        <DeviceRegister
          borrowType={dataSelected?.borrowType}
          data={dataSelected?.borrowRequestDevices}
        />
      )}
      {tabValue === 2 && (
        <DeviceRegisterRoom
          borrowType={dataSelected?.borrowType}
          data={dataSelected?.borrowRequestRooms}
        />
      )}
    </Drawer>
  );
};

export default memo(DetailDrawer);
