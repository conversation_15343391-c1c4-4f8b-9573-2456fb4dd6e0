import { IOption } from "@/components/common";
import { AppConstant } from "@/constant";
import { IDonVi, ISchoolLevel } from "@/models/app.model";
import { IDivision, IDoet } from "@/models/educationUnits.model";
import { IPaginationModel } from "@/models/response.model";
import { rootReducer } from "@/redux/reducer";
import {
  PayloadAction,
  WithSlice,
  createSelector,
  createSlice,
} from "@reduxjs/toolkit";
import { RootState } from "./store";
import { convertDonViToOptions } from "@/utils/common.utils";

/* ------------- Initial State ------------- */
export interface IInitialState {
  isFetching: boolean;
  error: object | string | null;

  donViList: IDonVi[];
  soList: IDoet[];
  phongList: IDivision[];
  truongList: IDonVi[];
  schoolLevelList: ISchoolLevel[];

  soValue: IOption | null;
  phongValue: IOption | null;
  truongValue: IOption | null;
  schoolLevelValue: IOption | null;

  pagination: IPaginationModel;
  totalTruong: number;
}

const initialState: IInitialState = {
  isFetching: false,
  error: null,

  donViList: [],
  soList: [],
  phongList: [],
  truongList: [],
  schoolLevelList: [],

  soValue: null,
  phongValue: null,
  truongValue: null,
  schoolLevelValue: null,

  pagination: AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
  totalTruong: 0,
};

/* ------------- Selector ------------- */
export const selectSoList = createSelector(
  [(state: RootState) => state.educationUnitsReducer?.soList],
  (soList) => convertDonViToOptions(soList)
);

export const selectPhongList = createSelector(
  [(state: RootState) => state.educationUnitsReducer?.phongList],
  (phongList) => convertDonViToOptions(phongList)
);

export const selectTruongList = createSelector(
  [(state: RootState) => state.educationUnitsReducer?.truongList],
  (truongList) => convertDonViToOptions(truongList) || []
);

export const selectPagination = createSelector(
  [(state: RootState) => state.educationUnitsReducer?.pagination],
  (pagination) => pagination as IPaginationModel
);

export const selectTotalTruong = createSelector(
  [(state: RootState) => state.educationUnitsReducer?.totalTruong],
  (totalTruong) => totalTruong || 0
);

export const selectSoValue = (state: RootState) =>
  state.educationUnitsReducer?.soValue ?? null;

export const selectPhongValue = (state: RootState) =>
  state.educationUnitsReducer?.phongValue ?? null;

export const selectTruongValue = (state: RootState) =>
  state.educationUnitsReducer?.truongValue ?? null;

export const getFetching = (state: RootState) =>
  Boolean(state.educationUnitsReducer?.isFetching);

export const selectSchoolLevelList = createSelector(
  [(state: RootState) => state.educationUnitsReducer?.schoolLevelList],
  (schoolLevelList) => convertDonViToOptions(schoolLevelList)
);

export const selectSchoolLevelValue = createSelector(
  [(state: RootState) => state.educationUnitsReducer?.schoolLevelValue],
  (schoolLevelValue) => schoolLevelValue
);

/* ------------- Reducers ------------- */
const reducers = {
  getSoList: (state: IInitialState) => {},
  getSoListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<IDoet>;
      totalCount: number;
    }>
  ) => {
    state.soList = action.payload.data || [];
  },
  getPhongList: (state: IInitialState, action: PayloadAction<any>) => {},
  getPhongListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<IDivision>;
      totalCount: number;
    }>
  ) => {
    state.isFetching = false;
    state.phongList = action.payload.data || [];
  },
  getTruongList: (
    state: IInitialState,
    action: PayloadAction<
      any & {
        isScroll?: boolean;
      }
    >
  ) => {
    if (typeof action.payload.skip === "number") {
      state.pagination.skip = action.payload.skip;
    }
  },
  getTruongListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<IDonVi>;
      totalCount: number;
      isScroll?: boolean;
    }>
  ) => {
    if (action.payload.isScroll) {
      state.truongList = [...state.truongList, ...(action.payload.data || [])];
    } else {
      state.truongList = action.payload.data || [];
    }
    state.totalTruong = action.payload.totalCount || 0;
  },
  resetSoPhongTruong: (state: IInitialState) => {
    state.pagination = AppConstant.DEFAULT_PAGINATION_SKIP_TAKE;

    state.soList = [];
    state.phongList = [];
    state.truongList = [];
    state.totalTruong = 0;
  },

  handleChangeValue: (
    state: IInitialState,
    action: PayloadAction<{
      field: keyof Pick<
        IInitialState,
        "soValue" | "phongValue" | "truongValue" | "schoolLevelValue"
      >;
      value: IOption | null;
    }>
  ) => {
    const { field, value } = action.payload;

    // Gán giá trị mới
    state[field] = value;

    // Reset các giá trị phụ thuộc
    if (field === "soValue") {
      state.phongValue = null;
      state.truongValue = null;
      state.phongList = [];
      state.truongList = [];
      state.totalTruong = 0;
      state.pagination = AppConstant.DEFAULT_PAGINATION_SKIP_TAKE;
    } else if (field === "phongValue") {
      state.truongValue = null;
      state.truongList = [];
      state.totalTruong = 0;
      state.pagination = AppConstant.DEFAULT_PAGINATION_SKIP_TAKE;
    }
  },

  clearSoPhongTruongValue: (state: IInitialState) => {
    state.soValue = null;
    state.phongValue = null;
    state.truongValue = null;
  },

  getSchoolLevelList: (state: IInitialState) => {},
  getSchoolLevelListSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: Array<ISchoolLevel>;
    }>
  ) => {
    state.schoolLevelList = action.payload.data || [];
  },

  educationUnitsFailure: (state: IInitialState, action: PayloadAction<any>) => {
    state.isFetching = false;
    state.error = action.payload ?? {};
  },
  educationUnitsReset: (state: IInitialState) => {
    state.isFetching = false;
    state.error = null;

    state.phongList = [];
    state.truongList = [];
    state.truongList = [];

    state.soValue = null;
    state.phongValue = null;
    state.truongValue = null;

    state.totalTruong = 0;
    state.pagination = AppConstant.DEFAULT_PAGINATION_SKIP_TAKE;
  },
};

export const educationUnitsSlice = createSlice({
  name: "educationUnitsReducer",
  initialState,
  reducers,
});

export const educationUnitsActions = educationUnitsSlice.actions;

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices
    extends WithSlice<typeof educationUnitsSlice> {}
}

// Inject reducer
const injectededucationUnitsSlice = educationUnitsSlice.injectInto(rootReducer);

export const educationUnitsSelectors = injectededucationUnitsSlice.selectors;
