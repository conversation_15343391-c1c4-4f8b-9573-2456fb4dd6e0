import {
  deviceActions,
  selectorCancelIds,
  selectorDeviceTransfer,
  selectorDeviceTransferParams,
  selectorDeviceTransferPagination,
  selectorTotalDeviceTransfer,
} from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.slice";
import { IDeviceTransfer } from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/type";
import { AppCheckbox, AppTable } from "@/components/common";
import useConfigHeightOfTableInModal from "@/hooks/useConfigHeightOfTableInModal";
import { IPaginationModel } from "@/models/response.model";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { formatNumber } from "@/utils/format.utils";
import { Row } from "@tanstack/react-table";
import { ColumnDef } from "@tanstack/react-table";
import React, { memo, useCallback, useEffect, useMemo } from "react";

const CancelTransferTable = () => {
  const dispatch = useAppDispatch();
  const data = useAppSelector(selectorDeviceTransfer);
  const totalData = useAppSelector(selectorTotalDeviceTransfer);
  const deviceTransferParams = useAppSelector(selectorDeviceTransferParams);
  const deviceTransferPagination = useAppSelector(
    selectorDeviceTransferPagination
  );

  const handleChangePagination = useCallback(
    (pagination: IPaginationModel) => {
      dispatch(deviceActions.setDeviceTransferPagination(pagination));
    },
    [dispatch]
  );

  useEffect(() => {
    dispatch(
      deviceActions.getDeviceTransfer({
        ...deviceTransferPagination,
        ...deviceTransferParams,
      })
    );
  }, [deviceTransferPagination, deviceTransferParams]);

  useConfigHeightOfTableInModal();

  return (
    <AppTable
      columns={columns}
      data={data}
      totalData={totalData}
      onPageChange={handleChangePagination}
      paginationData={deviceTransferPagination}
      className="table-modal"
    />
  );
};

export default CancelTransferTable;

const columns: ColumnDef<IDeviceTransfer>[] = [
  {
    id: "index",
    header: "STT",
    cell: ({ row }) => <IdxCell row={row} />,
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "select",
    header: ({ table }) => <HeaderCheckbox table={table} />,
    cell: ({ row }) => <RowCheckbox row={row} />,
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "deviceCode",
    header: "Mã thiết bị",
    accessorKey: "deviceCode",
    size: 120,
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
    size: 150,
  },
  {
    id: "fromRoomName",
    header: "Điều chuyển từ",
    accessorKey: "fromRoomName",
    size: 150,
  },
  {
    id: "toRoomName",
    header: "Điều chuyển đến",
    accessorKey: "toRoomName",
    size: 150,
  },
  {
    id: "totalBroken",
    header: "Hỏng",
    accessorFn: (row) => formatNumber(row.totalBroken),
    size: 150,
    meta: {
      align: "right",
    },
  },
  {
    id: "totalLost",
    header: "Mất",
    accessorFn: (row) => formatNumber(row.totalLost),
    size: 150,
    meta: {
      align: "right",
    },
  },
  {
    id: "totalAvailable",
    header: "Còn SD",
    accessorFn: (row) => formatNumber(row.totalAvailable),
    size: 150,
    meta: {
      align: "right",
    },
  },
];

const IdxCell = memo(({ row }: { row: Row<IDeviceTransfer> }) => {
  const pagination = useAppSelector(selectorDeviceTransferPagination);

  return row.index + 1 + (pagination?.skip ?? 0);
});

const HeaderCheckbox = ({ table }) => {
  const dispatch = useAppDispatch();
  const cancelIds = useAppSelector(selectorCancelIds);
  const deviceTransfer = useAppSelector(selectorDeviceTransfer);

  const isCheckedAll = useMemo(() => {
    return (
      deviceTransfer.every((item) => cancelIds.includes(item.id)) &&
      deviceTransfer.length !== 0
    );
  }, [deviceTransfer, cancelIds]);

  const handleCheckedAll = (_, checked: boolean) => {
    table.toggleAllPageRowsSelected(!!checked);
    dispatch(deviceActions.selectAllRow(checked));
  };

  return <AppCheckbox checked={isCheckedAll} onChange={handleCheckedAll} />;
};

const RowCheckbox = ({ row }) => {
  const dispatch = useAppDispatch();
  const cancelIds = useAppSelector(selectorCancelIds);

  const handleCheckedRow = (id) => {
    dispatch(deviceActions.selectRow(id));
  };

  const isChecked = useMemo(() => {
    return cancelIds.includes(row.original.id);
  }, [cancelIds, row.original.id]);

  return (
    <AppCheckbox
      onChange={() => handleCheckedRow(row.original.id)}
      checked={isChecked}
    />
  );
};
