"use client";

import React, { memo, JSX, useMemo } from "react";
import { Grid } from "@mui/material";
import {
  Control,
  FieldValues,
  Path,
  UseFormSetValue,
  useFormState,
  useWatch,
} from "react-hook-form";
import GridFormContainer from "@/components/common/GridFormContainer";
import AppFormTextField from "@/components/common/form/AppFormTextField";
import {
  ConditionalDisplayConfig,
  FilterValueType,
  FormFieldConfig,
} from "@/components/common/TablePageLayout/type";
import useGetApiFormFieldOptions from "../hooks/useGetApiFormFieldOptions";
import AutoListForm from "./form-field/AutoListForm";
import AppFormToggle from "@/components/common/form/AppFormToggle";
import AppFormDatePicker from "@/components/common/form/AppFormDatePicker";
import AppFormTextEditor from "@/components/common/form/AppFormTextEditor";
import AppFormRadio from "@/components/common/form/AppFormRadio";
import { DataConstant } from "@/constant";
import AppFormSelectTree from "@/components/common/form/AppFormSelectTree";
import { ITreeData } from "@/models/types";
import FieldOptionFetcher from "./FieldOptionFetcher";

type FromActionsModalProps<T extends FieldValues> = {
  control: Control<T>;
  formField?: FormFieldConfig<T>[];
  onSetValue: UseFormSetValue<Record<string, any>>;
};

const FromActionsModal = <T extends FieldValues>({
  control,
  formField,
  onSetValue,
}: FromActionsModalProps<T>) => {
  const { errors } = useFormState({ control });

  const watchedKeys = useMemo(() => {
    return (
      formField?.flatMap(
        (field) => field.displayIf?.map((d) => d.key as Path<T>) ?? []
      ) ?? []
    );
  }, [formField]);

  const watchedValues = useWatch({
    control,
    name: watchedKeys,
  });

  const {
    optionsObj,
    setOptionsObj,
    itemsWithApi,
    allValues,
    watchedFilterVals,
  } = useGetApiFormFieldOptions(formField, control);

  const renderField = (item: FormFieldConfig<T>) => {
    switch (item.type) {
      case "text":
        return (
          <AppFormTextField<any>
            control={control}
            textfieldProps={{
              autoFocus: item.autoFocus,
              error: Boolean(errors?.[item.key]),
              helperText: errors?.[item.key]?.message as React.ReactNode,
              placeholder: item.placeholder,
              disabled: item.disabled,
              ...item?.textFieldProps,
            }}
            onChangeValueForm={(event) =>
              item.onChangeValue?.(onSetValue, event?.target.value)
            }
            rules={item.rules}
            label={item.label}
            name={item.key}
            {...item.fieldProps}
          />
        );
      case "select":
        return (
          <AutoListForm<any>
            options={optionsObj[item.key] ?? []}
            formField={item}
            textFieldProps={{
              autoFocus: item.autoFocus,
              error: Boolean(errors?.[item.key]),
              helperText: errors?.[item.key]?.message as React.ReactNode,
              placeholder: item.placeholder,
              disabled: item.disabled,
              ...item?.textFieldProps,
            }}
            onChangeValueForm={(value?: FilterValueType) =>
              item.onChangeValue?.(onSetValue, value)
            }
            {...item.fieldProps}
          />
        );
      case "number":
        return (
          <AppFormTextField<any>
            control={control}
            textfieldProps={{
              autoFocus: item.autoFocus,
              error: Boolean(errors?.[item.key]),
              helperText: errors?.[item.key]?.message as React.ReactNode,
              placeholder: item.placeholder,
              type: "number",
              isDecimal: item?.textFieldNumberProps?.isDecimal,
              slotProps: {
                htmlInput: {
                  min: item?.textFieldNumberProps?.min,
                  max: item?.textFieldNumberProps?.max,
                  step: item?.textFieldNumberProps?.step,
                },
              },
              disabled: item.disabled,
              ...item?.textFieldProps,
            }}
            rules={item.rules}
            label={item.label}
            name={item.key}
            onChangeValueForm={(event) =>
              item.onChangeValue?.(onSetValue, event?.target.value)
            }
            {...item.fieldProps}
          />
        );
      case "toggle":
        return (
          <AppFormToggle
            label={item.label}
            control={control}
            name={item.key}
            toggleProps={{
              disabled: item.disabled,
            }}
            onChangeValueForm={(_, checked: DataConstant.BOOLEAN_TYPE) =>
              item.onChangeValue?.(onSetValue, checked)
            }
            {...item.fieldProps}
          />
        );
      case "date":
        return (
          <AppFormDatePicker
            label={item.label}
            control={control}
            name={item.key}
            datePickerProps={{
              disabled: item.disabled,
              slotProps: {
                textField: {
                  error: Boolean(errors?.[item.key]),
                  helperText: errors?.[item.key]?.message as React.ReactNode,
                },
              },
              ...item.datePickerProps,
            }}
            rules={item.rules}
            onChangeValueForm={(val) => {
              item.onChangeValue?.(onSetValue, val);
            }}
            {...item.fieldProps}
          />
        );
      case "editor":
        return (
          <AppFormTextEditor
            label={item.label}
            control={control}
            name={item.key}
            rules={item.rules}
            onChangeValueForm={(value) => {
              item.onChangeValue?.(onSetValue, value);
            }}
            {...item.fieldProps}
          />
        );
      case "radio":
        return (
          <AppFormRadio
            label={item.label}
            control={control}
            name={item.key}
            rules={item.rules}
            radioList={optionsObj?.[item.key] ?? []}
            onChangeValueForm={(value) => {
              item.onChangeValue?.(onSetValue, value);
            }}
            {...item.fieldProps}
          />
        );
      case "tree":
        return (
          <AppFormSelectTree
            label={item.label}
            control={control}
            name={item.key}
            rules={item.rules}
            nodeChildren="children"
            data={optionsObj?.[item.key] as unknown as ITreeData[]}
            onChangeValueForm={(value) => {
              item.onChangeValue?.(onSetValue, value);
            }}
            {...item.fieldProps}
          />
        );
      case "custom":
        return item.component?.(control);
      default:
        return null;
    }
  };

  return (
    <>
      {itemsWithApi.map((field) => (
        <FieldOptionFetcher
          key={field.key}
          item={field}
          allValues={allValues}
          watchedFilterVals={watchedFilterVals}
          setOptionsObj={setOptionsObj}
        />
      ))}

      <GridFormContainer>
        {formField?.map((item) => {
          if (!shouldDisplay(item.displayIf, watchedKeys, watchedValues))
            return null;
          return (
            <Grid size={item.size || 12} key={item.key} offset={item.offset}>
              {renderField(item)}
            </Grid>
          );
        })}
      </GridFormContainer>
    </>
  );
};

export default memo(FromActionsModal) as <T extends FieldValues>(
  props: FromActionsModalProps<T>
) => JSX.Element;

const shouldDisplay = <T extends FieldValues>(
  displayIf: ConditionalDisplayConfig<T>[] | undefined,
  watchedKeys: Path<T>[],
  watchedValues: unknown[]
): boolean => {
  if (!displayIf || displayIf.length === 0) return true;

  return displayIf.some((condition) => {
    const index = watchedKeys.findIndex((key) => key === condition.key);
    const current = watchedValues?.[index];

    return condition.notEqual
      ? current !== condition.value
      : current === condition.value;
  });
};
