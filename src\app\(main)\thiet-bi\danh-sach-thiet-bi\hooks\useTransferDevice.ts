import http from "@/api";
import { selectorTransferInput } from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.slice";
import { ApiConstant, EnvConstant } from "@/constant";
import { DataResponseModel } from "@/models/response.model";
import { useAppSelector } from "@/redux/hook";
import { CommonUtils } from "@/utils";
import { toast } from "sonner";

const useTransferDevice = () => {
  const handleTransferDevice = async (
    deviceTransfer,
    onSuccess?: () => void
  ) => {
    try {
      if (deviceTransfer.length === 0) {
        toast.warning("Chưa có thiết bị điều chuyển!", {
          description: "Vui lòng chọn thiết bị điều chuyển",
        });
        return;
      }

      const isErrorValue = deviceTransfer.some(
        (item) =>
          Number(item.totalAvailable) === 0 &&
          Number(item.totalBroken) === 0 &&
          Number(item.totalLost) === 0
      );
      if (isErrorValue) {
        toast.warning("Thiết bị điều chuyển chưa có số lượng!", {
          description: "Vui lòng kiểm tra lại số lượng điều chuyển.",
        });
        return;
      }

      const isErrorQuantity = deviceTransfer.some((item) => item.isError);
      if (isErrorQuantity) {
        toast.warning("Số lượng điều chuyển không hợp lệ!", {
          description: "Vui lòng kiểm tra lại số lượng điều chuyển.",
        });
        return;
      }

      const isErrorRoom = deviceTransfer.some((item) => !item.toRoomId);
      if (isErrorRoom) {
        toast.warning("Phòng điều chuyển không hợp lệ!", {
          description: "Vui lòng kiểm tra lại phòng điều chuyển.",
        });
        return;
      }

      const response: DataResponseModel<any> = await http.post(
        ApiConstant.TRANSFER_DEVICE,
        deviceTransfer
      );
      if (response.code === ApiConstant.ERROR_CODE_OK) {
        toast.success("Thành công", {
          description: "Điều chuyển thành công",
        });
        onSuccess?.();
      } else {
        throw response;
      }
    } catch (error) {
      EnvConstant.IS_DEV && console.log(error);
      toast.error("Thất bại!", {
        description: CommonUtils.extractErrorMessage(error),
      });
    }
  };

  const handleCancelTransferDevice = async (ids, onSuccess?: () => void) => {
    try {
      const response: DataResponseModel<any> = await http.delete(
        ApiConstant.DELETE_TRANSFER_DEVICE,
        ids
      );
      if (response.code === ApiConstant.ERROR_CODE_OK) {
        toast.success("Thành công", {
          description: "Hủy điều chuyển thành công",
        });
        onSuccess?.();
      } else {
        throw response;
      }
    } catch (error) {
      EnvConstant.IS_DEV && console.log(error);
      toast.error("Thất bại!", {
        description: CommonUtils.extractErrorMessage(error),
      });
    }
  };

  return {
    handleTransferDevice,
    handleCancelTransferDevice,
  };
};

export default useTransferDevice;
