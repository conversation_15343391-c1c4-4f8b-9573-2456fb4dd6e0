"use client";
import { useAppSelector } from "@/redux/hook";
import { Stack, Typography } from "@mui/material";
import { usePathname, useRouter } from "next/navigation";
import React, { memo, useMemo, useState, useCallback } from "react";
import dynamic from "next/dynamic";
import { ArrowIcon } from "@/components/icons";
import { PathConstant } from "@/constant";

const MenuDropdown = dynamic(() => import("./MenuDropdown"), { ssr: false });

const TitlePage = () => {
  const pathname = usePathname();
  const menuSidebar = useAppSelector((state) => state.appReducer.menuSidebar);
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  const title = useMemo(
    () => getPageNameByHref(menuSidebar, pathname),
    [pathname, menuSidebar]
  );
  const haveMenu = pathname.startsWith(PathConstant.SYSTEM + "/");

  const handlePopoverOpen = useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      setAnchorEl(event.currentTarget);
    },
    []
  );

  const handlePopoverClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  return haveMenu ? (
    <>
      <Stack
        direction="row"
        alignItems="center"
        spacing={1}
        sx={{
          cursor: "pointer",
          borderRadius: 1,
          px: 1,
          py: 0.5,
          ml: 1,
        }}
        onClick={handlePopoverOpen}
      >
        <Typography variant="h4">{title}</Typography>
        <ArrowIcon
          sx={{
            fontSize: 24,
            transition: "transform 0.2s ease-in-out",
            transform: Boolean(anchorEl) ? "rotate(90deg)" : "rotate(-90deg)",
          }}
        />
      </Stack>
      <MenuDropdown
        onClose={handlePopoverClose}
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        onMenuItemClick={handlePopoverClose}
      />
    </>
  ) : (
    <Typography variant="h4" ml={1}>
      {title}
    </Typography>
  );
};

export default memo(TitlePage);

const getPageNameByHref = (
  menuList: any[],
  targetHref: string
): string | undefined => {
  for (const item of menuList) {
    if (item.href === targetHref) return item.name;

    if (item.children && Array.isArray(item.children)) {
      const childResult = getPageNameByHref(item.children, targetHref);
      if (childResult) return childResult;
    }
  }

  return undefined;
};
