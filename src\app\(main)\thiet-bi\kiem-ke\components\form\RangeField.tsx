import { inventoryTransactionActions } from "@/app/(main)/thiet-bi/kiem-ke/inventoryTransaction.slice";
import { ManageTypeEnum } from "@/app/(main)/thiet-bi/kiem-ke/type";
import { AppFormAutocomplete } from "@/components/common";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { selectRoomList, selectSubjectList } from "@/redux/system.slice";
import { useCallback, useMemo } from "react";
import { useFormContext, useWatch } from "react-hook-form";

const RangeField = () => {
  const dispatch = useAppDispatch();
  const rooms = useAppSelector(selectRoomList);
  const subjects = useAppSelector(selectSubjectList);
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const isManageType = useWatch({
    control,
    name: "isManageType",
  });

  const scopeIds = useWatch({
    control,
    name: "scopeIds",
  });

  const options = useMemo(() => {
    return Number(isManageType) === ManageTypeEnum.Room
      ? rooms.map((item) => ({
          id: item.id,
          label: item.name,
        }))
      : subjects.map((item) => ({
          id: item.id,
          label: item.name,
        }));
  }, [isManageType, rooms, subjects]);

  const handleBlurChangeFilter = useCallback(() => {
    dispatch(
      inventoryTransactionActions.changeFilterChooseDevices({
        key:
          isManageType === ManageTypeEnum.Room ? "roomIds" : "schoolSubjectIds",
        value: scopeIds.map((item) => item.id),
      })
    );
  }, [isManageType, scopeIds]);

  return (
    <AppFormAutocomplete
      options={options}
      direction="row"
      labelProps={{
        sx: {
          overflow: "initial",
          minWidth: 110,
        },
      }}
      autocompleteProps={{
        limitTags: 3,
        onBlur: handleBlurChangeFilter,
        hasAllOption: true,
        disableCloseOnSelect: true,
        textFieldProps: {
          error: Boolean(errors.scopeIds),
          helperText: errors.scopeIds?.message as string,
        },
      }}
      isMultiple
      name="scopeIds"
      label="Phạm vi"
      control={control}
      rules={{
        required: "Phạm vi không được bỏ trống",
      }}
    />
  );
};

export default RangeField;
