"use client";

import { TablePageLayout } from "@/components/common";
import {
  FormFieldConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import { AppConstant, DataConstant } from "@/constant";
import { TEACHER, TEACHER_GROUP } from "@/constant/api.const";
import { ITeacher } from "@/models/system.model";
import { formatDayjsWithType, getGenderLabel } from "@/utils/format.utils";
import { ColumnDef } from "@tanstack/react-table";
import React, { useRef } from "react";

const TeacherPage = () => {
  const tableRef = useRef<ITableRef>(null);

  return (
    <TablePageLayout<ITeacher>
      ref={tableRef}
      visibleCol={VISIBLE_COL}
      apiUrl={TEACHER}
      tableProps={{
        columns: COLUMN,
      }}
      filterConfig={[
        {
          key: "searchKey",
          type: "text",
          label: "<PERSON><PERSON><PERSON> kiếm",
          size: 2.4,
        },
      ]}
      actions={["create", "update", "delete"]}
      formConfig={{
        modalProps: {
          maxWidth: "md",
          fullWidth: true,
        },
        deleteUrl: TEACHER,
        detailUrl: TEACHER,
        createUrl: TEACHER,
        updateUrl: TEACHER,
        createFields: CREATE_CONFIG,
        updateFields: UPDATE_CONFIG,
      }}
    />
  );
};

export default TeacherPage;

const VISIBLE_COL = [];
const COLUMN: ColumnDef<ITeacher>[] = [
  {
    id: "fullName",
    accessorKey: "fullName",
    header: "Tên giáo viên",
  },
  {
    id: "dateOfBirth",
    accessorKey: "dateOfBirth",
    header: "Ngày sinh",
    accessorFn: (row) => formatDayjsWithType(row.dateOfBirth),
    size: 70,
  },
  {
    id: "gender",
    accessorKey: "gender",
    header: "Giới tính",
    accessorFn: (row) => getGenderLabel(row.gender),
    size: 20,
  },
  {
    id: "teacherGroupSubjectName",
    accessorKey: "teacherGroupSubjectName",
    header: "Tổ bộ môn",
  },
];

const CREATE_CONFIG: FormFieldConfig<ITeacher>[] = [
  {
    key: "lastName",
    label: "Họ đệm",
    type: "text",
    size: 6,
    rules: {
      required: "Họ đệm không được để trống",
    },
  },
  {
    key: "firstName",
    label: "Tên",
    type: "text",
    size: 6,
    rules: {
      required: "Tên không được để trống",
    },
  },
  {
    key: "code",
    label: "Mã",
    type: "text",
    size: 6,
    rules: {
      required: "Mã không được để trống",
    },
  },
  {
    key: "identityNumber",
    label: "Số CCCD",
    type: "text",
    size: 6,
    rules: {
      required: "Số CCCD không được để trống",
    },
  },
  {
    key: "password",
    label: "Mật khẩu",
    type: "text",
    size: 6,
    rules: {
      required: "Mật khẩu không được để trống",
    },
  },
  {
    key: "applicationFunctionCode",
    label: "Nhóm quyền",
    type: "select",
    size: 6,
    selectConfig: {
      valueKey: "code",
    },
    apiListUrl: "/v1/application-function",
  },
  {
    key: "teacherGroupSubjectId",
    label: "Tổ bộ môn",
    type: "select",
    size: 6,
    apiListUrl: TEACHER_GROUP,
  },
  {
    key: "dateOfBirth",
    label: "Ngày sinh",
    type: "date",
    size: 6,
  },
  {
    key: "gender",
    label: "Giới tính",
    type: "select",
    size: 6,
    selectConfig: {
      options: DataConstant.GENDER_TYPE_LIST,
    },
  },
  {
    key: "phone",
    label: "Số điện thoại",
    type: "text",
    size: 6,
    rules: {
      validate: (value) => {
        return (
          !value ||
          [AppConstant.PHONE_REGEX].every((pattern) => pattern.test(value)) ||
          "Sai định dạng số điện thoại!"
        );
      },
    },
  },
  {
    key: "email",
    label: "Email",
    type: "text",
    size: 6,
    rules: {
      validate: (value) => {
        return (
          !value ||
          [AppConstant.EMAIL_REGEX].every((pattern) => pattern.test(value)) ||
          "Sai định dạng email!"
        );
      },
    },
  },
  {
    key: "address",
    label: "Địa chỉ",
    type: "text",
    size: 12,
  },
];

const UPDATE_CONFIG: FormFieldConfig<ITeacher>[] = [
  {
    key: "lastName",
    label: "Họ đệm",
    type: "text",
    size: 6,
    rules: {
      required: "Họ đệm không được để trống",
    },
  },
  {
    key: "firstName",
    label: "Tên",
    type: "text",
    size: 6,
    rules: {
      required: "Tên không được để trống",
    },
  },
  {
    key: "code",
    label: "Mã",
    type: "text",
    size: 6,
    rules: {
      required: "Mã không được để trống",
    },
  },
  {
    key: "identityNumber",
    label: "Số CCCD",
    type: "text",
    size: 6,
    rules: {
      required: "Số CCCD không được để trống",
    },
  },
  {
    key: "applicationFunctionCode",
    label: "Nhóm quyền",
    type: "select",
    size: 6,
    selectConfig: {
      valueKey: "code",
    },
    apiListUrl: "/v1/application-function",
  },
  {
    key: "teacherGroupSubjectId",
    label: "Tổ bộ môn",
    type: "select",
    size: 6,
    apiListUrl: TEACHER_GROUP,
  },
  {
    key: "dateOfBirth",
    label: "Ngày sinh",
    type: "date",
    size: 6,
  },
  {
    key: "gender",
    label: "Giới tính",
    type: "select",
    size: 6,
    selectConfig: {
      options: DataConstant.GENDER_TYPE_LIST,
    },
  },
  {
    key: "phone",
    label: "Số điện thoại",
    type: "text",
    size: 6,
    rules: {
      validate: (value) => {
        return (
          !value ||
          [AppConstant.PHONE_REGEX].every((pattern) => pattern.test(value)) ||
          "Sai định dạng số điện thoại!"
        );
      },
    },
  },
  {
    key: "email",
    label: "Email",
    type: "text",
    size: 6,
    rules: {
      validate: (value) => {
        return (
          !value ||
          [AppConstant.EMAIL_REGEX].every((pattern) => pattern.test(value)) ||
          "Sai định dạng email!"
        );
      },
    },
  },
  {
    key: "address",
    label: "Địa chỉ",
    type: "text",
    size: 12,
  },
];
