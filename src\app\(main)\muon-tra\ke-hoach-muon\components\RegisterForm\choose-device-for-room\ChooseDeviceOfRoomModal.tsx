import { AppModal } from "@/components/common";
import { Button } from "@mui/material";
import React, { memo, useEffect } from "react";
import FormDeviceSelected from "./FormDeviceSelected";
import { FormProvider, useForm, useFormContext } from "react-hook-form";
import { BorrowStatusEnum, IEduDevice } from "@/models/eduDevice.model";
import { IBorrowRequestAction } from "../../../borrowRequestModel";
import { v4 as uuid } from "uuid";
import {
  getOptionId,
  IOption,
  toOption,
} from "@/components/common/AppAutoComplete";
import { toast } from "sonner";

const ChooseDeviceOfRoomModal = ({ isOpen, onClose, idSelected }) => {
  const { setValue: setParentValue, getValues: getParrentValue } =
    useFormContext<IBorrowRequestAction>();

  const methods = useForm({
    defaultValues: INIT_VALUE,
  });

  const { reset, handleSubmit, setValue } = methods;

  const handleClose = () => {
    onClose();
    reset(INIT_VALUE);
  };

  const handleSubmitData = (data) => {
    const parrentData = getParrentValue();

    const borrowType = getParrentValue("borrowType");

    const errorItem = data.deviceSelected.find(
      (item) => Number(item.totalBorrow) > Number(item.totalBorrowReady)
    );

    if (errorItem) {
      const max = errorItem.totalBorrowReady;
      const name = errorItem.deviceName;
      const code = errorItem.code;
      const description = `Số lượng "${name} (${code})" còn lại không đủ. Thầy cô có thể mượn tối đa (${max}) thiết bị.`;

      toast.warning("Cảnh báo", { description });
      return;
    }

    const newDevices = data.deviceSelected.map((item) => ({
      id: uuid(),
      deviceId: item.id as number,
      deviceName: item.deviceName,
      deviceCode: item.code,
      subjectId: toOption(
        item.schoolSubjectId,
        item.schoolSubjectName
      ) as IOption,
      subjectName: item.schoolSubjectName,
      roomId: toOption(item.roomId, item.roomName) as IOption,
      roomName: item.roomName,
      fromDate: parrentData.borrowFromDate as Date,
      toDate: parrentData.borrowToDate as Date,
      quantity: Number(item.totalBorrow),
      deviceUnitName: item.deviceUnitName,
      deviceUnitId: item.deviceUnitId,
      gradeCode: item.gradeCode,
      roomDeviceGuid: idSelected,
      borrowType,
      status: BorrowStatusEnum.Register,
    }));

    setParentValue("borrowRequestDevices", [...newDevices]);
    handleClose();
  };

  useEffect(() => {
    if (isOpen && idSelected) {
      const borrowRequestDevices = getParrentValue("borrowRequestDevices");

      const dataSelected = borrowRequestDevices
        .filter((item) => item.roomDeviceGuid === idSelected)
        .map((item) => ({
          id: item.deviceId,
          deviceId: item.deviceId,
          code: item.deviceCode,
          deviceName: item.deviceName,
          schoolSubjectId: getOptionId(item.subjectId),
          schoolSubjectName: item.subjectName,
          roomId: getOptionId(item.roomId),
          roomName: item.roomName,
          deviceUnitName: item.deviceUnitName,
          deviceUnitId: item.deviceUnitId,
          gradeName: item.gradeName,
          totalBorrow: item.quantity ?? 1,
          idOriginal: item.deviceId,
        }));
      setValue("deviceSelected", dataSelected as any);
    }
  }, [isOpen]);

  return (
    <FormProvider {...methods}>
      <AppModal
        onSubmit={(e) => {
          e.stopPropagation();
          handleSubmit(handleSubmitData)(e);
        }}
        component="form"
        isOpen={isOpen}
        fullWidth
        fullScreen
        onClose={handleClose}
        modalTitleProps={{
          title: "Thêm thiết bị",
        }}
        modalContentProps={{
          sx: { py: 1 },
          content: (
            <>
              <FormDeviceSelected />
            </>
          ),
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleClose}
              >
                Đóng
              </Button>
              <Button variant="contained" type="submit">
                Hoàn tất
              </Button>
            </>
          ),
        }}
      />
    </FormProvider>
  );
};

export type ModalSelectedType = {
  deviceSelected: (IEduDevice & { totalBorrow: number })[];
};
const INIT_VALUE: ModalSelectedType = {
  deviceSelected: [],
};

export default memo(ChooseDeviceOfRoomModal);
