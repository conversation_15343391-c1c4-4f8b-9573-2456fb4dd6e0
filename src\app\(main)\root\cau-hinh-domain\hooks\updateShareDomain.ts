import http from "@/api";
import { ApiConstant, DataConstant } from "@/constant";
import { DataResponseModel } from "@/models/response.model";
import { CommonUtils } from "@/utils";
import { extractErrorMessage } from "@/utils/common.utils";
import { toast } from "sonner";
import stringFormat from "string-format";

export const updateShareDomain = async ({
  id,
  isShareDomain,
  url,
  onSuccess,
}: {
  id: string | number;
  isShareDomain: DataConstant.BOOLEAN_TYPE;
  url: string;
  onSuccess?: () => void;
}) => {
  try {
    CommonUtils.toggleAppProgress(true);

    const response: DataResponseModel<any> = await http.patch(
      stringFormat(url, { isShareDomain, id }),
      { isShareDomain, id }
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      toast.success("Thành công!", {
        description: "<PERSON><PERSON> liệu đã cập nhật thành công.",
      });
      onSuccess?.();
    } else {
      throw new Error(response?.message || "Đã có lỗi xảy ra");
    }
  } catch (error: any) {
    const description = extractErrorMessage(error);
    toast.error("Thất bại!", {
      description,
    });
  } finally {
    CommonUtils.toggleAppProgress(false);
  }
};
