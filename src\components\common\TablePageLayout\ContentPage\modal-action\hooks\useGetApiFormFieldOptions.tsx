import { IOption } from "@/components/common/AppAutoComplete";
import { useEffect, useMemo, useState } from "react";
import { FormFieldConfig } from "../../../type";
import { Control, FieldValues, Path, useWatch } from "react-hook-form";
import { useTableStore } from "../../../table-store/TableContext";

export default function useGetApiFormFieldOptions<R extends FieldValues>(
  formField?: FormFieldConfig<R>[],
  control?: Control<any>
) {
  const store = useTableStore<any>();
  const filter = store((state) => state.filter);

  const watchedKeys = useMemo(() => {
    return Array.from(
      new Set(
        formField
          ?.flatMap((item) => item.selectConfig?.changeKey ?? [])
          .filter(Boolean)
      )
    );
  }, [formField]);

  const watchedValues = useWatch({
    control,
    name: watchedKeys as unknown as Path<any>,
  });

  const allValues = useMemo(() => {
    const obj: Record<string, any> = {};
    watchedKeys.forEach((key, idx) => {
      obj[String(key)] = watchedValues?.[idx];
    });
    return obj;
  }, [watchedKeys, watchedValues]);

  const [optionsObj, setOptionsObj] = useState<Record<string, IOption[]>>({});

  const itemsWithApi = useMemo(() => {
    return formField?.filter((f) => f.apiListUrl) || [];
  }, [formField]);

  const watchedFilterVals = useMemo(() => {
    const filterMap = (filter ?? []).reduce((acc, f) => {
      acc[f.key] = f.value;
      return acc;
    }, {} as Record<string, any>);

    return itemsWithApi.reduce((acc, field) => {
      const keys = field.selectConfig?.filterParams;
      if (keys?.length) {
        acc[field.key as string] = keys.reduce((obj, key) => {
          if (key in filterMap) obj[key] = filterMap[key];
          return obj;
        }, {} as Record<string, any>);
      }
      return acc;
    }, {} as Record<string, any>);
  }, [filter, itemsWithApi]);

  // Set các options tĩnh
  useEffect(() => {
    const staticOptionsMap: Record<string, IOption[]> = {};
    formField?.forEach((item) => {
      if (
        !item.apiListUrl &&
        (item.type === "select" ||
          item.type === "radio" ||
          item.type === "tree")
      ) {
        staticOptionsMap[item.key] = item.selectConfig?.options || [];
      }
    });
    setOptionsObj((prev) => ({ ...prev, ...staticOptionsMap }));
  }, [formField]);

  return {
    optionsObj,
    setOptionsObj,
    itemsWithApi,
    allValues,
    watchedFilterVals,
  };
}
