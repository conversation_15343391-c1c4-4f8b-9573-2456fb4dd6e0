"use client";

import { TablePageLayout } from "@/components/common";
import {
  FilterConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import { BORROW_ROOM, ROOM, TEACHER } from "@/constant/api.const";
import {
  BORROW_TYPE,
  BorrowStatusEnum,
  IBorrowRoom,
} from "@/models/eduDevice.model";
import { formatDayjsWithType } from "@/utils/format.utils";
import { ColumnDef } from "@tanstack/react-table";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import FilterCustom from "./FilterCustom";
import CollapseFilterCustom from "@/app/(main)/muon-tra/muon-kho-phong/CollapseFilterCustom";
import { createInjectableSaga } from "@/saga/injectableSaga";
import { systemSaga } from "@/saga/system.saga";
import { useAppDispatch, useAppSelector, useAppStore } from "@/redux/hook";
import { selectSchoolWeekList, systemActions } from "@/redux/system.slice";
import { BorrowStatusCell } from "@/components/sn-common/BorrowStatusCell";
import { CheckIcon } from "@/components/icons";
import dayjs from "dayjs";
import { shallowEqual } from "react-redux";
import { Skeleton } from "@mui/material";
import dynamic from "next/dynamic";
const BorrowRoom = dynamic(
  () => import("@/app/(main)/muon-tra/muon-kho-phong/BorrowRoom"),
  {
    ssr: false,
    loading: () => (
      <Skeleton
        variant="rectangular"
        width={115}
        height={36}
        sx={{ borderRadius: 0.5 }}
        animation="wave"
      />
    ),
  }
);

const BorrowRoomPage = () => {
  const store = useAppStore();
  const tableRef = useRef<ITableRef>(null);
  const dispatch = useAppDispatch();
  const schoolYearSelected = useAppSelector(
    (state) => state.appReducer.schoolYearSelected,
    shallowEqual
  );
  const schoolWeekList = useAppSelector(selectSchoolWeekList);
  const [borrowType, setBorrowType] = useState(BORROW_TYPE.week);

  const tableProps = useMemo(() => {
    return {
      columns: borrowType === BORROW_TYPE.week ? columnsWeek : columnsLongTerm,
    };
  }, [borrowType]);

  const changeBorrowType = useCallback(
    (value) => setBorrowType(Number(value)),
    []
  );

  const filterConfig: FilterConfig[] = useMemo(() => {
    const currentDate = dayjs();
    let defaultWeek = schoolWeekList.find((week) => {
      const weekStart = dayjs(week.fromDate);
      const weekEnd = dayjs(week.toDate);
      return currentDate.isBetween(weekStart, weekEnd, "day", "[]");
    });
    if (!defaultWeek) {
      defaultWeek = schoolWeekList?.[schoolWeekList.length - 1];
    }

    return [
      {
        key: "borrowType",
        value: BORROW_TYPE.week,
        onChangeValue: changeBorrowType,
      },
      {
        key: "fromDate",
        value: defaultWeek?.fromDate,
        required: true,
      },
      {
        key: "toDate",
        value: defaultWeek?.toDate,
        required: true,
      },
      {
        key: "borrowStatus",
        type: "select",
        label: "Trạng thái",
        size: 6,
        options: BORROW_ROOM_STATUS_LIST,
        isAdvanced: true,
      },
      {
        key: "roomIds",
        type: "select",
        label: "Kho/Phòng",
        size: 6,
        apiListUrl: ROOM,
        isAdvanced: true,
        isMulti: true,
        hasAllOption: true,
      },
      {
        key: "teacherIds",
        type: "select",
        label: "Giáo viên",
        size: 12,
        apiListUrl: TEACHER,
        isAdvanced: true,
        isMulti: true,
        hasAllOption: true,
      },
      {
        key: "searchKey",
        type: "text",
        label: "Nhập tên kho/phòng",
        isCollapse: true,
        size: 3,
      },
    ];
  }, [schoolWeekList]);

  useEffect(() => {
    createInjectableSaga("systemReducer", systemSaga).injectInto(store);

    return () => {
      dispatch(systemActions.systemReset());
    };
  }, []);

  useEffect(() => {
    if (schoolYearSelected) {
      dispatch(systemActions.getSchoolWeekList({}));
    }
  }, [dispatch, schoolYearSelected]);

  return (
    <TablePageLayout
      ref={tableRef}
      tableProps={tableProps}
      filterConfig={filterConfig}
      filterCustom={(props) => <FilterCustom props={props} />}
      apiUrl={BORROW_ROOM}
      collapseFilterCustom={(props) => <CollapseFilterCustom props={props} />}
      methodFetch="POST"
      customActions={
        <BorrowRoom
          fetchCurrentData={() => tableRef.current?.fetchCurrentData?.()}
        />
      }
    />
  );
};

export default BorrowRoomPage;

const BORROW_ROOM_STATUS_LIST = [
  {
    id: BorrowStatusEnum.Borrowing,
    label: "Đang mượn",
  },
  {
    id: BorrowStatusEnum.Returned,
    label: "Đã trả",
  },
];

const columnsLongTerm: ColumnDef<IBorrowRoom>[] = [
  {
    id: "fromDate",
    header: "Từ ngày",
    accessorFn: (row) => formatDayjsWithType(row.fromDate),
    size: 120,
  },
  {
    id: "toDate",
    header: "Đến ngày",
    accessorFn: (row) => formatDayjsWithType(row.toDate),
    size: 120,
  },
  {
    id: "roomName",
    header: "Phòng",
    accessorKey: "roomName",
    size: 180,
  },
  {
    id: "teacherName",
    header: "Giáo viên",
    accessorKey: "teacherName",
    size: 150,
  },
  {
    id: "attachedDeviceName",
    header: "DSTB mượn kèm",
    accessorKey: "attachedDeviceName",
    size: 120,
  },
  {
    id: "subjectName",
    header: "Môn học",
    accessorKey: "subjectName",
    size: 120,
  },
  {
    id: "purposeName",
    header: "Mục đích",
    accessorKey: "purposeName",
    size: 120,
  },
  {
    id: "statusName",
    header: "Trạng thái",
    accessorKey: "statusName",
    cell: ({ row }) => <BorrowStatusCell status={row.original.status} />,
    size: 120,
    meta: {
      align: "center",
    },
  },
  {
    id: "notes",
    header: "Ghi chú",
    accessorKey: "notes",
    size: 120,
  },
];

const columnsWeek: ColumnDef<IBorrowRoom>[] = [
  {
    id: "fromDate",
    header: "Ngày",
    accessorFn: (row) => formatDayjsWithType(row.fromDate),
    size: 120,
  },
  {
    id: "roomName",
    header: "Phòng",
    accessorKey: "roomName",
    size: 180,
  },
  {
    id: "teacherName",
    header: "Giáo viên",
    accessorKey: "teacherName",
    size: 150,
  },
  {
    id: "periodName",
    header: "Ca",
    accessorKey: "periodName",
    columns: [
      {
        id: "morning",
        header: "Sáng",
        accessorKey: "morning",
        columns: [
          {
            id: "s1",
            header: "Tiết 1",
            cell: ({ row }) => <CheckIconCell value={row.original.s1} />,
            meta: {
              align: "center",
            },
            size: 70,
          },
          {
            id: "s2",
            header: "Tiết 2",
            cell: ({ row }) => <CheckIconCell value={row.original.s2} />,
            meta: {
              align: "center",
            },
            size: 70,
          },
          {
            id: "s3",
            header: "Tiết 3",
            cell: ({ row }) => <CheckIconCell value={row.original.s3} />,
            meta: {
              align: "center",
            },
            size: 70,
          },
          {
            id: "s4",
            header: "Tiết 4",
            cell: ({ row }) => <CheckIconCell value={row.original.s4} />,
            meta: {
              align: "center",
            },
            size: 70,
          },
          {
            id: "s5",
            header: "Tiết 5",
            cell: ({ row }) => <CheckIconCell value={row.original.s5} />,
            meta: {
              align: "center",
            },
            size: 70,
          },
        ],
      },
      {
        id: "afternoon",
        header: "Chiều",
        accessorKey: "afternoon",
        columns: [
          {
            id: "c1",
            header: "Tiết 1",
            cell: ({ row }) => <CheckIconCell value={row.original.c1} />,
            meta: {
              align: "center",
            },
            size: 70,
          },
          {
            id: "c2",
            header: "Tiết 2",
            cell: ({ row }) => <CheckIconCell value={row.original.c2} />,
            meta: {
              align: "center",
            },
            size: 70,
          },
          {
            id: "c3",
            header: "Tiết 3",
            cell: ({ row }) => <CheckIconCell value={row.original.c3} />,
            meta: {
              align: "center",
            },
            size: 70,
          },
          {
            id: "c4",
            header: "Tiết 4",
            cell: ({ row }) => <CheckIconCell value={row.original.c4} />,
            meta: {
              align: "center",
            },
            size: 70,
          },
          {
            id: "c5",
            header: "Tiết 5",
            cell: ({ row }) => <CheckIconCell value={row.original.c5} />,
            meta: {
              align: "center",
            },
            size: 70,
          },
        ],
      },
    ],
  },
  {
    id: "statusName",
    header: "Trạng thái",
    accessorKey: "statusName",
    cell: ({ row }) => <BorrowStatusCell status={row.original.status} />,
    size: 120,
    meta: {
      align: "center",
    },
  },
];

const CheckIconCell = ({ value }: { value: number }) => {
  return (
    <>{Boolean(value) && <CheckIcon color="success" sx={{ fontSize: 20 }} />}</>
  );
};
