"use client";

import { TablePageLayout } from "@/components/common";
import {
  ActionType,
  FilterConfig,
  FormConfig,
} from "@/components/common/TablePageLayout/type";
import { DEVICE_LIQUIDATION } from "@/constant/api.const";
import { useAppDispatch, useAppStore } from "@/redux/hook";
import { systemActions } from "@/redux/system.slice";
import { createInjectableSaga } from "@/saga/injectableSaga";
import { systemSaga } from "@/saga/system.saga";
import { formatDayjsWithType } from "@/utils/format.utils";
import { ColumnDef } from "@tanstack/react-table";
import dynamic from "next/dynamic";
import { useEffect, useState } from "react";
import { IDeviceLiquidation } from "../deviceLiquidation.model";
import { deviceLiquidationSaga } from "../deviceLiquidation.saga";
import { deviceLiquidationActions } from "../deviceLiquidation.slice";
const CreateModal = dynamic(() => import("./CreateModal"), {
  ssr: false,
});
const EditModal = dynamic(() => import("./EditModal"), {
  ssr: false,
});

const DeviceLiquidation = () => {
  const dispatch = useAppDispatch();
  const store = useAppStore();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    createInjectableSaga("systemReducer", systemSaga).injectInto(store);
    createInjectableSaga("deviceLiquidation", deviceLiquidationSaga).injectInto(
      store
    );

    setIsClient(true);

    return () => {
      dispatch(systemActions.systemReset());
      dispatch(deviceLiquidationActions.resetdeviceLiquidation());
    };
  }, []);

  if (!isClient) return null;

  return (
    <TablePageLayout<IDeviceLiquidation>
      tableProps={tableProps}
      actions={ACTIONS}
      CreateModalComponent={CreateModal}
      EditModalComponent={EditModal}
      apiUrl={DEVICE_LIQUIDATION}
      formConfig={FORM_FIELD_CONFIG}
      filterConfig={FILTER_CONFIG}
    />
  );
};

export default DeviceLiquidation;

const FORM_FIELD_CONFIG: FormConfig<IDeviceLiquidation> = {
  deleteUrl: DEVICE_LIQUIDATION,
  detailUrl: DEVICE_LIQUIDATION,
};

const FILTER_CONFIG: FilterConfig[] = [
  {
    key: "dateRange",
    type: "dateRange",
    label: "Ngày chứng từ",
    keyDateRange: ["fromDate", "toDate"],
    value: [null, null],
  },
  {
    key: "searchKey",
    type: "text",
    label: "Tìm kiếm",
  },
];

const ACTIONS: ActionType[] = ["create", "update"];

const COLUMN: ColumnDef<IDeviceLiquidation>[] = [
  {
    id: "documentNumber",
    accessorKey: "documentNumber",
    header: "Số phiếu",
    size: 50,
  },
  {
    id: "documentDate",
    header: "Ngày nhập",
    accessorFn: (row) => formatDayjsWithType(row.documentDate),
    size: 50,
  },
  {
    id: "notes",
    header: "Nội dung",
    accessorKey: "notes",
  },
];

const tableProps = {
  columns: COLUMN,
};
