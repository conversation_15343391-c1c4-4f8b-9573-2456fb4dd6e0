import {
  AppAutoComplete,
  AppConfirmModal,
  AppTable,
  AppTextField,
  IOption,
} from "@/components/common";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { ColumnDef } from "@tanstack/react-table";
import React, { memo, useEffect, useMemo, useState } from "react";
import {
  equipmentDocumentActions,
  makeSelectCountryOfDevices,
  makeSelectExpireDateOfDevices,
  makeSelectMaxCodeOfDevices,
  makeSelectMinCodeOfDevices,
  makeSelectRoomOfDevices,
  makeSelectTotalDevicesOfDevices,
  makeSelectTotalPricesOfDevices,
  selectEquipmentDocumentEntry,
} from "../../../equipmentDocument.slice";
import { IDeviceRoomGroup } from "../../../equipmentDocument.model";
import DeleteCell from "@/components/common/table/cell/DeleteCell";
import EditCell from "@/components/common/table/cell/EditCell";
import dynamic from "next/dynamic";
import { eduDeviceActions } from "@/redux/device/eduDevice.slice";
import { selectCountries, selectRoomList } from "@/redux/system.slice";
import AppDatePicker from "@/components/common/AppDatePicker";
import { Dayjs } from "dayjs";
import { groupDevicesByRoomSimple } from "../../../helper";
import { formatPrice } from "@/utils/format.utils";

const EditDeviceModal = dynamic(
  () =>
    import("../../device-modal/EditDeviceModal").then((mod) =>
      React.memo(mod.default)
    ),
  { ssr: false }
);

const TocTable = ({ tableProps }) => {
  const dispatch = useAppDispatch();

  const equipmentDocumentEntry = useAppSelector(selectEquipmentDocumentEntry);
  const [dataDelete, setDataDelete] = useState<IDeviceRoomGroup | null>(null);
  const [dataEdit, setDataEdit] = useState<IDeviceRoomGroup | null>(null);
  const columns = useMemo(() => getColumns(setDataDelete, setDataEdit), []);
  const data = useMemo(() => {
    return groupDevicesByRoomSimple(equipmentDocumentEntry);
  }, [equipmentDocumentEntry]);

  useEffect(() => {
    dispatch(equipmentDocumentActions.initDeviceIdsRoom(data));
  }, [data]);

  return (
    <>
      <AppTable
        columns={columns}
        data={data}
        totalData={data?.length}
        hasDefaultPagination
        {...tableProps}
      />
      {Boolean(dataDelete) && (
        <AppConfirmModal
          modalTitleProps={{
            title: "Bạn xác nhận xóa đầu mục này",
          }}
          isOpen={Boolean(dataDelete)}
          onClose={() => setDataDelete(null)}
          onConfirm={() =>
            dispatch(
              equipmentDocumentActions.deleteEquipmentDocumentEntry(
                dataDelete as IDeviceRoomGroup
              )
            )
          }
        />
      )}
      {Boolean(dataEdit) && (
        <EditDeviceModal
          isOpen={Boolean(dataEdit)}
          onClose={() => setDataEdit(null)}
          data={dataEdit}
        />
      )}
    </>
  );
};

export default memo(TocTable);

const RoomCell = memo(({ data }: { data: IDeviceRoomGroup }) => {
  const dispatch = useAppDispatch();

  const roomList = useAppSelector(selectRoomList);
  const selectRoomOfDevices = useMemo(makeSelectRoomOfDevices, []);
  const roomObj = useAppSelector((state) =>
    selectRoomOfDevices(
      state,
      data.deviceDefinitionId as string,
      data.id as string
    )
  );

  const handleChangeRoom = (room: IOption) => {
    dispatch(
      equipmentDocumentActions.changeRoomOfDevice({
        data,
        room,
      })
    );
  };

  return (
    <AppAutoComplete
      options={roomList}
      value={roomObj}
      disableClearable
      onChange={(_, value) => handleChangeRoom(value as IOption)}
    />
  );
});

const CountryCell = memo(({ data }: { data: IDeviceRoomGroup }) => {
  const dispatch = useAppDispatch();

  const countries = useAppSelector(selectCountries);

  const selectCountryOfDevices = useMemo(makeSelectCountryOfDevices, []);
  const countryObj = useAppSelector((state) =>
    selectCountryOfDevices(
      state,
      data.deviceDefinitionId as string,
      data.id as string
    )
  );

  const handleChangeCountry = (country: IOption) => {
    dispatch(
      equipmentDocumentActions.changeCountryOfDevice({
        data,
        country,
      })
    );
  };

  return (
    <AppAutoComplete
      options={countries}
      value={countryObj}
      onChange={(_, value) => handleChangeCountry(value as IOption)}
    />
  );
});

const ExpireDateCell = memo(({ data }: { data: IDeviceRoomGroup }) => {
  const dispatch = useAppDispatch();

  const selectExpireDateOfDevices = useMemo(makeSelectExpireDateOfDevices, []);
  const expireDate = useAppSelector((state) =>
    selectExpireDateOfDevices(
      state,
      data.deviceDefinitionId as string,
      data.id as string
    )
  );

  const handleChangeDate = (expireDate: Date | Dayjs | null) => {
    dispatch(
      equipmentDocumentActions.changeExpireDateOfDevice({
        data,
        expireDate,
      })
    );
  };

  return (
    <AppDatePicker
      value={expireDate}
      onChange={(value) => handleChangeDate(value)}
    />
  );
});

const QuantityCell = memo(({ data }: { data: IDeviceRoomGroup }) => {
  const selectTotalDevices = useMemo(makeSelectTotalDevicesOfDevices, []);
  const total = useAppSelector((state) =>
    selectTotalDevices(
      state,
      data.deviceDefinitionId as string,
      data.id as string
    )
  );
  const dispatch = useAppDispatch();
  const handleChangeQuantity = (e: React.ChangeEvent<HTMLInputElement>) => {
    const v = Number(e.target.value);
    if (!Number.isNaN(v)) {
      dispatch(
        equipmentDocumentActions.changeQuantityOfDevice({
          data,
          value: v,
        })
      );
    }
  };

  return (
    <>
      <AppTextField
        value={total}
        onChange={handleChangeQuantity}
        type="number"
        disabled={typeof data.deviceDefinitionId === "number"}
        slotProps={{
          htmlInput: {
            min: 1,
            max: 1000,
          },
        }}
      />
    </>
  );
});

const StartCell = memo(({ data }: { data: IDeviceRoomGroup }) => {
  const dispatch = useAppDispatch();
  const { deviceDefinitionId: id } = data;

  const selectMinCode = useMemo(makeSelectMinCodeOfDevices, []);
  const minCode = useAppSelector((state) =>
    selectMinCode(state, id as string, data.id as string)
  );

  const handleChangeQuantity = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const newValue = event.target.value;
    dispatch(
      equipmentDocumentActions.changeMinRegister({
        data,
        value: Number(newValue),
      })
    );
  };
  return (
    <>
      <AppTextField
        value={String(minCode).padStart(4, "0")}
        onChange={handleChangeQuantity}
        type="number"
        disabled={
          typeof data.deviceDefinitionId === "number" ||
          Boolean(data.isManageQuantity)
        }
        slotProps={{
          htmlInput: {
            min: 1,
          },
        }}
      />
    </>
  );
});

const EndCell = memo(({ data }: { data: IDeviceRoomGroup }) => {
  const { deviceDefinitionId: id } = data;

  const selectMaxCode = useMemo(makeSelectMaxCodeOfDevices, []);
  const maxCode = useAppSelector((state) =>
    selectMaxCode(state, id as string, data.id as string)
  );

  return (
    <>
      <AppTextField
        value={String(maxCode).padStart(4, "0")}
        type="number"
        disabled={true}
      />
    </>
  );
});

const TotalPricesCell = memo(({ data }: { data: IDeviceRoomGroup }) => {
  const selectTotalPrices = useMemo(makeSelectTotalPricesOfDevices, []);
  const totalPrices = useAppSelector((state) =>
    selectTotalPrices(
      state,
      data.deviceDefinitionId as string,
      data.id as string
    )
  );

  return <>{formatPrice(totalPrices)}</>;
});

const getColumns = (onDelete, onEdit): ColumnDef<IDeviceRoomGroup>[] => [
  {
    id: "index",
    header: "STT",
    cell: ({ row }) => row.index + 1,
    size: 60,
    meta: {
      align: "center",
    },
  },
  {
    id: "delete",
    header: "Xóa",
    cell: ({ row }) => <DeleteCell onClick={() => onDelete(row.original)} />,
    size: 60,
    meta: {
      align: "center",
    },
  },
  {
    id: "edit",
    header: "Sửa",
    cell: ({ row }) => <EditCellApi data={row.original} onEdit={onEdit} />,
    size: 60,
    meta: {
      align: "center",
    },
  },
  {
    id: "deviceCode",
    header: "Mã thiết bị",
    accessorKey: "deviceCode",
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
  },
  {
    id: "room",
    header: "Kho/phòng",
    cell: ({ row }) => <RoomCell data={row.original} />,
    accessorKey: "room",
    size: 250,
  },
  {
    id: "deviceUnitName",
    header: "Đơn vị tính",
    accessorKey: "deviceUnitName",
    size: 80,
  },
  {
    id: "totalDevices",
    header: "Số lượng",
    size: 100,
    accessorKey: "totalDevices",
    meta: {
      align: "right",
    },
    cell: ({ row }) => <QuantityCell data={row.original} />,
  },
  {
    id: "start",
    header: "Từ",
    size: 100,
    accessorKey: "start",
    meta: {
      align: "right",
    },
    cell: ({ row }) => <StartCell data={row.original} />,
  },
  {
    id: "end",
    header: "Đến",
    size: 100,
    accessorKey: "end",
    meta: {
      align: "right",
    },
    cell: ({ row }) => <EndCell data={row.original} />,
  },

  {
    id: "totalPrices",
    header: "Tổng giá trị",
    accessorKey: "totalPrices",
    meta: {
      align: "right",
    },
    cell: ({ row }) => <TotalPricesCell data={row.original} />,
  },
  {
    id: "expireDate",
    header: "Hạn sử dụng",
    size: 180,
    cell: ({ row }) => <ExpireDateCell data={row.original} />,
  },
  {
    id: "country",
    header: "Nước sản xuất",
    cell: ({ row }) => <CountryCell data={row.original} />,
    size: 200,
  },
];

const EditCellApi = memo(
  ({
    data,
    onEdit,
  }: {
    data: IDeviceRoomGroup;
    onEdit: (data: IDeviceRoomGroup) => void;
  }) => {
    const dispatch = useAppDispatch();
    return (
      <EditCell
        onClick={() => {
          if (typeof data.deviceDefinitionId !== "number") {
            onEdit(data);
            return;
          }
          dispatch(
            eduDeviceActions.chooseDevice({
              data: { ...data, id: data.deviceDefinitionId },
              onSuccess: (device) =>
                onEdit({
                  ...device,
                  ...data,
                  id: data.deviceDefinitionId,
                }),
            })
          );
        }}
      />
    );
  }
);
