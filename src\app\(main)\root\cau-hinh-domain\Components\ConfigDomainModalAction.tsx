import { But<PERSON> } from "@mui/material";
import {
  Control,
  FieldErrors,
  UseFormSetValue,
  useForm,
} from "react-hook-form";
import AddConfigModalPanel from "./AddConfigModalPanel";
import { AppModal, IOption } from "@/components/common";
import { IDomainConfigModel } from "@/app/(main)/root/cau-hinh-domain/domain.model";
import { DataConstant } from "@/constant";
import { postDomainConfig } from "../hooks/usePostDomainConfig";
import { useCallback, useEffect } from "react";
import { updateDomainConfig } from "../hooks/useUpdateDomainConfig";
import { SaveIcon } from "@/components/icons";

const ConfigDomainModalAction = ({
  open,
  onClose,
  onSuccess,
  dataSelected,
}: AddConfigDomainModalProps) => {
  const {
    control,
    formState: { errors },
    reset,
    handleSubmit,
    setValue: handleSetValueForm,
  } = useForm<IAddDomainConfigViewModel>({
    defaultValues: INIT_VALUE,
  });

  const handleResetState = useCallback(() => {
    reset(INIT_VALUE);
    onClose();
  }, []);

  const handleSubmitData = (data: IAddDomainConfigViewModel) => {
    const newData = convertDataViewToDataPayload(data);
    if (dataSelected) {
      updateDomainConfig({ id: dataSelected.id, body: newData, onSuccess });
    } else {
      postDomainConfig({ body: newData, onSuccess });
    }

    handleResetState();
  };

  useEffect(() => {
    if (dataSelected) {
      reset(getDefaultValueForEditModal(dataSelected));
    }

    return () => {
      reset(INIT_VALUE);
    };
  }, [dataSelected]);

  return (
    <AppModal
      component="form"
      onSubmit={handleSubmit(handleSubmitData)}
      isOpen={open}
      onClose={handleResetState}
      maxWidth="xs"
      modalTitleProps={{
        title: dataSelected ? "Cập nhật thông tin domain" : "Thêm mới domain",
      }}
      modalActionsProps={{
        children: (
          <>
            <Button
              variant="outlined"
              onClick={handleResetState}
              color="secondary"
            >
              Đóng
            </Button>
            <Button type="submit" variant="contained" endIcon={<SaveIcon />}>
              Ghi
            </Button>
          </>
        ),
      }}
      modalContentProps={{
        content: (
          <AddConfigModalPanel
            onSetValueForm={handleSetValueForm}
            control={control}
            errors={errors}
            dataSelected={dataSelected}
          />
        ),
      }}
    />
  );
};

const INIT_VALUE: IAddDomainConfigViewModel = {
  groupUnitCode: null,
  schoolCode: null,
  doetCode: null,
  divisionCode: null,
  name: "",
  domainUrl: "",
  isShareDomain: false,
  status: true,
};

export type HookFormComponentType = {
  errors: FieldErrors<typeof INIT_VALUE>;
  control: Control<any>;
  onSetValueForm: UseFormSetValue<typeof INIT_VALUE>;
};

type AddConfigDomainModalProps = {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  dataSelected?: IDomainConfigModel;
};

export interface IAddDomainConfigViewModel {
  groupUnitCode?: null | IOption;
  schoolCode?: null | IOption;
  doetCode?: null | IOption;
  divisionCode?: null | IOption;
  domainUrl: string;
  isShareDomain: boolean;
  status: boolean;
  name: string;
  unitLevel?: null | IOption;
}

const convertDataViewToDataPayload = (data: IAddDomainConfigViewModel) => {
  const newData: any = {
    groupUnitCode: null,
    schoolCode: null,
    doetCode: null,
    divisionCode: null,
    name: data.name,
    domainUrl: data.domainUrl,
    isShareDomain: DataConstant.BOOLEAN_TYPE.false,
    status: DataConstant.BOOLEAN_TYPE.true,
  };

  if (data.groupUnitCode) {
    newData.groupUnitCode = data.groupUnitCode.id;
  }

  if (data.divisionCode) {
    newData.divisionCode = data.divisionCode.code;
    newData.schoolCode = data.divisionCode.code;
  }
  if (data.doetCode) {
    newData.doetCode = data.doetCode.code;
    newData.schoolCode = data.doetCode.code;
  }
  if (data.schoolCode) {
    newData.schoolCode = data.schoolCode.schoolCode;
  }

  newData.isShareDomain = data.isShareDomain
    ? DataConstant.BOOLEAN_TYPE.true
    : DataConstant.BOOLEAN_TYPE.false;
  newData.status = data.status
    ? DataConstant.STATUS_TYPE.active
    : DataConstant.STATUS_TYPE.inActive;

  return newData;
};

const getDefaultValueForEditModal = (data?: IDomainConfigModel) => {
  if (data) {
    return {
      groupUnitCode: data.groupUnitCode
        ? DataConstant.DON_VI_LIST.find(
            (item) => item.id === data.groupUnitCode
          )
        : null,
      schoolCode: data.schoolCode
        ? {
            label: data.schoolName,
            schoolCode: data.schoolCode,
          }
        : null,

      doetCode: data.doetCode
        ? { code: data.doetCode, label: data.doetName }
        : null,

      divisionCode: data.divisionCode
        ? {
            code: data.divisionCode,
            label: data.divisionName,
          }
        : null,
      name: data.name ?? "",
      domainUrl: data.domainUrl,
      isShareDomain: data.isShareDomain === DataConstant.BOOLEAN_TYPE.true,
      status: data.status === DataConstant.STATUS_TYPE.active,
    };
  }
  return { ...INIT_VALUE };
};

export default ConfigDomainModalAction;
