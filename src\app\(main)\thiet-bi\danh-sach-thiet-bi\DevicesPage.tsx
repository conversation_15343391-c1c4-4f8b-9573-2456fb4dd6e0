"use client";

import { TablePageLayout } from "@/components/common";
import { MINIMUM_QUANTITY } from "./type";
import {
  DEVICE_LIST,
  DEVICE_TYPE,
  DEVICES,
  GET_GRADE,
  ROOM,
  SUBJECT,
} from "@/constant/api.const";
import { ColumnDef } from "@tanstack/react-table";
import { formatNumber } from "@/utils/format.utils";
import {
  FilterConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import FilterCustom from "./components/FilterCustom";
import dynamic from "next/dynamic";
import { Skeleton, Typography } from "@mui/material";
import MoreActions from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/components/MoreActions";
import { useAppDispatch, useAppSelector, useAppStore } from "@/redux/hook";
import {
  deviceActions,
  selectorResetRowSelected,
} from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.slice";
import { useEffect, useMemo, useRef, useState } from "react";
import { createInjectableSaga } from "@/saga/injectableSaga";
import { systemSaga } from "@/saga/system.saga";
import { systemActions } from "@/redux/system.slice";
import { DataConstant } from "@/constant";
import { IDevice } from "@/models/eduDevice.model";
import { deviceSaga } from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.saga";
import HistoryDeviceModal from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/components/HistoryDevice/HistoryDeviceModal";
import { borrowRequestSaga } from "@/saga/device/borrowRequest.saga";
import { borrowRequestActions } from "@/redux/device/borrowRequest.slice";

const BorrowDevices = dynamic(
  () =>
    import("@/app/(main)/thiet-bi/danh-sach-thiet-bi/components/BorrowDevices"),
  {
    ssr: false,
    loading: () => (
      <Skeleton
        variant="rectangular"
        width={115}
        height={36}
        sx={{ borderRadius: 0.5 }}
        animation="wave"
      />
    ),
  }
);

const DevicesPage = () => {
  const dispatch = useAppDispatch();
  const tableRef = useRef<ITableRef>(null);
  const store = useAppStore();
  const [historyDeviceId, setHistoryDeviceId] = useState<number | null>(null);
  const rowSelected = useAppSelector(selectorResetRowSelected);

  const tableProps = useMemo(() => {
    return {
      rowSelected,
      columns: getColumns({
        onOpenHistoryDevice: (deviceId) => setHistoryDeviceId(deviceId),
      }),
      onRowSelectionChange: (selectedRows) => {
        dispatch(deviceActions.setDeviceSelected(selectedRows));
      },
    };
  }, [rowSelected]);

  useEffect(() => {
    createInjectableSaga("deviceReducer", deviceSaga).injectInto(store);
    createInjectableSaga("systemReducer", systemSaga).injectInto(store);
    createInjectableSaga("borrowRequestReducer", borrowRequestSaga).injectInto(
      store
    );

    return () => {
      dispatch(systemActions.systemReset());
      dispatch(deviceActions.resetDevice());
      dispatch(borrowRequestActions.reset());
    };
  }, []);

  return (
    <>
      <TablePageLayout<IDevice>
        ref={tableRef}
        apiUrl={DEVICES}
        methodFetch="POST"
        tableProps={tableProps}
        filterConfig={filterConfig}
        filterCustom={(props) => <FilterCustom props={props} />}
        customActions={
          <>
            <MoreActions
              fetchCurrentData={() => tableRef.current?.fetchCurrentData?.()}
            />
            <BorrowDevices
              fetchCurrentData={() => tableRef.current?.fetchCurrentData?.()}
            />
          </>
        }
        actions={["check"]}
      />
      <HistoryDeviceModal
        open={!!historyDeviceId}
        onClose={() => setHistoryDeviceId(null)}
        deviceId={historyDeviceId}
      />
    </>
  );
};

export default DevicesPage;

const filterConfig: FilterConfig[] = [
  {
    key: "isGroupByDefinition",
    value: DataConstant.BOOLEAN_TYPE.true,
  },
  {
    key: "searchKey",
  },
  {
    key: "schoolSubjectIds",
    type: "select",
    label: "Môn học",
    size: 6,
    apiListUrl: SUBJECT,
    isAdvanced: true,
    isMulti: true,
    hasAllOption: true,
  },
  {
    key: "roomIds",
    type: "select",
    label: "Kho/Phòng",
    size: 6,
    apiListUrl: ROOM,
    isAdvanced: true,
    isMulti: true,
    hasAllOption: true,
  },
  {
    key: "schoolDeviceTypeIds",
    type: "select",
    label: "Loại thiết bị",
    size: 6,
    apiListUrl: DEVICE_TYPE,
    isAdvanced: true,
    isMulti: true,
    hasAllOption: true,
  },
  {
    key: "gradeCodes",
    type: "select",
    label: "Khối lớp",
    size: 6,
    apiListUrl: GET_GRADE,
    selectedKey: "code",
    isAdvanced: true,
    isMulti: true,
    hasAllOption: true,
  },
  {
    key: "isConsumable",
    type: "select",
    label: "Thiết bị tiêu hao",
    size: 6,
    isAdvanced: true,
    options: [
      {
        label: "Có",
        id: DataConstant.BOOLEAN_TYPE.true,
      },
      {
        label: "Không",
        id: DataConstant.BOOLEAN_TYPE.false,
      },
    ],
  },
  {
    key: "isSelfMade",
    type: "select",
    label: "Thiết bị tự làm",
    size: 6,
    isAdvanced: true,
    options: [
      {
        label: "Có",
        id: DataConstant.BOOLEAN_TYPE.true,
      },
      {
        label: "Không",
        id: DataConstant.BOOLEAN_TYPE.false,
      },
    ],
  },
  {
    key: "isInMinimumDeviceCategory",
    type: "select",
    label: "Thuộc danh mục TB tối thiểu",
    size: 6,
    isAdvanced: true,
    options: [
      {
        label: "Có",
        id: DataConstant.BOOLEAN_TYPE.true,
      },
      {
        label: "Không",
        id: DataConstant.BOOLEAN_TYPE.false,
      },
    ],
  },
  {
    key: "checkMinimumQuantitys",
    type: "select",
    label: "Đối chiếu SL tối thiểu",
    size: 6,
    isAdvanced: true,
    isMulti: true,
    hasAllOption: true,
    options: [
      {
        label: "Đủ",
        id: MINIMUM_QUANTITY.Sufficient,
      },
      {
        label: "Thừa",
        id: MINIMUM_QUANTITY.Surplus,
      },
      {
        label: "Thiếu",
        id: MINIMUM_QUANTITY.Deficient,
      },
    ],
  },
];

const getColumns = ({
  onOpenHistoryDevice,
}: {
  onOpenHistoryDevice: (deviceId: number) => void;
}): ColumnDef<IDevice>[] => [
  {
    id: "deviceCode",
    header: "Mã thiết bị",
    accessorKey: "deviceCode",
    size: 120,
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
    size: 150,
    cell: ({ row }) => {
      return (
        <Typography
          color="primary"
          onClick={() => onOpenHistoryDevice(row.original.id)}
          sx={{
            cursor: "pointer",
          }}
        >
          {row.original.deviceName}
        </Typography>
      );
    },
  },
  {
    id: "roomName",
    header: "Kho/Phòng",
    accessorKey: "roomName",
    size: 120,
  },
  {
    id: "schoolSubjectName",
    header: "Môn học",
    accessorKey: "schoolSubjectName",
    size: 120,
  },
  {
    id: "quantity",
    header: "SL thiết bị",
    columns: [
      {
        id: "totalQuantity",
        header: "SL",
        accessorFn: (row) => formatNumber(row.quantity, 0, 0),
        size: 75,
        meta: {
          align: "right",
        },
      },
      {
        id: "totalBroken",
        header: "Hỏng",
        accessorFn: (row) => formatNumber(row.totalBroken, 0, 0),
        size: 75,
        meta: {
          align: "right",
        },
      },
      {
        id: "totalLost",
        header: "Mất",
        accessorFn: (row) => formatNumber(row.totalLost, 0, 0),
        size: 75,
        meta: {
          align: "right",
        },
      },
      {
        id: "totalAvailable",
        header: "Còn SD",
        accessorFn: (row) => formatNumber(row.totalAvailable, 0, 0),
        size: 75,
        meta: {
          align: "right",
        },
      },
    ],
  },
  {
    id: "available",
    header: "SL dùng được",
    columns: [
      {
        id: "readyToBorrow",
        header: "Sẵn sàng mượn",
        accessorFn: (row) => formatNumber(row.totalBorrowReady, 0, 0),
        size: 75,
        meta: {
          headerSx: {
            whiteSpace: "normal",
          },
          align: "right",
        },
      },
      {
        id: "registered",
        header: "Đã đăng ký",
        accessorFn: (row) => formatNumber(row.totalRegister, 0, 0),
        size: 75,
        meta: {
          headerSx: {
            whiteSpace: "normal",
          },
          align: "right",
        },
      },
      {
        id: "borrowing",
        header: "Đang mượn",
        accessorFn: (row) => formatNumber(row.totalBorrowing, 0, 0),
        size: 75,
        meta: {
          align: "right",
        },
      },
    ],
  },
  {
    id: "deviceUnitName",
    header: "ĐVT",
    accessorKey: "deviceUnitName",
    size: 100,
  },
  {
    id: "schoolDeviceTypeName",
    header: "Loại thiết bị",
    accessorKey: "schoolDeviceTypeName",
    size: 120,
  },
  {
    id: "gradeName",
    header: "Khối lớp",
    accessorKey: "gradeName",
    size: 120,
  },
];
