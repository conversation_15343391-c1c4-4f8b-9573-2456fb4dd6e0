import {
  IFeature,
  IFeatureFilter,
} from "@/app/(main)/root/quan-ly-tinh-nang/type";
import { DataConstant } from "@/constant";
import { rootReducer } from "@/redux/reducer";
import { createSlice, PayloadAction, WithSlice } from "@reduxjs/toolkit";

export interface IInitialState {
  features: IFeature[];
  filter: IFeatureFilter;
}

const initialState: IInitialState = {
  features: [],
  filter: {
    groupUnitCode: DataConstant.DON_VI_TYPE.truong,
    applicationId: null,
  },
};
/* ------------- Selector ------------- */
const selectors = {
  features: (state: IInitialState) => state.features,
  filter: (state: IInitialState) => state.filter,
};

const reducers = {
  setFeatures: (state: IInitialState, action: PayloadAction<IFeature[]>) => {
    state.features = action.payload;
  },
  changeFilterWithKey: (
    state: IInitialState,
    action: PayloadAction<{ key: keyof IFeatureFilter; value: any }>
  ) => {
    state.filter[action.payload.key] = action.payload.value;
  },
};

const featureSlice = createSlice({
  name: "feature",
  initialState,
  reducers,
  selectors,
});

export const featureActions = featureSlice.actions;

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices extends WithSlice<typeof featureSlice> {}
}

const injectedFeatureSlice = featureSlice.injectInto(rootReducer);

export const featureSelectors = injectedFeatureSlice.selectors;
