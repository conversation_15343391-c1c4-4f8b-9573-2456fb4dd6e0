import { useEffect } from "react";

export const useResizeObserver = (
  elementRef: React.RefObject<HTMLDivElement | null>,
  onResize: () => void
) => {
  useEffect(() => {
    const el = elementRef.current;
    if (!el) return;

    const observer = new ResizeObserver(onResize);
    observer.observe(el);
    window.addEventListener("resize", onResize);
    onResize();

    return () => {
      observer.unobserve(el);
      observer.disconnect();
      window.removeEventListener("resize", onResize);
    };
  }, [elementRef, onResize]);
};
