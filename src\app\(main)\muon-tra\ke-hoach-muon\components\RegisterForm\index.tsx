import React, { memo, useCallback, useRef, useState } from "react";
import FormInfo from "./FormInfo";
import { Button, Skeleton, Stack, Tab, Tabs } from "@mui/material";
import RoomSelectedTable, {
  RoomRefProps,
} from "./table-selected/RoomSelectedTable";
import { useFormContext, useWatch } from "react-hook-form";
import { IBorrowRequestAction } from "../../borrowRequestModel";
import ChooseButton from "./choose-device-modal/ChooseButton";
import WeekDeviceSelected from "./table-selected/WeekDeviceSelected";
import DeviceSelectedTable from "./table-selected/DeviceSelectedTable";
import { PlusIcon } from "@/components/icons";
import WeekRoomSelected from "./table-selected/WeekRoomSelected";
import { BORROW_TYPE } from "@/models/eduDevice.model";

const RegisterForm = ({
  isEdit,
  defaultDelayedTab = 1,
}: {
  isEdit?: boolean;
  defaultDelayedTab?: number;
}) => {
  const roomRef = useRef<RoomRefProps>(null);
  const { control } = useFormContext<IBorrowRequestAction>();
  const borrowType = useWatch({ control, name: "borrowType" });

  const [tabValue, setTabValue] = useState(defaultDelayedTab);
  const [delayedTab, setDelayedTab] = useState(defaultDelayedTab);
  const [isPending, setIsPending] = useState(false);

  const handleChangeTab = useCallback((_: React.SyntheticEvent, value) => {
    setTabValue(value);
    setIsPending(true);
    setTimeout(() => {
      setDelayedTab(value);
      setIsPending(false);
    }, 350);
  }, []);

  return (
    <>
      <FormInfo isEdit={isEdit} />
      <Stack
        direction="row"
        justifyContent="space-between"
        mt={1}
        alignItems="center"
      >
        <Tabs value={tabValue} onChange={handleChangeTab}>
          <Tab label="Danh sách thiết bị" value={1} />
          <Tab label="Danh sách phòng" value={2} />
        </Tabs>
        {Number(borrowType) === BORROW_TYPE.longTerm && (
          <>
            {tabValue === 1 && <ChooseButton />}
            {tabValue === 2 && (
              <Button
                variant="contained"
                size="small"
                onClick={() => roomRef.current?.addRoom()}
                startIcon={<PlusIcon />}
              >
                Thêm phòng
              </Button>
            )}
          </>
        )}
      </Stack>
      <Stack flex={1} minHeight={0} position="relative" mt={1}>
        {isPending && <LoadingSkeleton />}

        {delayedTab === 1 &&
          (Number(borrowType) === BORROW_TYPE.longTerm ? (
            <DeviceSelectedTable />
          ) : (
            <WeekDeviceSelected isEdit={isEdit} />
          ))}
        {delayedTab === 2 &&
          (Number(borrowType) === BORROW_TYPE.longTerm ? (
            <RoomSelectedTable ref={roomRef} />
          ) : (
            <WeekRoomSelected isEdit={isEdit} />
          ))}
      </Stack>
    </>
  );
};

export default memo(RegisterForm);

// Loading Skeleton
const LoadingSkeleton = memo(({}) => (
  <Stack
    spacing={2}
    position="absolute"
    top={0}
    left={0}
    right={0}
    bottom={0}
    zIndex={1000}
    bgcolor="white"
    flex={1}
    minHeight={0}
    overflow="hidden"
  >
    <Skeleton variant="rectangular" sx={{ minHeight: 40 }} />
    {Array.from({ length: 20 }).map((_, i) => (
      <Skeleton key={i} variant="text" />
    ))}
  </Stack>
));
