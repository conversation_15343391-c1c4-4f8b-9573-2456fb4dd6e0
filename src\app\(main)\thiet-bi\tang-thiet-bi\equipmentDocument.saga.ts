import { PayloadAction } from "@reduxjs/toolkit";
import { call, put, select, takeLatest } from "redux-saga/effects";
import { DataResponseModel } from "@/models/response.model";
import { ApiConstant, AppConstant, EnvConstant } from "@/constant";
import {
  IDeviceTransactionItems,
  IDeviceTransaction,
  IDeviceTransactionAction,
} from "./equipmentDocument.model";
import {
  addDocumentEntryService,
  getDocumentEntryDetailService,
  updateDocumentEntryService,
  validEquipmentDocumentEntry,
} from "./equipmentDocument.service";
import {
  equipmentDocumentActions,
  selectDeviceMapList,
  selectEquipmentDocumentEntry,
} from "./equipmentDocument.slice";
import { toast } from "sonner";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import { formatDayjsWithType } from "@/utils/format.utils";
import {
  cleanIdString,
  findMissingEquipmentDocumentEntryIds,
  mapEquipmentItem,
} from "./helper";
import { getOptionId } from "@/components/common/AppAutoComplete";
import dayjs from "dayjs";
import { IEduDevice } from "@/models/eduDevice.model";
import { v4 as uuid } from "uuid";
import { MANAGE_BY } from "@/models/system.model";

function* addDocumentEntrySaga(
  action: PayloadAction<{
    data: IDeviceTransactionAction;
    onSuccess?: () => void;
  }>
) {
  try {
    toggleAppProgress(true);
    const data = action.payload.data;
    let deviceTransactionItems = yield select(selectEquipmentDocumentEntry);
    const devices = yield select(selectDeviceMapList);

    // Kiểm tra số lượng thiết bị
    if (!deviceTransactionItems.length) {
      toast.warning("Cảnh báo", {
        description: "Danh sách thiết bị không được bỏ trống",
      });
      return;
    }

    // Kiểm tra ngày nhập
    const isInvalidEntryDate = devices.some((device) => {
      if (!device.entryDate || !data.documentDate) return false;
      return dayjs(device.entryDate).isBefore(dayjs(data.documentDate));
    });

    if (isInvalidEntryDate) {
      toast.error("Cảnh báo", {
        description: "Ngày nhập thiết bị không được nhỏ hơn ngày chứng từ",
      });
      return;
    }

    deviceTransactionItems = deviceTransactionItems.map((item) => {
      return cleanIdString({
        ...item,
        devices: devices.filter((d) => d.deviceDefinitionId === item.id),
      });
    });

    const newData: IDeviceTransactionAction = {
      ...data,
      documentDate: formatDayjsWithType(
        data.documentDate,
        AppConstant.DATE_TIME_YYYYescape
      ) as unknown as Date,
      schoolBudgetCategoryId: getOptionId(data.schoolBudgetCategoryId) ?? 0,
      deviceTransactionItems,
    };

    const response: DataResponseModel<unknown> = yield call(
      addDocumentEntryService,
      newData
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      if (action.payload.onSuccess) yield call(action.payload.onSuccess);
      toast.success("Thành công", {
        description: "Thêm mới chứng từ thành công",
      });
      return;
    }

    throw response;
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

function* getDocumentEntryDetailSaga(action: PayloadAction<number>) {
  try {
    const response: DataResponseModel<IDeviceTransaction> = yield call(
      getDocumentEntryDetailService,
      action.payload
    );
    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        equipmentDocumentActions.getDocumentEntryDetailSuccess(response.data)
      );
      return;
    }
    throw response;
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(
        error,
        "Lấy thông tin chứng từ không thành công"
      ),
    });
  }
}

function* updateDocumentEntrySaga(
  action: PayloadAction<{
    data: IDeviceTransactionAction;
    defaultData: IDeviceTransaction | null;
    onSuccess?: () => void;
  }>
) {
  try {
    toggleAppProgress(true);

    const data = action.payload.data;

    const equipmentDocumentEntriesOriginal: IDeviceTransactionItems[] =
      yield select(selectEquipmentDocumentEntry);
    let deviceTransactionItems = equipmentDocumentEntriesOriginal;

    if (!deviceTransactionItems.length) {
      toast.warning("Cảnh báo", {
        description: "Danh sách thiết bị không được bỏ trống",
      });
      return;
    }
    const devices = yield select(selectDeviceMapList);

    // Kiểm tra ngày nhập
    const isInvalidEntryDate = devices.some((device) => {
      if (!device.entryDate || !data.documentDate) return false;
      return dayjs(device.entryDate).isBefore(dayjs(data.documentDate));
    });

    if (isInvalidEntryDate) {
      toast.error("Cảnh báo", {
        description: "Ngày nhập thiết bị không được nhỏ hơn ngày chứng từ",
      });
      return;
    }

    const deviceTransactionItemEdited: (number | string)[] = yield select(
      (state) => state.equipmentDocumentReducer.deviceTransactionItemEdited
    );

    deviceTransactionItems = deviceTransactionItems
      .filter(
        (item) =>
          deviceTransactionItemEdited.includes(item.id as string) ||
          typeof item.id === "string"
      )
      .map((item) => {
        return cleanIdString({
          ...item,
          devices: devices.filter((d) => d.deviceDefinitionId === item.id),
        });
      });
    const newData: IDeviceTransactionAction = {
      ...data,
      documentDate: formatDayjsWithType(
        data.documentDate,
        AppConstant.DATE_TIME_YYYYescape
      ) as unknown as Date,
      schoolBudgetCategoryId: getOptionId(data.schoolBudgetCategoryId) ?? 0,
      deviceTransactionItems,
      deleteDeviceDefinitionIds: findMissingEquipmentDocumentEntryIds(
        action.payload.defaultData?.deviceTransactionItems,
        equipmentDocumentEntriesOriginal
      ),
    };

    const response: DataResponseModel<unknown> = yield call(
      updateDocumentEntryService,
      newData
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      if (action.payload.onSuccess) yield call(action.payload.onSuccess);
      toast.success("Thành công", {
        description: "Cập nhật chứng từ thành công",
      });
      return;
    }

    throw response;
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

function* chooseDeviceSaga(action: PayloadAction<IEduDevice>) {
  try {
    toggleAppProgress(true);

    const data = action.payload;

    const isManageQuantity = data.isManageQuantity;

    const maxIndexItem = Boolean(isManageQuantity)
      ? 1
      : Number(data.maxIndexItem ?? 0) + 1;

    const totalDevices = 1;
    const totalPrices = 0;
    const dataId = uuid();

    const devices = [
      mapEquipmentItem({
        item: {
          roomId: {
            id: data.devices?.[0]?.roomId ?? null,
            label: data.devices?.[0]?.roomName ?? null,
          },
          id: uuid(),
          quantity: 1,
        } as unknown as any,
        isQuantityMode: Boolean(isManageQuantity),
        totalDevices,
        deviceUnitName: data.deviceUnitName,
        deviceCode: data.deviceCode,
        // Nếu quản lý theo số lượng, auto là 1
        maxIndexItem,
        index: 0,
        deviceId: dataId,
      }),
    ];

    const newData = {
      ...data,
      id: dataId,
      itemId: data.id as number,
      totalDevices,
      totalPrices,
      maxIndexItem,
      devices,
      manageBy: data.isManageDevice
        ? MANAGE_BY.isManageDevice
        : MANAGE_BY.isManageQuantity,
    };

    const cleanData: IDeviceTransactionItems = cleanIdString(newData);

    const deviceTransactionItems: IDeviceTransactionItems[] = yield select(
      selectEquipmentDocumentEntry
    );
    const isSameCode = deviceTransactionItems.some(
      (item) => item.deviceCode === newData.deviceCode && dataId !== item.id
    );
    if (isSameCode) {
      toast.error("Thất bại", {
        description: "Mã thiết bị đã tồn tại trong chứng từ!",
      });
      return;
    }

    const response: DataResponseModel<unknown> = yield call(
      validEquipmentDocumentEntry,
      {
        ...cleanData,
        id: data.id,
        deviceCodes: devices.map((item) => item.code) ?? [],
      }
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(equipmentDocumentActions.chooseDeviceSuccess(newData));
    } else {
      throw response;
    }
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

export function* equipmentDocumentEntrySaga() {
  yield takeLatest(
    equipmentDocumentActions.addDocumentEntry.type,
    addDocumentEntrySaga
  );
  yield takeLatest(
    equipmentDocumentActions.updateDocumentEntry.type,
    updateDocumentEntrySaga
  );
  yield takeLatest(
    equipmentDocumentActions.getDocumentEntryDetail.type,
    getDocumentEntryDetailSaga
  );
  yield takeLatest(
    equipmentDocumentActions.chooseDevice.type,
    chooseDeviceSaga
  );
}
