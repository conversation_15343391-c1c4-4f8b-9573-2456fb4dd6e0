import { selectorHistoryDevice } from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.slice";
import { ITransactionHistory } from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/type";
import { AppTable } from "@/components/common";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import { useAppSelector } from "@/redux/hook";
import { formatDayjsWithType, formatNumber } from "@/utils/format.utils";
import { ColumnDef } from "@tanstack/react-table";

const HistoryDeviceTable = () => {
  const historyDevice = useAppSelector(selectorHistoryDevice);

  return (
    <AppTable
      columns={columns}
      data={historyDevice?.transactionHistories ?? []}
      totalData={historyDevice?.transactionHistories?.length ?? 0}
      hasDefaultPagination
      {...TABLE_MODAL_FULL_HEIGHT}
    />
  );
};

export default HistoryDeviceTable;

const columns: ColumnDef<ITransactionHistory>[] = [
  {
    id: "index",
    header: "STT",
    size: 50,
    cell: ({ row }) => {
      return row.index + 1;
    },
    meta: {
      align: "center",
    },
  },
  {
    id: "documentDate",
    header: "Ngày ghi nhận",
    accessorFn: (row) => formatDayjsWithType(row.documentDate),
    size: 120,
  },
  {
    id: "transactionType",
    header: "Loại biến động",
    accessorKey: "transactionTypeName",
    size: 120,
  },
  {
    id: "documentNumber",
    header: "Số phiếu",
    accessorKey: "documentNumber",
    size: 150,
  },
  {
    id: "quantity",
    header: "Số lượng",
    accessorFn: (row) => formatNumber(row.quantity),
    size: 120,
    meta: {
      align: "right",
    },
  },
  {
    id: "roomName",
    header: "Kho/Phòng",
    accessorKey: "roomName",
    size: 120,
  },
  {
    id: "notes",
    header: "Ghi chú",
    accessorKey: "notes",
    size: 120,
  },
];
