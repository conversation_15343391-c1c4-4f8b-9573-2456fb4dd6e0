import React, { memo } from "react";
import { IconButton } from "@mui/material";
import FixIcon from "@/components/icons/FIxIcon";
import { ILostDamageDevice } from "../lostDamgeDevice.model";

interface FixIconCellProps {
  data: ILostDamageDevice;
  onFixClick: (data: ILostDamageDevice) => void;
}

const FixIconCell = memo(({ data, onFixClick }: FixIconCellProps) => {
  return (
    <IconButton
      onClick={() => onFixClick(data)}
      sx={{ fontSize: 20, color: "text.primary" }}
    >
      <FixIcon />
    </IconButton>
  );
});

FixIconCell.displayName = "FixIconCell";

export default FixIconCell;
