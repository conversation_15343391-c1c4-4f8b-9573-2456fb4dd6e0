"use client";

import { AppSearchDebounceTextFiled, AppTable } from "@/components/common";
import { PlusIcon } from "@/components/icons";
import { AppConstant } from "@/constant";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import { IEduDevice } from "@/models/eduDevice.model";
import {
  eduDeviceActions,
  selectDeviceOriginalList,
} from "@/redux/device/eduDevice.slice";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { debounce } from "@/utils/common.utils";
import { formatNumber } from "@/utils/format.utils";
import { IconButton, Stack, Typography } from "@mui/material";
import { ColumnDef } from "@tanstack/react-table";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";

const DeviceTable = ({ onSelected, fields }) => {
  const dispatch = useAppDispatch();
  const devices = useAppSelector(selectDeviceOriginalList);

  const [pagination, setPagination] = useState(
    AppConstant.DEFAULT_PAGINATION_SKIP_TAKE
  );
  const [filter, setFilter] = useState({ searchKey: "" });
  const [totalData, setTotalData] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const columns = useMemo(() => getColumn(onSelected), [onSelected]);
  const deviceView = useMemo(() => {
    const idArr: number[] = fields.map((item) => item.idOriginal);

    return devices.filter((item) => !idArr.includes(item.id as number));
  }, [devices, fields]);

  const handleNextData = useCallback(
    debounce((el: HTMLDivElement) => {
      const isBottom =
        Math.ceil(el.scrollTop + el.clientHeight) >= el.scrollHeight - 100;

      if (isBottom && devices.length < totalData && hasMore) {
        setPagination((prev) => ({
          ...prev,
          skip: prev.skip + prev.take,
        }));
        return;
      } else if (isBottom) {
        setHasMore(false);
      }
    }, 100),
    [devices.length, totalData, hasMore]
  );

  const tableContainerProps = useMemo(() => {
    return {
      onScroll: (e) => {
        handleNextData(e.currentTarget);
      },
      sx: {
        height: "100%",
      },
    };
  }, [handleNextData]);

  useEffect(() => {
    dispatch(
      eduDeviceActions.getDeviceOriginal({
        params: { ...pagination, ...filter },
        onTotalChange: setTotalData,
        isReset: pagination.skip === 0,
      })
    );
  }, [pagination.skip, pagination.take, filter?.searchKey]);

  useEffect(() => {
    return () => {
      dispatch(eduDeviceActions.resetDeviceOriginal());
    };
  }, []);

  return (
    <Stack height="100%">
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        mb={1}
      >
        <Typography
          whiteSpace="nowrap"
          color="primary"
          fontWeight={500}
        >{`Danh sách thiết bị (${totalData})`}</Typography>
        <AppSearchDebounceTextFiled
          label="Tìm kiếm"
          boxProps={{
            sx: {
              width: 250,
            },
          }}
          onChangeValue={(value) => {
            setFilter((pre) => ({ ...pre, searchKey: value }));
            setPagination(AppConstant.DEFAULT_PAGINATION_SKIP_TAKE);
          }}
        />
      </Stack>
      <AppTable
        columns={columns}
        data={deviceView}
        totalData={totalData}
        {...TABLE_MODAL_FULL_HEIGHT}
        tableContainerProps={tableContainerProps}
        columnPinning={{
          left: ["index"],
          right: ["add"],
        }}
      />
    </Stack>
  );
};

export default memo(DeviceTable);

const getColumn = (onSelected): ColumnDef<IEduDevice>[] => [
  {
    id: "index",
    header: "STT",
    size: 50,
    cell: ({ row }) => row.index + 1,
    meta: {
      align: "center",
    },
  },
  {
    header: "Mã thiết bị",
    accessorKey: "code",
    meta: {
      cellSx: {
        whiteSpace: "nowrap",
      },
    },
    size: 50,
  },
  {
    header: "Tên thiết bị",
    accessorKey: "deviceName",
    size: 150,
  },
  {
    header: "Môn học",
    accessorKey: "schoolSubjectName",
    size: 80,
  },
  {
    header: "Khối lớp",
    accessorKey: "gradeName",
    size: 80,
  },
  {
    header: "SL",
    accessorFn: (row) => formatNumber(row.totalBorrowReady),
    size: 50,
    meta: {
      align: "right",
    },
  },
  {
    header: "ĐVT",
    accessorKey: "deviceUnitName",
    size: 50,
  },
  {
    id: "add",
    header: "",
    size: 40,
    meta: {
      align: "center",
      cellSx: {
        px: 0,
      },
    },
    cell: ({ row }) =>
      Boolean(row.original.totalBorrowReady) && (
        <IconButton
          sx={{
            p: 0,
          }}
          onClick={() =>
            onSelected(
              {
                ...row.original,
                totalBorrow: 1,
                idOriginal: row.original.id,
              },
              { shouldFocus: false }
            )
          }
        >
          <PlusIcon />
        </IconButton>
      ),
  },
];
