"use client";

import React, {
  useMemo,
  memo,
  useCallback,
  useState,
  useRef,
  useEffect,
} from "react";
import dayjs from "dayjs";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import { <PERSON><PERSON>, <PERSON>ack, Typography } from "@mui/material";
import { useFormContext, useWatch, useFieldArray } from "react-hook-form";
import { AppFormTextField, AppTable } from "@/components/common";
import { IBorrowRequestAction } from "../../../borrowRequestModel";
import { ColumnDef } from "@tanstack/react-table";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import DeleteCell from "@/components/common/table/cell/DeleteCell";
import { PlusIcon } from "@/components/icons";
import { v4 as uuid } from "uuid";
import {
  RoomSelectEditForm,
  SubjectSelectEditForm,
} from "./DeviceSelectedTable";
import ChooseDeviceColCell from "./ChooseDeviceColCell";
import ChooseButton from "../choose-device-modal/ChooseButton";
import { BORROW_TYPE, BorrowStatusEnum } from "@/models/eduDevice.model";
import { ArrowDropDownIcon } from "@mui/x-date-pickers";
import { selectHasBorrowRequestOfTeacher } from "@/redux/device/borrowRequest.slice";
import { useAppSelector } from "@/redux/hook";

dayjs.extend(isSameOrBefore);
dayjs.locale("vi");

// Component
const WeekDeviceSelected = ({ isEdit }) => {
  const { control } = useFormContext<IBorrowRequestAction>();

  const schoolWeek = useWatch({
    control,
    name: "schoolWeekConfigId",
  });

  const { fields, append, remove, update } = useFieldArray({
    control,
    name: "borrowRequestDevices",
  });

  const handleAddDevice = useCallback(
    (date: Date) => {
      append({
        id: uuid(),
        deviceId: 0,
        deviceCode: "",
        deviceName: "",
        subjectId: 0,
        roomId: 0,
        fromDate: date,
        toDate: date,
        quantity: 1,
        status: BorrowStatusEnum.Register,
        roomDeviceGuid: null,
        borrowType: BORROW_TYPE.week,
        totalBorrowReady: 1,
      });
    },
    [append]
  );

  const handleDelete = useCallback(
    (index: number) => {
      remove(index);
    },
    [remove]
  );

  const treeData = useMemo(() => {
    if (!schoolWeek?.fromDate || !schoolWeek?.toDate) return [];

    const from = dayjs(schoolWeek.fromDate);
    const to = dayjs(schoolWeek.toDate);
    const days: WeeklyBorrowDay<DeviceBorrowItem>[] = [];

    let curr = from;

    while (curr.isSameOrBefore(to, "day")) {
      const date = curr.toDate();
      const id = curr.format("YYYY-MM-DD");

      const dayItems = fields
        .map((f, index) => ({
          ...f,
          _index: index,
          subjectId: extractId(f.subjectId),
          roomId: extractId(f.roomId),
        }))
        .filter((f) => dayjs(f.fromDate).isSame(date, "day"));

      const children =
        dayItems.length > 0
          ? dayItems.map((f) => ({
              ...f,
              id: uuid(),
              parentId: id,
            }))
          : [
              {
                id: uuid(),
                parentId: id,
                deviceId: 0,
                deviceCode: "",
                deviceName: "",
                subjectId: 0,
                roomId: 0,
                fromDate: date,
                toDate: date,
                quantity: 1,
                status: BorrowStatusEnum.Register,
                roomDeviceGuid: null,
                gradeCode: "",
                borrowType: BORROW_TYPE.week,
                totalBorrowReady: 1,
                _index: -1,
              },
            ];

      days.push({
        id,
        parentId: 0,
        date,
        name: capitalizeFirstLetter(curr.format("dddd (DD/MM)")),
        children,
      });

      curr = curr.add(1, "day");
    }

    return days;
  }, [schoolWeek, fields]);

  const expandedState = useMemo(() => {
    return treeData.reduce((acc, row) => {
      const hasRealChildren = row.children?.some((r) => r._index !== -1);
      if (hasRealChildren) acc[row.id] = true;
      return acc;
    }, {});
  }, [treeData]);
  const isShowCollapse = useAppSelector(selectHasBorrowRequestOfTeacher);

  const [expanded, setExpanded] = useState({});

  useEffect(() => {
    if (!isEdit && !isShowCollapse) return;

    setExpanded(expandedState);
  }, [expandedState, isEdit, isShowCollapse]);

  const columns = useMemo(
    () => getColumns(handleAddDevice, handleDelete, update, append, expanded),
    [handleAddDevice, handleDelete, update, append, expanded]
  );

  return (
    <AppTable
      columns={columns}
      data={treeData}
      totalData={treeData.length}
      options={{
        state: { expanded },
        onExpandedChange: setExpanded,
        getSubRows: (row) =>
          row.children as unknown as
            | WeeklyBorrowDay<DeviceBorrowItem>[]
            | undefined,
      }}
      {...TABLE_MODAL_FULL_HEIGHT}
    />
  );
};

type DeviceBorrowItem = {
  id: string;
  parentId: string | number;
  deviceId: number;
  deviceCode: string;
  deviceName: string;
  subjectId?: number;
  subjectName?: string;
  roomId?: number;
  roomName?: string;
  fromDate: Date;
  toDate: Date;
  quantity: number;
  roomDeviceGuid?: string | null;
  gradeCode?: string;
  _index: number;
};

export type WeeklyBorrowDay<T> = {
  id: string;
  parentId: number;
  date: Date;
  name: string;
  children: T[];
};

export const capitalizeFirstLetter = (str: string) =>
  str.charAt(0).toUpperCase() + str.slice(1);

export const extractId = (value: any) =>
  typeof value === "object" && value !== null ? value.id : value;

export default memo(WeekDeviceSelected);

const getColumns = (
  onAdd: (date: Date) => void,
  onDelete: (index: number) => void,
  update,
  append,
  expanded
): ColumnDef<any>[] => [
  {
    id: "delete",
    size: 50,
    meta: {
      align: "center",
    },
    cell: ({ row }) =>
      row.depth === 1 ? (
        <DeleteCell
          disabled={row.original.status !== BorrowStatusEnum.Register}
          onClick={() => onDelete(row.original._index)}
        />
      ) : null,
  },
  {
    id: "deviceName",
    header: "Tên - Mã thiết bị",
    accessorKey: "deviceName",
    size: 500,
    cell: ({ row }) => (
      <DeviceNameCell
        onAdd={onAdd}
        update={update}
        row={row}
        append={append}
        isExpanded={expanded?.[row.id]}
      />
    ),
    meta: {
      colSpanOnParentRow: 8,
    },
  },
  {
    id: "quantity",
    header: "SL Đăng ký",
    accessorKey: "quantity",
    size: 150,
    cell: ({ row }) =>
      row.depth !== 1 ? null : (
        <QuantityCell
          disabled={row.original.status !== BorrowStatusEnum.Register}
          index={row.original._index}
          defaultValue={row.original.quantity}
          max={row.original.totalBorrowReady}
        />
      ),
  },
  {
    id: "unit",
    header: "Đơn vị tính",
    accessorKey: "unit",
    size: 50,
    cell: ({ row }) => (row.depth === 1 ? row.original.deviceUnitName : null),
  },
  {
    size: 250,
    id: "room",
    header: "Kho/Phòng",
    accessorKey: "roomId",
    cell: ({ row }) =>
      row.depth === 1 ? (
        <RoomSelectEditForm
          disabled={row.original.status !== BorrowStatusEnum.Register}
          rowIndex={row.original._index}
        />
      ) : null,
  },
  {
    id: "class",
    header: "Khối lớp",
    accessorKey: "gradeName",
    size: 100,
  },
  {
    id: "sj",
    header: "Môn học",
    cell: ({ row }) =>
      row.depth === 1 ? (
        <SubjectSelectEditForm
          disabled={row.original.status !== BorrowStatusEnum.Register}
          rowIndex={row.original._index}
        />
      ) : null,
    size: 250,
  },
  {
    id: "empty",
    accessorKey: "empty",
    header: "",
    size: 1,
    meta: {
      headerSx: {
        width: "100%",
      },
    },
  },
];

const QuantityCell = memo(
  ({
    index,
    defaultValue,
    disabled,
    max,
  }: {
    index: number;
    defaultValue: any;
    disabled: boolean;
    max?: number;
  }) => {
    const { control } = useFormContext();

    return (
      <AppFormTextField
        name={`borrowRequestDevices.${index}.quantity`}
        control={control}
        defaultValue={defaultValue}
        textfieldProps={{
          disabled,
          type: "number",
          inputProps: { min: 1, max },
        }}
      />
    );
  }
);

const DeviceNameCell = ({ row, onAdd, update, append }: any) => {
  const [_, forceRender] = useState({});

  const toggleExpand = () => {
    row.toggleExpanded();
    forceRender({});
  };

  if (row.depth !== 0) {
    return (
      <ChooseDeviceColCell
        append={append}
        disabled={row.original.status !== BorrowStatusEnum.Register}
        update={update}
        row={row}
      />
    );
  }

  return (
    <Stack
      onClick={toggleExpand}
      sx={{
        cursor: "pointer",
        borderLeft: "4px solid",
        borderColor: "primary.main",
        pl: 0.5,
      }}
      direction="row"
      alignItems="center"
      spacing={2}
    >
      <ArrowDropDownIcon
        sx={{
          color: "primary.main",
          transform: row.getIsExpanded() ? "rotate(180deg)" : "rotate(0)",
          transition: "transform 0.2s ease",
        }}
      />
      <Typography sx={{ width: 200, fontWeight: 500 }}>
        {row.original.name}
        <Typography color="primary" component="span" fontWeight={500}>
          {" "}
          (
          {row.original.children.filter((item) => item._index !== -1).length ??
            0}
          )
        </Typography>
      </Typography>
      {row.getIsExpanded() && (
        <>
          <ChooseButton
            onSuccess={() => {
              if (!row.getIsExpanded()) {
                row.toggleExpanded(true);
                forceRender({});
              }
            }}
            variant="outlined"
            color="secondary"
            initDate={row.original.id}
          >
            Thêm nhiều thiết bị
          </ChooseButton>

          <Button
            variant="outlined"
            color="secondary"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              if (!row.getIsExpanded()) {
                row.toggleExpanded(true);
                forceRender({});
              }
              onAdd(row.original.date);
            }}
            startIcon={<PlusIcon />}
          >
            Thêm thiết bị
          </Button>
        </>
      )}
    </Stack>
  );
};
