"use client";

import React, { memo, useCallback } from "react";
import { useFormContext } from "react-hook-form";
import { Grid } from "@mui/material";
import { AppFormTextField, GridFormContainer } from "@/components/common";
import AppFormDatePicker from "@/components/common/form/AppFormDatePicker";
import DeviceSelector from "./DeviceSelector";

interface CreateFormProps {}

const CreateForm = ({}: CreateFormProps) => {
  const {
    control,
    setValue,
    formState: { errors },
  } = useFormContext();

  const handleNumberFieldChange = useCallback(
    (event: any) => {
      const value = event.target.value;
      if (value === "" || value === null || value === undefined) {
        setValue(event.target.name, 0);
      }
    },
    [setValue]
  );

  return (
    <GridFormContainer>
      <Grid container spacing={2}>
        <Grid size={12}>
          <DeviceSelector />
        </Grid>

        <Grid size={12}>
          <AppFormTextField
            control={control}
            name="roomName"
            label="Phòng"
            textfieldProps={{
              disabled: true,
            }}
          />
        </Grid>

        <Grid size={12}>
          <AppFormDatePicker
            control={control}
            name="reportedDate"
            label="Thời gian"
            rules={{
              required: "Thời gian không được để trống",
            }}
            datePickerProps={{
              maxDate: null,
            }}
          />
        </Grid>

        <Grid size={6}>
          <AppFormTextField
            control={control}
            name="totalBroken"
            label="Số lượng hỏng"
            textfieldProps={{
              type: "number",
              inputProps: { min: 0 },
              onBlur: handleNumberFieldChange,
            }}
          />
        </Grid>

        <Grid size={6}>
          <AppFormTextField
            control={control}
            name="totalLost"
            label="Số lượng mất"
            textfieldProps={{
              type: "number",
              inputProps: { min: 0 },
              onBlur: handleNumberFieldChange,
            }}
          />
        </Grid>

        <Grid size={12}>
          <AppFormTextField
            control={control}
            name="notes"
            label="Ghi chú"
            textfieldProps={{
              multiline: true,
              minRows: 3,
              error: !!errors.notes,
              helperText: errors.notes?.message as string,
              placeholder: "Nhập ghi chú",
            }}
            rules={{
              maxLength: {
                value: 500,
                message: "Ghi chú không được dài quá 500 ký tự",
              },
            }}
          />
        </Grid>
      </Grid>
    </GridFormContainer>
  );
};

export default memo(CreateForm);
