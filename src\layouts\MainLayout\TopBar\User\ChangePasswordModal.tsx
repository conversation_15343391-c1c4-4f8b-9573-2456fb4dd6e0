"use client";

import { AppFormTextField, AppModal } from "@/components/common";
import { AppModalProps } from "@/components/common/modal/AppModal";
import { appActions } from "@/redux/app.slice";
import { useAppDispatch } from "@/redux/hook";
import { <PERSON><PERSON>, Stack } from "@mui/material";
import { useCallback } from "react";
import { useForm } from "react-hook-form";

interface ChangePasswordFormData {
  password: string;
  newPassword: string;
  confirmPassword: string;
}

interface ChangePasswordModalProps
  extends Omit<
    AppModalProps,
    "modalTitleProps" | "modalContentProps" | "modalActionsProps" | "onSubmit"
  > {
  isOpen: boolean;
  onClose: () => void;
  onSubmit?: (data: ChangePasswordFormData) => void;
}

const ChangePasswordModal = ({
  isOpen,
  onClose,
  onSubmit,
  ...otherProps
}: ChangePasswordModalProps) => {
  const {
    control,
    reset,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<ChangePasswordFormData>({
    defaultValues: {
      password: "",
      newPassword: "",
      confirmPassword: "",
    },
  });
  const dispatch = useAppDispatch();

  const newPassword = watch("newPassword");

  const handleCloseModal = useCallback(() => {
    onClose();
    reset();
  }, [onClose, reset]);

  const handleSubmitData = useCallback(
    (values: ChangePasswordFormData) => {
      const { password, newPassword, confirmPassword } = values;
      dispatch(
        appActions.changePassword({
          password,
          newPassword,
          confirmPassword,
          onSuccess: () => {
            handleCloseModal();
            if (onSubmit) {
              onSubmit(values);
            }
          },
        })
      );
    },
    [dispatch, handleCloseModal, onSubmit]
  );

  return (
    <AppModal
      component="form"
      onSubmit={handleSubmit(handleSubmitData)}
      isOpen={isOpen}
      onClose={handleCloseModal}
      modalTitleProps={{
        title: "Thay đổi mật khẩu",
      }}
      modalContentProps={{
        content: (
          <Stack spacing={2}>
            <AppFormTextField
              control={control}
              name="password"
              label="Mật khẩu cũ"
              rules={{
                required: "Vui lòng nhập mật khẩu cũ",
              }}
              textfieldProps={{
                type: "password",
                error: !!errors.password,
                helperText: errors.password?.message,
              }}
            />

            <AppFormTextField
              control={control}
              name="newPassword"
              label="Mật khẩu mới"
              rules={{
                required: "Vui lòng nhập mật khẩu mới",
                minLength: {
                  value: 8,
                  message: "Mật khẩu phải có ít nhất 8 ký tự",
                },
                pattern: {
                  value:
                    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
                  message:
                    "Mật khẩu phải có ít nhất 1 chữ cái viết hoa, 1 chữ cái viết thường, 1 số và 1 ký tự đặc biệt",
                },
              }}
              textfieldProps={{
                type: "password",
                error: !!errors.newPassword,
                helperText: errors.newPassword?.message,
              }}
            />

            <AppFormTextField
              control={control}
              name="confirmPassword"
              label="Xác nhận mật khẩu mới"
              rules={{
                required: "Vui lòng xác nhận mật khẩu mới",
                validate: (value) =>
                  value === newPassword || "Mật khẩu xác nhận không khớp",
              }}
              textfieldProps={{
                type: "password",
                error: !!errors.confirmPassword,
                helperText: errors.confirmPassword?.message,
              }}
            />
          </Stack>
        ),
      }}
      modalActionsProps={{
        children: (
          <>
            <Button
              variant="outlined"
              onClick={handleCloseModal}
              color="secondary"
            >
              Đóng
            </Button>
            <Button type="submit" variant="contained">
              Ghi
            </Button>
          </>
        ),
      }}
      {...otherProps}
    />
  );
};

export default ChangePasswordModal;
