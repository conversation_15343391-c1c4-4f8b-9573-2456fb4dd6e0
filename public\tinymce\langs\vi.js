tinymce.addI18n("vi",{"#":"#","Accessibility":"Kh\u1ea3 n\u0103ng ti\u1ebfp c\u1eadn","Accordion":"M\xf4-\u0111un n\u1ed9i dung m\u1edf r\u1ed9ng/thu g\u1ecdn \u0111\u01b0\u1ee3c","Accordion body...":"N\u1ed9i dung m\xf4-\u0111un n\u1ed9i dung m\u1edf r\u1ed9ng/thu g\u1ecdn \u0111\u01b0\u1ee3c...","Accordion summary...":"T\xf3m t\u1eaft m\xf4-\u0111un n\u1ed9i dung m\u1edf r\u1ed9ng/thu g\u1ecdn \u0111\u01b0\u1ee3c...","Action":"H\xe0nh \u0111\u1ed9ng","Activity":"Ho\u1ea1t \u0111\u1ed9ng","Address":"\u0110\u1ecba ch\u1ec9","Advanced":"N\xe2ng cao","Align":"Canh l\u1ec1","Align center":"C\u0103n gi\u1eefa","Align left":"Canh tr\xe1i","Align right":"C\u0103n ph\u1ea3i","Alignment":"Canh ch\u1ec9nh","Alignment {0}":"C\u0103n l\u1ec1 {0}","All":"T\u1ea5t c\u1ea3","Alternative description":"M\xf4 t\u1ea3 thay th\u1ebf (Alt)","Alternative source":"Ngu\u1ed3n thay th\u1ebf","Alternative source URL":"\u0110\u01b0\u1eddng d\u1eabn ngu\u1ed3n thay th\u1ebf","Anchor":"Neo li\xean k\u1ebft","Anchor...":"Neo...","Anchors":"Neo","Animals and Nature":"\u0110\u1ed9ng v\u1eadt v\xe0 thi\xean nhi\xean","Arrows":"M\u0169i t\xean","B":"M\xe0u xanh d\u01b0\u01a1ng","Background color":"M\xe0u n\u1ec1n","Background color {0}":"M\xe0u n\u1ec1n {0}","Black":"\u0110en","Block":"Kh\u1ed1i","Block {0}":"Kh\u1ed1i {0}","Blockquote":"\u0110o\u1ea1n tr\xedch d\u1eabn","Blocks":"Kh\u1ed1i","Blue":"Xanh d\u01b0\u01a1ng","Blue component":"Th\xe0nh ph\u1ea7n xanh","Body":"N\u1ed9i dung","Bold":"In \u0111\u1eadm","Border":"Vi\u1ec1n","Border color":"M\xe0u vi\u1ec1n","Border style":"Ki\u1ec3u vi\u1ec1n","Border width":"\u0110\u1ed9 d\xe0y vi\u1ec1n","Bottom":"D\u01b0\u1edbi","Browse files":"M\u1edf t\u1ec7p","Browse for an image":"Ch\u1ecdn m\u1ed9t h\xecnh \u1ea3nh","Browse links":"M\u1edf li\xean k\u1ebft","Bullet list":"Danh s\xe1ch d\u1ea1ng bi\u1ec3u t\u01b0\u1ee3ng","Cancel":"Hu\u1ef7 B\u1ecf","Caption":"Ch\xfa th\xedch","Cell":"\xd4","Cell padding":"Kho\u1ea3ng c\xe1ch trong \xf4","Cell properties":"Thu\u1ed9c t\xednh \xf4","Cell spacing":"Kho\u1ea3ng c\xe1ch \xf4","Cell styles":"Ki\u1ec3u d\xe1ng \xf4","Cell type":"Lo\u1ea1i \xf4","Center":"Gi\u1eefa","Characters":"Nh\xe2n v\u1eadt","Characters (no spaces)":"K\xfd t\u1ef1 (kh\xf4ng kho\u1ea3ng tr\u1ed1ng)","Circle":"H\xecnh tr\xf2n","Class":"L\u1edbp","Clear formatting":"Xo\xe1 \u0111\u1ecbnh d\u1ea1ng","Close":"\u0110\xf3ng L\u1ea1i","Code":"M\xe3","Code sample...":"M\xe3 m\u1eabu...","Code view":"Xem code","Color Picker":"B\u1ea3ng ch\u1ecdn m\xe0u","Color swatch":"M\u1eabu m\xe0u","Cols":"C\u1ed9t","Column":"C\u1ed9t","Column clipboard actions":"C\u1ed9t thao t\xe1c tr\xean khay nh\u1edb t\u1ea1m","Column group":"Gom nh\xf3m c\u1ed9t","Column header":"Ti\xeau \u0111\u1ec1 c\u1ed9t","Constrain proportions":"T\u1ef7 l\u1ec7 r\xe0ng bu\u1ed9c","Copy":"Sao ch\xe9p 2","Copy column":"Sao ch\xe9p c\u1ed9t","Copy row":"Sao ch\xe9p d\xf2ng","Could not find the specified string.":"Kh\xf4ng t\xecm th\u1ea5y chu\u1ed7i ch\u1ec9 \u0111\u1ecbnh.","Could not load emojis":"Kh\xf4ng th\u1ec3 t\u1ea3i bi\u1ec3u t\u01b0\u1ee3ng c\u1ea3m x\xfac","Count":"\u0110\u1ebfm","Currency":"Ti\u1ec1n t\u1ec7","Current window":"C\u1eeda s\u1ed5 hi\u1ec7n t\u1ea1i","Custom color":"Tu\u1ef3 ch\u1ec9nh m\xe0u","Custom...":"Tu\u1ef3 ch\u1ec9nh...","Cut":"C\u1eaft","Cut column":"C\u1eaft c\u1ed9t","Cut row":"C\u1eaft d\xf2ng","Dark Blue":"Xanh d\u01b0\u01a1ng \u0111\u1eadm","Dark Gray":"X\xe1m \u0111\u1eadm","Dark Green":"Xanh l\xe1 c\xe2y \u0111\u1eadm","Dark Orange":"Cam \u0111\u1eadm","Dark Purple":"T\xedm \u0111\u1eadm","Dark Red":"\u0110\u1ecf \u0111\u1eadm","Dark Turquoise":"Ng\u1ecdc lam t\u1ed1i","Dark Yellow":"V\xe0ng \u0111\u1eadm","Dashed":"N\xe9t \u0111\u1ee9t","Date/time":"Ng\xe0y/th\u1eddi gian","Decrease indent":"Th\u1ee5t l\xf9i d\xf2ng","Default":"M\u1eb7c \u0111\u1ecbnh","Delete accordion":"X\xf3a m\xf4-\u0111un n\u1ed9i dung m\u1edf r\u1ed9ng/thu g\u1ecdn \u0111\u01b0\u1ee3c","Delete column":"Xo\xe1 c\u1ed9t","Delete row":"Xo\xe1 d\xf2ng","Delete table":"Xo\xe1 b\u1ea3ng","Dimensions":"K\xedch th\u01b0\u1edbc","Disc":"\u0110\u0129a","Div":"Khung","Document":"T\xe0i li\u1ec7u","Dotted":"N\xe9t ch\u1ea5m","Double":"N\xe9t \u0111\xf4i","Drop an image here":"Th\u1ea3 h\xecnh \u1ea3nh v\xe0o \u0111\xe2y","Dropped file type is not supported":"Lo\u1ea1i t\u1ec7p \u0111\xe3 k\xe9o th\u1ea3 kh\xf4ng \u0111\u01b0\u1ee3c h\u1ed7 tr\u1ee3","Edit":"S\u1eeda","Embed":"Nh\xfang","Emojis":"Bi\u1ec3u t\u01b0\u1ee3ng c\u1ea3m x\xfac","Emojis...":"Bi\u1ec3u t\u01b0\u1ee3ng c\u1ea3m x\xfac...","Error":"L\u1ed7i","Error: Form submit field collision.":"L\u1ed7i: Xung \u0111\u1ed9t tr\u01b0\u1eddng trong bi\u1ec3u m\u1eabu.","Error: No form element found.":"L\u1ed7i: Kh\xf4ng t\xecm th\u1ea5y bi\u1ec3u m\u1eabu.","Extended Latin":"Latin m\u1edf r\u1ed9ng","Failed to initialize plugin: {0}":"Kh\xf4ng th\u1ec3 kh\u1edfi t\u1ea1o plugin: {0}","Failed to load plugin url: {0}":"Kh\xf4ng th\u1ec3 t\u1ea3i \u0111\u01b0\u1eddng d\u1eabn plugin: {0}","Failed to load plugin: {0} from url {1}":"Kh\xf4ng th\u1ec3 t\u1ea3i plugin: {0} t\u1eeb \u0111\u01b0\u1eddng d\u1eabn {1}","Failed to upload image: {0}":"Kh\xf4ng th\u1ec3 \u0111\u0103ng h\xecnh: {0}","File":"T\u1eadp tin","Find":"T\xecm ki\u1ebfm","Find (if searchreplace plugin activated)":"T\xecm ki\u1ebfm","Find and Replace":"T\xecm v\xe0 thay th\u1ebf","Find and replace...":"T\xecm v\xe0 thay th\u1ebf...","Find in selection":"T\xecm trong l\u1ef1a ch\u1ecdn","Find whole words only":"Ch\u1ec9 t\xecm to\xe0n b\u1ed9 t\u1eeb","Flags":"C\u1edd","Focus to contextual toolbar":"T\u1eadp trung v\xe0o thanh c\xf4ng c\u1ee5 ng\u1eef c\u1ea3nh","Focus to element path":"T\u1eadp trung v\xe0o \u0111\u01b0\u1eddng d\u1eabn ph\u1ea7n t\u1eed","Focus to menubar":"T\u1eadp trung v\xe0o tr\xecnh \u0111\u01a1n","Focus to toolbar":"T\u1eadp trung v\xe0o thanh c\xf4ng c\u1ee5","Font":"Ph\xf4ng ch\u1eef","Font size {0}":"C\u1ee1 ch\u1eef {0}","Font sizes":"K\xedch th\u01b0\u1edbc ch\u1eef","Font {0}":"Ph\xf4ng ch\u1eef {0}","Fonts":"Ph\xf4ng ch\u1eef","Food and Drink":"Th\u1ee9c \u0103n v\xe0 \u0111\u1ed3 u\u1ed1ng","Footer":"Ch\xe2n","Format":"\u0110\u1ecbnh d\u1ea1ng","Format {0}":"\u0110\u1ecbnh d\u1ea1ng {0}","Formats":"\u0110\u1ecbnh d\u1ea1ng","Fullscreen":"To\xe0n m\xe0n h\xecnh","G":"M\xe0u xanh l\xe1","General":"Chung","Gray":"X\xe1m","Green":"Xanh l\xe1","Green component":"Th\xe0nh ph\u1ea7n xanh","Groove":"3D c\xf3 x\u1ebb r\xe3nh","Handy Shortcuts":"Ph\xedm t\u1eaft th\xf4ng d\u1ee5ng","Header":"Ti\xeau \u0111\u1ec1","Header cell":"Ti\xeau \u0111\u1ec1 \xf4","Heading 1":"H1","Heading 2":"H2","Heading 3":"H3","Heading 4":"H4","Heading 5":"H5","Heading 6":"H6","Headings":"\u0110\u1ec1 m\u1ee5c","Height":"\u0110\u1ed9 Cao","Help":"Tr\u1ee3 gi\xfap","Hex color code":"M\xe3 m\xe0u hex","Hidden":"\u1ea8n","Horizontal align":"C\u0103n ngang","Horizontal line":"K\u1ebb ngang","Horizontal space":"N\u1eb1m ngang","ID":"ID","ID should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.":"ID ph\u1ea3i b\u1eaft \u0111\u1ea7u b\u1eb1ng ch\u1eef c\xe1i, theo sau l\xe0 c\xe1c ch\u1eef c\xe1i, s\u1ed1, d\u1ea5u g\u1ea1ch ngang, d\u1ea5u ch\u1ea5m, d\u1ea5u hai ch\u1ea5m ho\u1eb7c d\u1ea5u g\u1ea1ch d\u01b0\u1edbi.","Image is decorative":"H\xecnh \u1ea3nh minh ho\u1ea1","Image list":"Danh s\xe1ch h\xecnh \u1ea3nh","Image title":"Ti\xeau \u0111\u1ec1 \u1ea3nh","Image...":"H\xecnh \u1ea3nh...","ImageProxy HTTP error: Could not find Image Proxy":"L\u1ed7i HTTP ImageProxy: Kh\xf4ng th\u1ec3 t\xecm th\u1ea5y Image Proxy","ImageProxy HTTP error: Incorrect Image Proxy URL":"L\u1ed7i HTTP ImageProxy: URL proxy h\xecnh \u1ea3nh kh\xf4ng ch\xednh x\xe1c","ImageProxy HTTP error: Rejected request":"L\u1ed7i HTTP ImageProxy: Y\xeau c\u1ea7u b\u1ecb t\u1eeb ch\u1ed1i","ImageProxy HTTP error: Unknown ImageProxy error":"L\u1ed7i HTTP ImageProxy: L\u1ed7i ImageProxy kh\xf4ng x\xe1c \u0111\u1ecbnh","Increase indent":"T\u0103ng kho\u1ea3ng c\xe1ch d\xf2ng","Inline":"C\xf9ng d\xf2ng","Insert":"Ch\xe8n","Insert Template":"Th\xeam m\u1eabu","Insert accordion":"Ch\xe8n m\xf4-\u0111un n\u1ed9i dung m\u1edf r\u1ed9ng/thu g\u1ecdn \u0111\u01b0\u1ee3c","Insert column after":"Th\xeam c\u1ed9t b\xean ph\u1ea3i","Insert column before":"Th\xeam c\u1ed9t b\xean tr\xe1i","Insert date/time":"Ch\xe8n ng\xe0y/th\xe1ng","Insert image":"Ch\xe8n \u1ea3nh","Insert link (if link plugin activated)":"Ch\xe8n li\xean k\u1ebft","Insert row after":"Th\xeam d\xf2ng ph\xeda d\u01b0\u1edbi","Insert row before":"Th\xeam d\xf2ng ph\xeda tr\xean","Insert table":"Th\xeam b\u1ea3ng","Insert template...":"Th\xeam m\u1eabu...","Insert video":"Ch\xe8n video","Insert/Edit code sample":"Ch\xe8n/S\u1eeda m\xe3 m\u1eabu","Insert/edit image":"Ch\xe8n/s\u1eeda \u1ea3nh","Insert/edit link":"Ch\xe8n/s\u1eeda li\xean k\u1ebft","Insert/edit media":"Ch\xe8n/s\u1eeda \u0111a ph\u01b0\u01a1ng ti\u1ec7n","Insert/edit video":"Ch\xe8n/s\u1eeda video","Inset":"3D khung ch\xecm","Invalid hex color code: {0}":"M\xe3 m\xe0u hex kh\xf4ng h\u1ee3p l\u1ec7: {0}","Invalid input":"\u0110\u1ea7u v\xe0o kh\xf4ng h\u1ee3p l\u1ec7","Italic":"In nghi\xeang","Justify":"C\u0103n \u0111\u1ec1u hai b\xean","Keyboard Navigation":"Ph\xedm \u0111i\u1ec1u h\u01b0\u1edbng","Language":"Ng\xf4n ng\u1eef","Learn more...":"T\xecm hi\u1ec3u th\xeam...","Left":"Tr\xe1i","Left to right":"Tr\xe1i sang ph\u1ea3i","Light Blue":"Xanh d\u01b0\u01a1ng nh\u1ea1t","Light Gray":"X\xe1m nh\u1ea1t","Light Green":"Xanh l\xe1 nh\u1ea1t","Light Purple":"T\xedm nh\u1ea1t","Light Red":"\u0110\u1ecf nh\u1ea1t","Light Yellow":"V\xe0ng nh\u1ea1t","Line height":"\u0110\u1ed9 cao d\xf2ng","Link list":"Danh s\xe1ch li\xean k\u1ebft","Link...":"Li\xean k\u1ebft...","List Properties":"Thu\u1ed9c t\xednh danh s\xe1ch","List properties...":"C\xe1c thu\u1ed9c t\xednh danh s\xe1ch...","Loading emojis...":"\u0110ang t\u1ea3i bi\u1ec3u t\u01b0\u1ee3ng c\u1ea3m x\xfac...","Loading...":"\u0110ang t\u1ea3i...","Lower Alpha":"K\xfd t\u1ef1 th\u01b0\u1eddng","Lower Greek":"S\u1ed1 Hy L\u1ea1p th\u01b0\u1eddng","Lower Roman":"S\u1ed1 la m\xe3 th\u01b0\u1eddng","Match case":"Ph\xe2n bi\u1ec7t hoa/th\u01b0\u1eddng","Mathematical":"To\xe1n h\u1ecdc","Media poster (Image URL)":"\xc1p ph\xedch \u0111a ph\u01b0\u01a1ng ti\u1ec7n (\u0110\u01b0\u1eddng d\u1eabn h\xecnh \u1ea3nh)","Media...":"\u0110a ph\u01b0\u01a1ng ti\u1ec7n...","Medium Blue":"Xanh d\u01b0\u01a1ng nh\u1eb9","Medium Gray":"X\xe1m nh\u1eb9","Medium Purple":"T\xedm nh\u1eb9","Merge cells":"Tr\u1ed9n \xf4","Middle":"\u1ede gi\u1eefa","Midnight Blue":"Xanh d\u01b0\u01a1ng n\u1eeda \u0111\xeam","More...":"Th\xeam...","Name":"T\xean","Navy Blue":"Xanh n\u01b0\u1edbc bi\u1ec3n","New document":"T\u1ea1o t\xe0i li\u1ec7u m\u1edbi","New window":"C\u1eeda s\u1ed5 m\u1edbi","Next":"Sau","No":"Kh\xf4ng","No alignment":"Kh\xf4ng c\u0103n l\u1ec1","No color":"Kh\xf4ng c\xf3 m\xe0u","Nonbreaking space":"Kh\xf4ng xu\u1ed1ng h\xe0ng","None":"Kh\xf4ng","Numbered list":"Danh s\xe1ch \u0111\xe1nh s\u1ed1","OR":"HO\u1eb6C","Objects":"V\u1eadt d\u1ee5ng","Ok":"\u0110\u1ed3ng \xfd","Open help dialog":"M\u1edf h\u1ed9p tho\u1ea1i tr\u1ee3 gi\xfap","Open link":"M\u1edf li\xean k\u1ebft","Open link in...":"M\u1edf \u0111\u01b0\u1eddng d\u1eabn trong...","Open popup menu for split buttons":"M\u1edf menu b\u1eadt l\xean cho c\xe1c n\xfat t\xe1ch","Orange":"Cam","Outset":"3D khung n\u1ed5i","Page break":"Ng\u1eaft trang","Paragraph":"\u0110o\u1ea1n v\u0103n","Paste":"D\xe1n","Paste as text":"D\xe1n \u0111o\u1ea1n v\u0103n b\u1ea3n","Paste column after":"D\xe1n c\u1ed9t v\xe0o b\xean ph\u1ea3i","Paste column before":"D\xe1n c\u1ed9t v\xe0o b\xean tr\xe1i","Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.":"D\xe1n \u0111ang trong tr\u1ea1ng th\xe1i v\u0103n b\u1ea3n thu\u1ea7n. N\u1ed9i dung s\u1ebd \u0111\u01b0\u1ee3c d\xe1n d\u01b0\u1edbi d\u1ea1ng v\u0103n b\u1ea3n thu\u1ea7n, kh\xf4ng \u0111\u1ecbnh d\u1ea1ng.","Paste or type a link":"D\xe1n ho\u1eb7c nh\u1eadp m\u1ed9t li\xean k\u1ebft","Paste row after":"D\xe1n v\xe0o ph\xeda sau, d\u01b0\u1edbi","Paste row before":"D\xe1n v\xe0o ph\xeda tr\u01b0\u1edbc, tr\xean","Paste your embed code below:":"D\xe1n m\xe3 nh\xfang c\u1ee7a b\u1ea1n d\u01b0\u1edbi \u0111\xe2y:","People":"Ng\u01b0\u1eddi","Plugins":"Plugin","Plugins installed ({0}):":"Plugin \u0111\xe3 c\xe0i ({0}):","Powered by {0}":"Cung c\u1ea5p b\u1edfi {0}","Pre":"Ti\u1ec1n t\u1ed1","Preferences":"T\xf9y ch\u1ecdn","Preformatted":"\u0110\u1ecbnh d\u1ea1ng s\u1eb5n","Premium plugins:":"Plugin cao c\u1ea5p:","Press the Up and Down arrow keys to resize the editor.":"Nh\u1ea5n L\xean v\xe0 Xu\u1ed1ng \u0111\u1ec3 ch\u1ec9nh k\xedch c\u1ee1 tr\xecnh so\u1ea1n th\u1ea3o.","Press the arrow keys to resize the editor.":"Nh\u1ea5n ph\xedm m\u0169i t\xean \u0111\u1ec3 ch\u1ec9nh k\xedch c\u1ee1 tr\xecnh so\u1ea1n th\u1ea3o.","Press {0} for help":"Nh\u1ea5n {0} \u0111\u1ec3 \u0111\u01b0\u1ee3c tr\u1ee3 gi\xfap","Preview":"Xem th\u1eed","Previous":"Tr\u01b0\u1edbc","Print":"In","Print...":"In...","Purple":"T\xedm","Quotations":"Tr\xedch d\u1eabn","R":"M\xe0u \u0111\u1ecf","Range 0 to 255":"T\u1eeb 0 \u0111\u1ebfn 255","Red":"\u0110\u1ecf","Red component":"Th\xe0nh ph\u1ea7n \u0111\u1ecf","Redo":"L\xe0m l\u1ea1i","Remove":"Xo\xe1","Remove color":"B\u1ecf m\xe0u","Remove link":"H\u1ee7y li\xean k\u1ebft","Replace":"Thay th\u1ebf","Replace all":"Thay t\u1ea5t c\u1ea3","Replace with":"Thay th\u1ebf b\u1edfi","Resize":"Thay \u0111\u1ed5i k\xedch th\u01b0\u1edbc","Restore last draft":"Kh\xf4i ph\u1ee5c b\u1ea3n g\u1ea7n nh\u1ea5t","Reveal or hide additional toolbar items":"Hi\u1ec7n ho\u1eb7c \u1ea9n c\xe1c m\u1ee5c b\u1ed5 sung","Rich Text Area":"V\xf9ng v\u0103n b\u1ea3n phong ph\xfa","Rich Text Area. Press ALT-0 for help.":"V\xf9ng v\u0103n b\u1ea3n phong ph\xfa. Nh\xe2n ALT-0 \u0111\u1ec3 bi\u1ebft th\xeam.","Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help":"Rich Text Area. B\u1ea5m ALT-F9 m\u1edf tr\xecnh \u0111\u01a1n. B\u1ea5m ALT-F10 m\u1edf thanh c\xf4ng c\u1ee5. B\u1ea5m ALT-0 m\u1edf tr\u1ee3 gi\xfap","Ridge":"3D tr\xf2n n\u1ed5i","Right":"Ph\u1ea3i","Right to left":"Ph\u1ea3i sang tr\xe1i","Row":"D\xf2ng","Row clipboard actions":"H\xe0ng thao t\xe1c tr\xean khay nh\u1edb t\u1ea1m","Row group":"Gom nh\xf3m d\xf2ng","Row header":"Ti\xeau \u0111\u1ec1 h\xe0ng","Row properties":"Thu\u1ed9c t\xednh d\xf2ng","Row type":"Th\u1ec3 lo\u1ea1i d\xf2ng","Rows":"D\xf2ng","Save":"L\u01b0u","Save (if save plugin activated)":"L\u01b0u","Scope":"Quy\u1ec1n","Search":"T\xecm ki\u1ebfm","Select all":"Ch\u1ecdn t\u1ea5t c\u1ea3","Select...":"Ch\u1ecdn...","Selection":"L\u1ef1a ch\u1ecdn","Shortcut":"Ph\xedm t\u1eaft","Show blocks":"Hi\u1ec3n th\u1ecb kh\u1ed1i","Show caption":"Hi\u1ec7n ch\xfa th\xedch","Show invisible characters":"Hi\u1ec3n th\u1ecb k\xfd t\u1ef1 \u1ea9n","Size":"K\xedch th\u01b0\u1edbc","Solid":"N\xe9t li\u1ec1n m\u1ea1ch","Source":"Ngu\u1ed3n","Source code":"M\xe3 ngu\u1ed3n","Special Character":"K\xfd t\u1ef1 \u0111\u1eb7c bi\u1ec7t","Special character...":"K\xfd t\u1ef1 \u0111\u1eb7c bi\u1ec7t...","Split cell":"Chia c\u1eaft \xf4","Square":"\xd4 vu\xf4ng","Start list at number":"Danh s\xe1ch b\u1eaft \u0111\u1ea7u b\u1eb1ng s\u1ed1","Strikethrough":"G\u1ea1ch ngang ch\u1eef","Style":"Ki\u1ec3u","Subscript":"K\xfd t\u1ef1 th\u1ea5p","Superscript":"K\xfd t\u1ef1 m\u0169","Switch to or from fullscreen mode":"Chuy\u1ec3n qua ho\u1eb7c l\u1ea1i ch\u1ebf \u0111\u1ed9 to\xe0n m\xe0n h\xecnh","Symbols":"K\xfd hi\u1ec7u","System Font":"Ph\xf4ng ch\u1eef h\u1ec7 th\u1ed1ng","Table":"B\u1ea3ng","Table caption":"Ch\xfa th\xedch b\u1ea3ng","Table properties":"Thu\u1ed9c t\xednh b\u1ea3ng","Table styles":"Ki\u1ec3u d\xe1ng b\u1ea3ng","Template":"M\u1eabu","Templates":"M\u1eabu","Text":"V\u0103n b\u1ea3n","Text color":"M\xe0u v\u0103n b\u1ea3n","Text color {0}":"M\xe0u ch\u1eef {0}","Text to display":"N\u1ed9i dung hi\u1ec3n th\u1ecb","The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?":"\u0110\u1ecba ch\u1ec9 URL b\u1ea1n v\u1eeba nh\u1eadp gi\u1ed1ng nh\u01b0 m\u1ed9t \u0111\u1ecba ch\u1ec9 email. B\u1ea1n c\xf3 mu\u1ed1n th\xeam ti\u1ec1n t\u1ed1 mailto: kh\xf4ng?","The URL you entered seems to be an external link. Do you want to add the required http:// prefix?":"\u0110\u1ecba ch\u1ec9 URL b\u1ea1n v\u1eeba nh\u1eadp gi\u1ed1ng nh\u01b0 m\u1ed9t li\xean k\u1ebft. B\u1ea1n c\xf3 mu\u1ed1n th\xeam ti\u1ec1n t\u1ed1 http:// kh\xf4ng?","The URL you entered seems to be an external link. Do you want to add the required https:// prefix?":"Li\xean k\u1ebft b\u1ea1n nh\u1eadp c\xf3 v\u1ebb l\xe0 li\xean k\u1ebft b\xean ngo\xe0i. B\u1ea1n c\xf3 mu\u1ed1n b\u1eaft bu\u1ed9c th\xeam ti\u1ec1n t\u1ed1 https:// ?","Title":"Ti\xeau \u0111\u1ec1","To open the popup, press Shift+Enter":"\u0110\u1ec3 m\u1edf h\u1ed9p tho\u1ea1i, nh\u1ea5n Shift+Enter","Toggle accordion":"Chuy\u1ec3n \u0111\u1ed5i m\xf4-\u0111un n\u1ed9i dung m\u1edf r\u1ed9ng/thu g\u1ecdn \u0111\u01b0\u1ee3c","Tools":"C\xf4ng c\u1ee5","Top":"Tr\xean","Travel and Places":"Du l\u1ecbch v\xe0 \u0111\u1ecba \u0111i\u1ec3m","Turquoise":"Ng\u1ecdc lam","Underline":"G\u1ea1ch d\u01b0\u1edbi","Undo":"Ho\xe0n t\xe1c","Upload":"T\u1ea3i l\xean","Uploading image":"\u0110ang t\u1ea3i \u1ea3nh l\xean","Upper Alpha":"In hoa","Upper Roman":"S\u1ed1 la m\xe3 hoa","Url":"Url","User Defined":"\u0110\u1ecbnh ngh\u0129a b\u1edfi ng\u01b0\u1eddi d\xf9ng","Valid":"H\u1ee3p l\u1ec7","Version":"Phi\xean b\u1ea3n","Vertical align":"C\u0103n d\u1ecdc","Vertical space":"N\u1eb1m d\u1ecdc","View":"Xem","Visual aids":"Ch\u1ec9 d\u1eabn tr\u1ef1c quan","Warn":"C\u1ea3nh b\xe1o","White":"Tr\u1eafng","Width":"\u0110\u1ed9 R\u1ed9ng","Word count":"S\u1ed1 t\u1eeb","Words":"Ch\u1eef","Words: {0}":"Ch\u1eef: {0}","Yellow":"V\xe0ng","Yes":"C\xf3","You are using {0}":"B\u1ea1n \u0111ang s\u1eed d\u1ee5ng {0}","You have unsaved changes are you sure you want to navigate away?":"B\u1ea1n ch\u01b0a l\u01b0u thay \u0111\u1ed5i b\u1ea1n c\xf3 ch\u1eafc b\u1ea1n mu\u1ed1n di chuy\u1ec3n \u0111i?","Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.":"Tr\xecnh duy\u1ec7t c\u1ee7a b\u1ea1n kh\xf4ng h\u1ed7 tr\u1ee3 truy c\u1eadp truy c\u1eadp b\u1ed9 nh\u1edb \u1ea3o, vui l\xf2ng s\u1eed d\u1ee5ng c\xe1c t\u1ed5 h\u1ee3p ph\xedm Ctrl + X, C, V.","alignment":"canh l\u1ec1","austral sign":"k\xfd hi\u1ec7u austral","cedi sign":"k\xfd hi\u1ec7u cedi ","colon sign":"d\u1ea5u hai ch\u1ea5m","cruzeiro sign":"k\xfd hi\u1ec7u cruzeiro","currency sign":"k\xfd hi\u1ec7u ti\u1ec1n t\u1ec7","dollar sign":"k\xfd hi\u1ec7u \u0111\xf4 la","dong sign":"k\xfd hi\u1ec7u \u0111\u1ed3ng","drachma sign":"k\xfd hi\u1ec7u drachma","euro-currency sign":"k\xfd hi\u1ec7u euro","example":"v\xed d\u1ee5","formatting":"\u0111\u1ecbnh d\u1ea1ng","french franc sign":"k\xfd hi\u1ec7u franc Ph\xe1p","german penny symbol":"k\xfd hi\u1ec7u xu \u0110\u1ee9c","guarani sign":"k\xfd hi\u1ec7u guarani","history":"l\u1ecbch s\u1eed","hryvnia sign":"k\xfd hi\u1ec7u hryvnia","indentation":"th\u1ee5t \u0111\u1ea7u d\xf2ng","indian rupee sign":"k\xfd hi\u1ec7u rupee \u1ea5n \u0111\u1ed9","kip sign":"k\xfd hi\u1ec7u \u0111\u1ed3ng kip","lira sign":"k\xfd hi\u1ec7u lira","livre tournois sign":"k\xfd hi\u1ec7u livre tournois","manat sign":"k\xfd hi\u1ec7u manat","mill sign":"k\xfd hi\u1ec7u mill","naira sign":"k\xfd hi\u1ec7u naira","new sheqel sign":"k\xfd hi\u1ec7u new sheqel","nordic mark sign":"k\xfd hi\u1ec7u mark b\u1eafc \xe2u","peseta sign":"k\xfd hi\u1ec7u peseta","peso sign":"k\xfd hi\u1ec7u peso","ruble sign":"k\xfd hi\u1ec7u r\xfap","rupee sign":"k\xfd hi\u1ec7u rupee","spesmilo sign":"k\xfd hi\u1ec7u spesmilo","styles":"ki\u1ec3u","tenge sign":"K\xfd hi\u1ec7u tenge","tugrik sign":"k\xfd hi\u1ec7u tugrik","turkish lira sign":"k\xfd hi\u1ec7u lira th\u1ed5 nh\u0129 k\u1ef3","won sign":"k\xfd hi\u1ec7u won","yen character":"k\xfd hi\u1ec7u y\xean","yen/yuan character variant one":"k\xfd hi\u1ec7u y\xean/yuan bi\u1ebfn th\u1ec3","yuan character":"k\xfd hi\u1ec7u yuan","yuan character, in hong kong and taiwan":"k\xfd hi\u1ec7u yuan, \u1edf h\u1ed3ng k\xf4ng v\xe0 \u0111\xe0i loan","{0} characters":"{0} k\xfd t\u1ef1","{0} columns, {1} rows":"{0} c\u1ed9t, {1} h\xe0ng","{0} words":"{0} ch\u1eef"});