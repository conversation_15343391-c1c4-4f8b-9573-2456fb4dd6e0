"use client";

import React, { ReactNode, useEffect, useMemo, useRef } from "react";
import { createTableStore } from "./storeFactory";
import { TableStoreContext } from "./TableContext";
import { handleGetApiOptions } from "../helper";
import equal from "fast-deep-equal";
import { FilterConfig } from "../type";

interface TableProviderProps<T> {
  children: ReactNode;
  apiUrl?: string;
  methodFetch?: "GET" | "POST";
  filterConfig?: FilterConfig[];
  formatData?: (data: T[], currentFilter?: Record<string, any>) => T[];
  fetchAll?: boolean;
  cleanDataFormFiled?: (data: T) => any;
  onResetFilter?: (filter: FilterConfig[] | null) => void;
}

export function TableProvider<T>({
  children,
  apiUrl,
  methodFetch,
  filterConfig,
  formatData,
  fetchAll = false,
  cleanDataFormFiled,
  onResetFilter,
}: TableProviderProps<T>) {
  const currentAbortRef = useRef<(() => void) | undefined>(undefined);
  const hasFetchedOnce = useRef(false);

  const store = useMemo(
    () =>
      createTableStore<T>({
        apiUrl,
        methodFetch,
        cleanDataFormFiled,
        formatData,
        fetchAll,
        onResetFilter,
      }),
    [apiUrl, fetchAll]
  );

  useEffect(() => {
    if (!filterConfig) return; // không làm gì nếu không có filterConfig

    const state = store.getState();
    const prevFilterConfig = state.filterConfig ?? [];

    const changedFilters = getChangedFilters(prevFilterConfig, filterConfig);

    if (changedFilters.length > 0) {
      state.setFilterConfig(filterConfig);
      state.setFilter(filterConfig);
      handleGetApiOptions(changedFilters, state.setFilterOptions);
      hasFetchedOnce.current = false; // force refetch
    }

    if (!hasFetchedOnce.current) {
      currentAbortRef.current?.();
      currentAbortRef.current = state.fetchCurrentData() || undefined;
      hasFetchedOnce.current = true;
    }
  }, [filterConfig]);

  useEffect(() => {
    const state = store.getState();

    const unsubscribe = store.subscribe(
      (s: any) => [s.pagination, s.filter],
      ([pagination, filter]: any, [prevPagination, prevFilter]: any) => {
        const isPagChanged = !equal(pagination, prevPagination);
        const isFilterChanged = !equal(filter, prevFilter);

        if (isPagChanged || isFilterChanged) {
          currentAbortRef.current?.();
          currentAbortRef.current = state.fetchCurrentData() || undefined;
        }
      },
      {
        fireImmediately: false,
        equalityFn: () => false,
      }
    );

    return () => {
      unsubscribe();
      currentAbortRef?.current?.();
      store.getState().reset();
    };
  }, []);

  return (
    <TableStoreContext.Provider value={store}>
      {children}
    </TableStoreContext.Provider>
  );
}

export function getChangedFilters(
  prev: FilterConfig[],
  current: FilterConfig[]
): FilterConfig[] {
  const prevMap = new Map(prev.map((item) => [item.key, item]));

  return current.filter((currItem) => {
    const prevItem = prevMap.get(currItem.key);

    // Nếu là item mới => fetch
    if (!prevItem) return true;

    // Nếu toàn bộ object khác => fetch
    if (!equal(currItem, prevItem)) return true;

    return false;
  });
}
