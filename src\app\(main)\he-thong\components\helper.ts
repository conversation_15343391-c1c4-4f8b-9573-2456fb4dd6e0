import { IMenuItemTree } from "@/models/menu.model";

/**
 * Trả về danh sách các menu item cùng cấp với current path
 * @param menuItems - Array of menu items from Redux
 * @param currentPathname - Current route pathname
 * @returns Array of menu items cùng cấp
 */
export const transformMenuItemsToTabs = (
  menuItems: IMenuItemTree[],
  currentPathname: string
): IMenuItemTree[] => {
  const currentMenuItem = findMenuItemByPath(menuItems, currentPathname);
  if (!currentMenuItem) {
    return [];
  }
  const parentMenu = findParentMenu(menuItems, currentMenuItem);
  if (!parentMenu?.children) {
    return [currentMenuItem];
  }
  return [...parentMenu.children];
};

/**
 * Finds a menu item by its pathname
 * @param menuItems - Array of menu items to search in
 * @param pathname - Target pathname to find
 * @returns Found menu item or null
 */
export const findMenuItemByPath = (
  menuItems: IMenuItemTree[],
  pathname: string
): IMenuItemTree | null => {
  for (const item of menuItems) {
    if (item.href === pathname) {
      return item;
    }
    if (item.children && item.children.length > 0) {
      const found = findMenuItemByPath(item.children, pathname);
      if (found) return found;
    }
  }
  return null;
};

/**
 * Finds the parent menu of a target menu item
 * @param menuItems - Array of menu items to search in
 * @param targetItem - Target menu item to find parent for
 * @returns Parent menu item or null
 */
export const findParentMenu = (
  menuItems: IMenuItemTree[],
  targetItem: IMenuItemTree
): IMenuItemTree | null => {
  for (const item of menuItems) {
    if (
      item.children?.some((child: IMenuItemTree) => child.id === targetItem.id)
    ) {
      return item;
    }
    if (item.children && item.children.length > 0) {
      const found = findParentMenu(item.children, targetItem);
      if (found) return found;
    }
  }
  return null;
};
