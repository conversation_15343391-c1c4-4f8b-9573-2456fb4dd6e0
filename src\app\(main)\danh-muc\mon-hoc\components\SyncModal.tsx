"use client";
import { AppConfirmModalProps } from "@/components/common/modal/AppConfirmModal";
import { SyncIcon } from "@/components/icons";
import { Button } from "@mui/material";
import React, { memo, useState } from "react";
import dynamic from "next/dynamic";
import useAsyncSource from "../hooks/useAsyncSource";
const AppConfirmModal = dynamic(
  () => import("@/components/common/modal/AppConfirmModal"),
  {
    ssr: false,
  }
);

const SyncModal = ({
  onFetchData,
  ...otherProps
}: Omit<AppConfirmModalProps, "onConfirm" | "onClose" | "isOpen"> & {
  onFetchData: () => void;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleAsyncSource = useAsyncSource();

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleAsync = () => {
    handleAsyncSource(onFetchData);
  };
  return (
    <>
      <Button
        variant="outlined"
        color="secondary"
        onClick={() => setIsOpen(true)}
        startIcon={<SyncIcon />}
      >
        Đồng bộ dữ liệu hệ thống
      </Button>
      <AppConfirmModal
        modalTitleProps={{
          title:
            "Bạn xác nhận muốn đồng bộ danh sách môn học từ hệ thống CSDL?",
        }}
        isOpen={isOpen}
        onClose={handleClose}
        onConfirm={handleAsync}
        {...otherProps}
      />
    </>
  );
};

export default memo(SyncModal);
