import InformationForm from "@/app/(main)/thiet-bi/kiem-ke/components/InformationForm";
import InventoryTable from "@/app/(main)/thiet-bi/kiem-ke/components/InventoryTable";
import { inventoryTransactionActions } from "@/app/(main)/thiet-bi/kiem-ke/inventoryTransaction.slice";
import useInventoryActions from "@/app/(main)/thiet-bi/kiem-ke/hooks/useInventoryActions";
import {
  IInventoryTransactionAction,
  ManageTypeEnum,
} from "@/app/(main)/thiet-bi/kiem-ke/type";
import AppModal, { AppModalProps } from "@/components/common/modal/AppModal";
import { useAppDispatch } from "@/redux/hook";
import { Button } from "@mui/material";
import { FormProvider, useForm } from "react-hook-form";
import dayjs from "dayjs";

const CreateModal = ({
  onClose,
  fetchCurrentData,
  ...otherProps
}: CreateModalProps) => {
  const dispatch = useAppDispatch();
  const methods = useForm({
    defaultValues: INIT_VALUE,
  });
  const { handleSubmit, reset } = methods;
  const { handleCreateInventory } = useInventoryActions();
  const handleClose = () => {
    dispatch(inventoryTransactionActions.reset());
    onClose();
    reset();
  };

  const handleSubmitData = (data) => {
    handleCreateInventory(data, () => {
      fetchCurrentData?.();
      handleClose();
    });
  };

  return (
    <FormProvider {...methods}>
      <AppModal
        component="form"
        onClose={handleClose}
        onSubmit={handleSubmit(handleSubmitData)}
        fullScreen
        modalTitleProps={{
          title: "Thêm phiếu kiểm kê",
        }}
        modalContentProps={{
          sx: {
            display: "flex",
            flexDirection: "column",
            bgcolor: "background.grey",
            padding: "12px !important",
            gap: "12px",
          },
          content: (
            <>
              <InformationForm />
              <InventoryTable />
            </>
          ),
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                onClick={handleClose}
                variant="outlined"
                color="secondary"
              >
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
        {...otherProps}
      />
    </FormProvider>
  );
};

export default CreateModal;

type CreateModalProps = AppModalProps & {
  fetchCurrentData?: () => void;
};

const INIT_VALUE: IInventoryTransactionAction = {
  documentNumber: "",
  inventoryName: "",
  fromDate: dayjs(),
  toDate: dayjs(),
  isManageType: ManageTypeEnum.Room,
  scopeIds: [],
  notes: "",
  inventoryTransactionItems: [],
  transactionTeams: [],
  deleteInventoryTransactionItemIds: [],
};
