import { AppModal } from "@/components/common";
import { CursorClickIcon } from "@/components/icons";
import { Button } from "@mui/material";
import React, { memo, useCallback, useState } from "react";
import { useAppDispatch } from "@/redux/hook";
import { inventoryTransactionActions } from "@/app/(main)/thiet-bi/kiem-ke/inventoryTransaction.slice";
import ChooseDeviceTable from "@/app/(main)/thiet-bi/kiem-ke/components/ChooseDeviceModal/ChooseDeviceTable";

const ChooseDeviceModal = () => {
  const dispatch = useAppDispatch();
  const [isOpen, setIsOpen] = useState(false);

  const handleClose = useCallback(() => {
    dispatch(inventoryTransactionActions.resetChooseModal());
    setIsOpen(false);
  }, []);

  const handleChooseDevice = useCallback(() => {
    dispatch(inventoryTransactionActions.addChooseDevices());
    setIsOpen(false);
  }, [dispatch]);

  return (
    <>
      <Button
        startIcon={<CursorClickIcon />}
        onClick={() => setIsOpen(true)}
        variant="contained"
        size="small"
      >
        Chọn thiết bị
      </Button>
      <AppModal
        fullWidth
        maxWidth="lg"
        slotProps={{
          paper: {
            sx: {
              height: "100%",
            },
          },
        }}
        onClose={handleClose}
        isOpen={isOpen}
        modalTitleProps={{ title: "Chọn thiết bị" }}
        modalContentProps={{
          sx: { py: 1 },
          content: <ChooseDeviceTable />,
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleClose}
              >
                Đóng
              </Button>
              <Button variant="contained" onClick={handleChooseDevice}>
                Chọn
              </Button>
            </>
          ),
        }}
      />
    </>
  );
};

export default memo(ChooseDeviceModal);
