"use client";

import AppModal from "@/components/common/modal/AppModal";
import React, { memo, JSX, ReactNode } from "react";
import { FormConfig, FormFieldConfig } from "../../type";
import { Button } from "@mui/material";
import { FormProvider, useForm } from "react-hook-form";
import FromActionsModal from "./FromActionsModal";
import useActionsData from "./hooks/useActionsData";
import useDefaultFormValues from "./hooks/useDefaultFormValues";

const CreateModal = <T,>({
  isOpen,
  onClose,
  formConfig,
  createFormContent,
}: CreateModalProps<T>) => {
  const { handleCreateData } = useActionsData<T>();

  const defaultValues = useDefaultFormValues(formConfig?.createFields);

  const methods = useForm({
    defaultValues,
  });

  const { control, handleSubmit, reset, setValue } = methods;

  const handleClose = () => {
    onClose();
    reset(defaultValues);
  };

  const handleSuccess = () => {
    formConfig?.onSuccess?.();
    if (!formConfig?.enableSaveAndAddNew) {
      onClose();
    }
    reset(defaultValues);
  };

  const handleSubmitData = (data: Record<string, any>) => {
    handleCreateData(data, formConfig, handleSuccess);
  };

  return (
    <FormProvider {...methods}>
      <AppModal
        onSubmit={handleSubmit(handleSubmitData)}
        isOpen={isOpen}
        onClose={handleClose}
        modalTitleProps={{
          title: "Thêm mới",
        }}
        slotProps={{
          paper: {
            component: "form",
          },
        }}
        modalContentProps={{
          content: createFormContent ? (
            createFormContent(formConfig?.createFields)
          ) : (
            <FromActionsModal<any>
              onSetValue={setValue}
              control={control}
              formField={formConfig?.createFields}
            />
          ),
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleClose}
              >
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
              {formConfig?.enableSaveAndAddNew && (
                <Button type="submit" variant="contained">
                  Ghi và thêm
                </Button>
              )}
            </>
          ),
        }}
        {...formConfig?.modalProps}
      />
    </FormProvider>
  );
};

type CreateModalProps<T> = {
  isOpen: boolean;
  onClose: () => void;
  formConfig?: FormConfig<T>;
  createFormContent?: (formField?: FormFieldConfig<T>[]) => ReactNode;
};

export default memo(CreateModal) as <T>(
  props: CreateModalProps<T>
) => JSX.Element;
