import http from "@/api";
import { convertToPayload } from "@/app/(main)/thiet-bi/giam-thiet-bi/helper";
import {
  selectorDeviceDeleted,
  selectorDeviceInput,
  selectorDevices,
} from "@/app/(main)/thiet-bi/giam-thiet-bi/reduceDevice.slice";
import { ApiConstant, EnvConstant } from "@/constant";
import { PUT_REDUCE_DEVICE, REDUCE_DEVICE } from "@/constant/api.const";
import { DATE_TIME_YYYYescape } from "@/constant/app.const";
import { DataResponseModel } from "@/models/response.model";
import { useAppSelector } from "@/redux/hook";
import { CommonUtils } from "@/utils";
import dayjs from "dayjs";
import { toast } from "sonner";
import stringFormat from "string-format";

const useActionReduce = () => {
  const deviceInput = useAppSelector(selectorDeviceInput);
  const deviceDeleted = useAppSelector(selectorDeviceDeleted);
  const devices = useAppSelector(selectorDevices);

  const handleCreateReduce = async (data, onSuccess) => {
    const isError = deviceInput.some((input) => input.isError);

    if (isError) {
      toast.error("Thất bại", {
        description: "Vui lòng kiểm tra lại số lượng giảm cho thiết bị",
      });
      return;
    }

    try {
      const payload = convertToPayload(devices, deviceInput);
      const response: DataResponseModel<any> = await http.post(REDUCE_DEVICE, {
        ...data,
        documentDate: data.documentDate
          ? dayjs(data.documentDate).format(DATE_TIME_YYYYescape)
          : "",
        devices: payload,
        deleteDeviceTransactionItemIds: deviceDeleted,
      });
      if (response.code === ApiConstant.ERROR_CODE_OK) {
        toast.success("Thành công", {
          description: "Ghi giảm thiết bị thành công",
        });
        onSuccess?.();
      } else {
        throw response;
      }
    } catch (error) {
      EnvConstant.IS_DEV && console.log(error);
      toast.error("Thất bại!", {
        description: CommonUtils.extractErrorMessage(error),
      });
    }
  };

  const handleUpdateReduce = async (data, onSuccess) => {
    const isError = deviceInput.some((input) => input.isError);
    const idsChanged = deviceInput
      .filter((item) => item.isChange)
      .map((item) => item.id);

    if (isError) {
      toast.error("Thất bại", {
        description: "Vui lòng kiểm tra lại số lượng giảm cho thiết bị",
      });
      return;
    }

    try {
      const payload = convertToPayload(
        devices.filter((item) => idsChanged.includes(item.id)),
        deviceInput
      );
      const response: DataResponseModel<any> = await http.put(
        stringFormat(PUT_REDUCE_DEVICE, { id: data.id }),
        {
          ...data,
          documentDate: data.documentDate
            ? dayjs(data.documentDate).format(DATE_TIME_YYYYescape)
            : "",
          devices: payload,
          deleteDeviceTransactionItemIds: deviceDeleted,
        }
      );
      if (response.code === ApiConstant.ERROR_CODE_OK) {
        toast.success("Thành công", {
          description: "Cập nhật phiếu giảm thiết bị thành công",
        });
        onSuccess?.();
      } else {
        throw response;
      }
    } catch (error) {
      EnvConstant.IS_DEV && console.log(error);
      toast.error("Thất bại!", {
        description: CommonUtils.extractErrorMessage(error),
      });
    }
  };

  return { handleCreateReduce, handleUpdateReduce };
};

export default useActionReduce;
