import React, { useMemo } from "react";
import { Typography } from "@mui/material";
import AppConfirmModal from "@/components/common/modal/AppConfirmModal";
import useCancelBorrowing from "../hooks/useCancelBorrowing";
import useCancelReturned from "../hooks/useCancelReturned";
import { IReturnDeviceList, IReturnDeviceHistory } from "../returnDevice.model";

interface ConfirmModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  type: "cancel-borrowing" | "cancel-returned";
  selectedRows: IReturnDeviceList[] | IReturnDeviceHistory[];
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  open,
  onClose,
  onSuccess,
  type,
  selectedRows,
}) => {
  const { cancelBorrowingDevices } = useCancelBorrowing();
  const { cancelReturnedDevices } = useCancelReturned();

  const modalContent = useMemo(() => {
    if (type === "cancel-borrowing") {
      return {
        title: "<PERSON>ác nhận hủy ghi mượn",
        content: `Bạn có chắc chắn muốn hủy ghi mượn cho ${selectedRows.length} thiết bị đã chọn?`,
      };
    } else {
      return {
        title: "Xác nhận hủy ghi trả",
        content: `Bạn có chắc chắn muốn hủy ghi trả cho ${selectedRows.length} thiết bị đã chọn?`,
      };
    }
  }, [type, selectedRows.length]);

  const handleConfirm = async () => {
    const handleSuccess = () => {
      onSuccess?.();
      onClose();
    };

    if (type === "cancel-borrowing") {
      await cancelBorrowingDevices(
        selectedRows as IReturnDeviceList[],
        handleSuccess
      );
    } else {
      await cancelReturnedDevices(
        selectedRows as IReturnDeviceHistory[],
        handleSuccess
      );
    }
  };

  if (!open) return null;

  return (
    <AppConfirmModal
      isOpen={open}
      onClose={onClose}
      onConfirm={handleConfirm}
      modalTitleProps={{
        title: modalContent.title,
      }}
      modalContentProps={{
        content: (
          <>
            <Typography variant="body1" sx={{ mb: 2 }}>
              {modalContent.content}
            </Typography>
            {selectedRows.length > 0 && (
              <Typography variant="body1" color="text.secondary">
                Thao tác này không thể hoàn tác.
              </Typography>
            )}
          </>
        ),
      }}
      labelCancel="Hủy"
    />
  );
};

export default ConfirmModal;
