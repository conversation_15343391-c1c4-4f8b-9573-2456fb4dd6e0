import { reduceDeviceActions } from "@/app/(main)/thiet-bi/giam-thiet-bi/reduceDevice.slice";
import { ApiConstant, EnvConstant } from "@/constant";
import { IDevice, IDeviceParams } from "@/models/eduDevice.model";
import {
  DataListResponseModel,
  IPaginationModel,
} from "@/models/response.model";
import { getDeviceService } from "@/services/eduDevice.service";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import { PayloadAction } from "@reduxjs/toolkit";
import { call, put, takeLatest } from "redux-saga/effects";
import { toast } from "sonner";

function* getDeviceChooseSaga(
  action: PayloadAction<IDeviceParams & IPaginationModel>
) {
  try {
    toggleAppProgress(true);

    const response: DataListResponseModel<IDevice> = yield call(
      getDeviceService,
      action.payload
    );
    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(reduceDeviceActions.getDeviceChooseSuccess(response.data));
    } else {
      throw response;
    }
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

export function* reduceDeviceSaga() {
  yield takeLatest(
    reduceDeviceActions.getDeviceChoose.type,
    getDeviceChooseSaga
  );
}
