"use client";

import { TablePageLayout } from "@/components/common";
import {
  FilterConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import { ReloadIcon } from "@/components/icons";
import { ADMIN_ACCOUNT } from "@/constant/api.const";
import { ColumnDef } from "@tanstack/react-table";
import React, { useMemo, useRef, useState } from "react";
import { IAdminAccount, IAdminAccountParams } from "../admin.model";
import { IconButton } from "@mui/material";
import dynamic from "next/dynamic";
import { DataConstant } from "@/constant";
import FilterCustom from "./FilterCustom";

const ResetPasswordModal = dynamic(() => import("./ResetPasswordModal"), {
  ssr: false,
});

export const DivisionAdmin = () => {
  const tableRef = useRef<ITableRef>(null);
  const [data, setData] = useState<IAdminAccount | null>(null);

  const tableProps = useMemo(() => {
    const columns = getColumns(setData);
    return {
      columns,
      columnPinning: {
        right: ["restart"],
        left: ["index"],
      },
    };
  }, []);

  return (
    <>
      <TablePageLayout<IAdminAccount>
        ref={tableRef}
        visibleCol={VISIBLE_COL}
        apiUrl={ADMIN_ACCOUNT}
        tableProps={tableProps}
        filterConfig={[
          {
            key: "searchKey",
            type: "text",
            label: "Tìm kiếm",
            size: 2.4,
          },
          {
            key: "groupUnitCode",
            value: DataConstant.DON_VI_TYPE.phong,
          },
          {
            key: "doetCode",
          },
        ]}
        filterCustom={(props) => <FilterCustom props={props} />}
      />
      {Boolean(data) && (
        <ResetPasswordModal
          data={data}
          isOpen={Boolean(data)}
          onClose={() => {
            setData(null);
            tableRef?.current?.fetchCurrentData?.();
          }}
        />
      )}
    </>
  );
};

const getColumns = (
  setData: (data: IAdminAccount | null) => void
): ColumnDef<IAdminAccount>[] => [
  {
    id: "schoolName",
    header: "Thông tin đơn vị",
    accessorKey: "schoolName",
    accessorFn: (row) => `${row.schoolName} (${row.schoolCode})`,
  },
  {
    id: "userName",
    header: "Tên đăng nhập",
    accessorKey: "userName",
    size: 60,
  },
  {
    id: "password",
    accessorKey: "password",
    header: "Mật khẩu",
  },
  {
    id: "restart",
    header: "Khởi tạo",
    size: 60,
    meta: { align: "center" },
    cell: ({ row }) => (
      <IconButton aria-label="Khởi tạo" onClick={() => setData(row.original)}>
        <ReloadIcon sx={{ fontSize: 24 }} />
      </IconButton>
    ),
  },
];

const VISIBLE_COL = [
  { id: "schoolName", name: "Thông tin đơn vị" },
  { id: "userName", name: "Tên Đăng nhập" },
  { id: "password", name: "Mật khẩu" },
];
