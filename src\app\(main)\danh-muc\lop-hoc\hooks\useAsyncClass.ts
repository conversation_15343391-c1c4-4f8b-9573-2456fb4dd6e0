import http from "@/api";
import { ApiConstant } from "@/constant";
import { DataResponseModel } from "@/models/response.model";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import { toast } from "sonner";

const useAsyncClass = () => {
  const handleAsyncClass = async (onSuccess?: () => void) => {
    try {
      toggleAppProgress(true);
      const response: DataResponseModel<unknown> = await http.post(
        ApiConstant.SYNC_CLASS,
        {}
      );
      if (response.code === ApiConstant.ERROR_CODE_OK) {
        toast.success("Thành công!", {
          description: "Dữ liệu đã cập nhật thành công.",
        });
        onSuccess?.();
      } else {
        throw new Error(response?.message || "Đã có lỗi xảy ra");
      }
    } catch (error: any) {
      const description = extractErrorMessage(error);
      toast.error("Thất bại!", {
        description,
      });
    } finally {
      toggleAppProgress(false);
    }
  };

  return handleAsyncClass;
};

export default useAsyncClass;
