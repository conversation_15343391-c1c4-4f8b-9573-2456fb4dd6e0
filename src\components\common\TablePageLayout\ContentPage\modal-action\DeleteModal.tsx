import AppConfirmModal from "@/components/common/modal/AppConfirmModal";
import React, { memo } from "react";
import useActionsData from "./hooks/useActionsData";
import { useModalAction } from "../../modal-store/useModalAction";
import { useModalData, useModalType } from "../../modal-store/useModalSelector";

const DeleteModal = ({
  url,
  onSuccess,
}: {
  url?: string;
  onSuccess?: () => void;
}) => {
  const modalType = useModalType();
  const modalData = useModalData();
  const { closeModal } = useModalAction();

  const isOpenDelete = modalType === "delete";
  const { handleDeleteData } = useActionsData();

  const handleSuccess = () => {
    onSuccess?.();
    closeModal();
  };

  const handleConfirm = () => {
    handleDeleteData((modalData as any)?.id, url, handleSuccess);
  };

  return (
    <AppConfirmModal
      isOpen={isOpenDelete}
      modalTitleProps={{
        title: "Bạn chắc chắn muốn xóa bản ghi này?",
      }}
      onClose={closeModal}
      onConfirm={handleConfirm}
    />
  );
};

export default memo(DeleteModal);
