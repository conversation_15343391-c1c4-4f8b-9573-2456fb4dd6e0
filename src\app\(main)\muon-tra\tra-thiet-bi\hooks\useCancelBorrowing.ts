import { CANCEL_BORROW_DEVICE } from "@/constant/api.const";
import http from "@/api";
import { toast } from "sonner";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import { ApiConstant, EnvConstant } from "@/constant";
import { DataListResponseModel } from "@/models/response.model";
import { IReturnDeviceList } from "../returnDevice.model";

const useCancelBorrowing = () => {
  const cancelBorrowingDevices = async (
    selectedRows: IReturnDeviceList[],
    onSuccess?: () => void
  ) => {
    try {
      toggleAppProgress(true);

      const id = selectedRows.map((row) => row.id).filter(Boolean);

      if (id.length === 0) {
        toast.warning("Thất bại!", {
          description: "Không có thiết bị nào được chọn",
        });
        return;
      }

      const response: DataListResponseModel<any> = await http.put(
        CANCEL_BORROW_DEVICE,
        id
      );

      if (response.code === ApiConstant.ERROR_CODE_OK) {
        onSuccess?.();
        toast.success("Thành công!", {
          description: "Hủy ghi mượn thiết bị thành công",
        });
      } else {
        throw response;
      }
    } catch (error: any) {
      EnvConstant.IS_DEV && console.log(error);
      toast.error("Thất bại!", {
        description:
          extractErrorMessage(error) ||
          "Có lỗi xảy ra khi hủy ghi mượn thiết bị",
      });
    } finally {
      toggleAppProgress(false);
    }
  };

  return {
    cancelBorrowingDevices,
  };
};

export default useCancelBorrowing;
