import { AppModal } from "@/components/common";
import { PlusIcon } from "@/components/icons";
import { useAppDispatch } from "@/redux/hook";
import { Button } from "@mui/material";
import { useState, useCallback } from "react";
import TeamLeadContent from "./TeamLeadContent";
import { deviceLiquidationActions } from "../../deviceLiquidation.slice";

const ChooseTeamLeadModal = () => {
  const dispatch = useAppDispatch();
  const [isOpen, setIsOpen] = useState(false);

  const handleClose = useCallback(() => {
    dispatch(deviceLiquidationActions.resetSelectedTeacherModal());
    setIsOpen(false);
  }, []);

  const handleChooseDevice = useCallback(() => {
    dispatch(deviceLiquidationActions.addTeamlead());
    setIsOpen(false);
  }, []);

  return (
    <>
      <Button
        onClick={() => setIsOpen(true)}
        startIcon={<PlusIcon />}
        variant="contained"
        size="small"
      >
        Chọn thành viên
      </Button>
      <AppModal
        fullWidth
        maxWidth="lg"
        slotProps={{
          paper: {
            sx: {
              height: "100%",
            },
          },
        }}
        onClose={handleClose}
        isOpen={isOpen}
        modalTitleProps={{ title: "Chọn thành viên" }}
        modalContentProps={{
          sx: { py: 1 },
          content: <TeamLeadContent />,
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleClose}
              >
                Đóng
              </Button>
              <Button variant="contained" onClick={handleChooseDevice}>
                Chọn
              </Button>
            </>
          ),
        }}
      />
    </>
  );
};

export default ChooseTeamLeadModal;
