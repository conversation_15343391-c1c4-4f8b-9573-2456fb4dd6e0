"use client";

import AppModal, { AppModalProps } from "@/components/common/modal/AppModal";
import { Button } from "@mui/material";
import React, { memo, useEffect } from "react";
import RegisterForm from "./RegisterForm";
import { FormProvider, useForm } from "react-hook-form";
import { useAppDispatch } from "@/redux/hook";
import { systemActions } from "@/redux/system.slice";
import useActionRequest from "./hooks/useActionRequest";
import { useInitBorrowRequestForm } from "./hooks/useInitBorrowRequestForm";
import { IBorrowRequest } from "@/models/eduDevice.model";
import { defaultBorrowRequestAction } from "./CreateModal";

const EditModal = ({
  fetchCurrentData,
  modalData,
  onClose,
  ...otherProps
}: EditModalProps) => {
  const dispatch = useAppDispatch();

  const { handleRequestUpdate } = useActionRequest();

  const methods = useForm({
    defaultValues: defaultBorrowRequestAction,
  });

  const { handleSubmit, reset, setValue } = methods;
  useInitBorrowRequestForm({ modalData, setValue });

  const handleClose = () => {
    onClose();
    reset(defaultBorrowRequestAction);
  };

  const handleSubmitData = (data) => {
    handleRequestUpdate({ data, dataOrigin: modalData }, () => {
      handleClose();
      fetchCurrentData?.();
    });
  };

  useEffect(() => {
    dispatch(systemActions.getTeacherComboList());
    dispatch(systemActions.getRoomList());
    dispatch(systemActions.getSubjectList());
    dispatch(systemActions.getSchoolWeekList({}));

    dispatch(systemActions.getPeriodList());
    dispatch(systemActions.getClassList());
  }, []);

  return (
    <FormProvider {...methods}>
      <AppModal
        onClose={handleClose}
        onSubmit={handleSubmit(handleSubmitData)}
        component="form"
        modalTitleProps={{
          title: "Đăng ký mượn",
        }}
        fullScreen
        modalContentProps={{
          sx: {
            pt: 1,
            pb: 0,
            display: "flex",
            flexDirection: "column",
          },
          content: <RegisterForm isEdit={true} />,
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleClose}
              >
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
        {...otherProps}
      />
    </FormProvider>
  );
};

type EditModalProps = AppModalProps & {
  fetchCurrentData?: () => void;
  modalData: IBorrowRequest;
};

export default memo(EditModal);
