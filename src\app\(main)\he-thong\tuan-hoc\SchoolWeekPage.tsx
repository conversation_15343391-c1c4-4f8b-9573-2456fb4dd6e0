"use client";

import DeclareSchoolWeek from "@/app/(main)/he-thong/tuan-hoc/components/DeclareSchoolWeek";
import DeleteSchoolWeek from "@/app/(main)/he-thong/tuan-hoc/components/DeleteSchoolWeek";
import { schoolWeekActions } from "@/app/(main)/he-thong/tuan-hoc/schoolWeek.slice";
import { ISchoolWeek } from "@/models/system.model";
import { AppCheckbox, TablePageLayout } from "@/components/common";
import {
  FilterConfig,
  FormFieldConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import { SCHOOL_WEEK } from "@/constant/api.const";
import { SEMESTER_TYPE_LIST } from "@/constant/data.const";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { formatDayjsWithType } from "@/utils/format.utils";
import { Stack } from "@mui/material";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { useMemo, useRef, useState } from "react";

const SchoolWeekPage = () => {
  const dispatch = useAppDispatch();
  const schoolYear = useAppSelector(
    (state) => state.appReducer.schoolYearSelected?.id
  );
  const tableRef = useRef<ITableRef>(null);
  const filterConfig: FilterConfig[] = useMemo(() => {
    return [
      {
        key: "semester",
        type: "select",
        label: "Học kỳ",
        size: 2.4,
        options: SEMESTER_TYPE_LIST,
      },
      {
        key: "schoolYear",
        value: schoolYear,
      },
    ];
  }, [schoolYear]);

  const createConfig: FormFieldConfig<ISchoolWeek>[] = useMemo(
    () => [
      {
        key: "semester",
        label: "Học kỳ",
        type: "select",
        selectConfig: {
          options: SEMESTER_TYPE_LIST,
        },
        rules: {
          required: "Họ đệm không được để trống",
        },
      },
      {
        key: "weekCode",
        label: "Tuần học",
        type: "text",
        rules: {
          required: "Tuần học không được để trống",
        },
      },
      {
        key: "fromDate",
        label: "Ngày bắt đầu tuần học",
        type: "date",
        rules: {
          required: "Ngày bắt đầu tuần học không được bỏ trống",
          validate: (value) => {
            return (
              dayjs(value).day() !== 0 || "Vui lòng chọn các ngày trong tuần"
            );
          },
        },
        onChangeValue: (onSetValue, value) => {
          onSetValue("toDate", value ? dayjs(value as string).day(6) : null);
        },
        datePickerProps: {
          maxDate: null,
        },
      },
      {
        key: "toDate",
        label: "Ngày kết thúc tuần học",
        type: "date",
        rules: {
          required: "Ngày kết thúc tuần học không được bỏ trống",
          validate: (value, formValues) => {
            if (!formValues.fromDate || !value) {
              return;
            }
            const minDate = dayjs(formValues.fromDate).add(1, "day");
            const maxDate = dayjs(formValues.fromDate).day(6);
            if (value < minDate) {
              return "Ngày kết thúc tuần học phải lớn hơn ngày bắt đầu tuần học";
            }
            if (value > maxDate) {
              return "Vui lòng chọn các ngày trong tuần của ngày bắt đầu tuần học.";
            }
          },
        },
        datePickerProps: {
          maxDate: null,
        },
      },
      {
        key: "schoolYear",
        label: "Năm học",
        type: "custom",
        component: (_) => {
          return null;
        },
        defaultValue: schoolYear,
      },
    ],
    [schoolYear]
  );

  const updateConfig: FormFieldConfig<ISchoolWeek>[] = useMemo(
    () => [
      {
        key: "semester",
        label: "Học kỳ",
        type: "select",
        disabled: true,
        selectConfig: {
          options: SEMESTER_TYPE_LIST,
          disabled: true,
        },
        rules: {
          required: "Họ đệm không được để trống",
        },
      },
      {
        key: "weekCode",
        label: "Tuần học",
        type: "text",
        rules: {
          required: "Tuần học không được để trống",
        },
      },
      {
        key: "fromDate",
        label: "Ngày bắt đầu tuần học",
        type: "date",
        rules: {
          required: "Ngày bắt đầu tuần học không được bỏ trống",
          validate: (value) => {
            return (
              dayjs(value).day() !== 0 || "Vui lòng chọn các ngày trong tuần"
            );
          },
        },
        onChangeValue: (onSetValue, value) => {
          onSetValue("toDate", value ? dayjs(value as string).day(6) : null);
        },
        datePickerProps: {
          maxDate: null,
        },
      },
      {
        key: "toDate",
        label: "Ngày kết thúc tuần học",
        type: "date",
        rules: {
          required: "Ngày kết thúc tuần học không được bỏ trống",
          validate: (value, formValues) => {
            if (!formValues.fromDate || !value) {
              return;
            }
            const minDate = dayjs(formValues.fromDate).add(1, "day");
            const maxDate = dayjs(formValues.fromDate).day(6);
            if (value < minDate) {
              return "Ngày kết thúc tuần học phải lớn hơn ngày bắt đầu tuần học";
            }
            if (value > maxDate) {
              return "Vui lòng chọn các ngày trong tuần của ngày bắt đầu tuần học.";
            }
          },
        },
        datePickerProps: {
          maxDate: null,
        },
      },
      {
        key: "schoolYear",
        label: "Năm học",
        type: "custom",
        component: (_) => {
          return null;
        },
        defaultValue: schoolYear,
      },
    ],
    [schoolYear]
  );

  return (
    <TablePageLayout<ISchoolWeek>
      fetchAll
      ref={tableRef}
      apiUrl={SCHOOL_WEEK}
      tableProps={{
        onRowSelectionChange(selectedRows) {
          dispatch(
            schoolWeekActions.setRowSelected(
              selectedRows.map((item) => item.id)
            )
          );
        },
        columns: getColumns(),
      }}
      filterConfig={filterConfig}
      customActions={
        <Stack direction="row" spacing={1}>
          <DeleteSchoolWeek
            onSuccess={() => {
              tableRef.current?.fetchCurrentData?.();
              dispatch(schoolWeekActions.setRowSelected([]));
            }}
          />
          <DeclareSchoolWeek
            onFetchData={() => {
              tableRef.current?.fetchCurrentData?.();
            }}
          />
        </Stack>
      }
      actions={["create", "update", "delete", "check"]}
      formConfig={{
        deleteUrl: SCHOOL_WEEK,
        detailUrl: SCHOOL_WEEK,
        createUrl: SCHOOL_WEEK,
        updateUrl: SCHOOL_WEEK,
        createFields: createConfig,
        updateFields: updateConfig,
        modalProps: {
          maxWidth: "xs",
          fullWidth: true,
        },
      }}
    />
  );
};

export default SchoolWeekPage;

const getColumns = (): ColumnDef<ISchoolWeek>[] => [
  {
    id: "weekCode",
    header: "Tuần học",
    accessorKey: "weekCode",
    size: 100,
  },
  {
    id: "fromDate",
    header: "Ngày bắt đầu tuần học",
    accessorKey: "fromDate",
    size: 150,
    accessorFn: (row) => formatDayjsWithType(row.fromDate),
  },
  {
    id: "toDate",
    header: "Ngày kết thúc tuần học",
    accessorKey: "toDate",
    size: 150,
    accessorFn: (row) => formatDayjsWithType(row.toDate),
  },
  {
    id: "grade",
    header: "Học kỳ",
    accessorKey: "semester",
    size: 100,
  },
];
