import { RETURN_DEVICE } from "@/constant/api.const";
import http from "@/api";
import { toast } from "sonner";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import dayjs from "dayjs";
import { ApiConstant, EnvConstant } from "@/constant";
import { DataListResponseModel } from "@/models/response.model";
import { IReturnDeviceList } from "../returnDevice.model";
import { formatDayjsWithType } from "@/utils/format.utils";

const useReturnDevice = () => {
  const returnDevices = async (
    selectedRows: IReturnDeviceList[],
    onSuccess?: () => void
  ) => {
    try {
      toggleAppProgress(true);

      const returnData = selectedRows
        .filter((row) => row.id)
        .map((row) => ({
          id: row.id,
          totalLost: row.totalLost || 0,
          totalBroken: row.totalBroken || 0,
          totalConsumed: row.totalConsumed || 0,
          notes: row.notes || "",
          borrowReturnDate:
            row.borrowReturnDate || formatDayjsWithType(dayjs().toDate()),
        }));

      if (returnData.length === 0) {
        toast.warning("Thất bại!", {
          description: "Không có thiết bị nào được chọn",
        });
        return;
      }

      const response: DataListResponseModel<any> = await http.put(
        RETURN_DEVICE,
        returnData
      );

      if (response.code === ApiConstant.ERROR_CODE_OK) {
        onSuccess?.();
        toast.success("Thành công!", {
          description: "Ghi trả thiết bị thành công",
        });
      } else {
        throw response;
      }
    } catch (error: any) {
      EnvConstant.IS_DEV && console.log(error);
      toast.error("Thất bại!", {
        description:
          extractErrorMessage(error) || "Có lỗi xảy ra khi ghi trả thiết bị",
      });
    } finally {
      toggleAppProgress(false);
    }
  };

  return {
    returnDevices,
  };
};

export default useReturnDevice;
