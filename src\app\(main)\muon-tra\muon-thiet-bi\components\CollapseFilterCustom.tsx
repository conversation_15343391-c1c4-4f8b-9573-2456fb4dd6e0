import { useMemo, memo } from "react";
import { Box } from "@mui/material";
import dayjs from "dayjs";
import { BORROW_TYPE } from "@/models/eduDevice.model";
import { useAppSelector } from "@/redux/hook";
import { FilterCustomProps } from "@/components/common/TablePageLayout/ContentPage/HeaderFilter";
import { selectSchoolWeekList } from "@/redux/system.slice";
import WeekTabSelector from "@/components/sn-common/WeekTabSelector";

const CollapseFilterCustom = ({ props }: { props: FilterCustomProps }) => {
  const borrowType = useMemo(() => {
    const filterValue = props.filter?.find((item) => item.key === "borrowType")
      ?.value as string;
    return filterValue ? Number(filterValue) : BORROW_TYPE.week;
  }, [props.filter]);

  const schoolWeek = useAppSelector(selectSchoolWeekList);

  const currentWeek = useMemo(() => {
    const fromDateFilter = props.filter?.find((f) => f.key === "fromDate")
      ?.value as string;
    const toDateFilter = props.filter?.find((f) => f.key === "toDate")
      ?.value as string;

    if (fromDateFilter && toDateFilter && schoolWeek.length > 0) {
      const foundWeek = schoolWeek.find((week) => {
        const weekStart = dayjs(week.fromDate).format("YYYY-MM-DD");
        const weekEnd = dayjs(week.toDate).format("YYYY-MM-DD");
        const filterStart = dayjs(fromDateFilter).format("YYYY-MM-DD");
        const filterEnd = dayjs(toDateFilter).format("YYYY-MM-DD");

        const isInRange =
          dayjs(fromDateFilter).isBetween(weekStart, weekEnd, "day", "[]") ||
          dayjs(toDateFilter).isBetween(weekStart, weekEnd, "day", "[]") ||
          (filterStart === weekStart && filterEnd === weekEnd);

        return isInRange;
      });

      return foundWeek || schoolWeek[schoolWeek.length - 1];
    }

    return schoolWeek[schoolWeek.length - 1] || null;
  }, [props.filter, schoolWeek]);

  const currentFilterDates = useMemo(() => {
    const fromDateFilter = props.filter?.find((f) => f.key === "fromDate")
      ?.value as string;
    const toDateFilter = props.filter?.find((f) => f.key === "toDate")
      ?.value as string;

    return {
      fromDate: fromDateFilter,
      toDate: toDateFilter,
    };
  }, [props.filter]);

  const handleDateChange = (dates: { fromDate: string; toDate: string }) => {
    props.onChangeFilterObj?.(dates);
  };

  return (
    <>
      {borrowType === BORROW_TYPE.week && (
        <WeekTabSelector
          currentWeek={currentWeek}
          onDateChange={handleDateChange}
          showAllTab={true}
          allTabLabel="Tất cả"
          currentFilterDates={currentFilterDates}
        />
      )}
    </>
  );
};

export default memo(CollapseFilterCustom);
