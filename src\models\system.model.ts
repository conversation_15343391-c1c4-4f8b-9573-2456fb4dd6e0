import { DataConstant } from "@/constant";

export interface ISource {
  id: number;
  name: string;
  code: string;
  note?: string;
  status: number;
  createdBy?: number;
  updatedBy?: number;
  createdAt?: string;
  isSystem: DataConstant.BOOLEAN_TYPE;
  updatedAt?: string;
}

export interface ISourceParams {
  searchKey?: string;
  status?: number;
}

export interface IDeviceType {
  id: number;
  createdBy: number;
  updatedBy: number;
  createdAt: Date;
  updatedAt: Date;
  code: string;
  name: string;
  status: DataConstant.STATUS_TYPE;
  note: string;
  isSystem: DataConstant.BOOLEAN_TYPE;
}

export interface IUnit {
  id: number;
  createdBy: number;
  updatedBy: number;
  createdAt: Date;
  updatedAt: Date;
  name: string;
}

export interface ICountry {
  id: number;
  createdBy: number;
  updatedBy: number;
  createdAt: Date;
  updatedAt: Date;
  name: string;
}

export interface IGrade {
  id: number;
  createdBy: number;
  updatedBy: number;
  createdAt: Date;
  updatedAt: Date;
  name: string;
  code: number;
}

export interface ISubject {
  code: string;
  name: string;
  doetCode: string | null;
  divisionCode: string | null;
  schoolCode: string | null;
  groupUnitCode: string | null;
  order: number;
  isSystem: number;
  status: number;
  id: number;
  createdBy: string | null;
  updatedBy: string | null;
  createdAt: string | null;
  updatedAt: string | null;
}

export interface ISearchParams {
  searchKey?: string;
  skip?: number;
  take?: number;
}

export interface IClass {
  id: number;
  code?: string;
  year: number;
  schoolYear?: string;
  gradeCode?: string;
  name?: string;
  status: DataConstant.STATUS_TYPE;
  order?: number;
  createBy?: number;
  updateBy?: number;
  createAt?: string;
  updateAt?: string;
  totalStudents?: number;
  isSystem: DataConstant.BOOLEAN_TYPE;
}

export interface IClassParams {
  year: number;
}

export interface IRoom {
  id: number;
  createdBy: number;
  updatedBy: number;
  createdAt: Date;
  updatedAt: Date;
  name: string;
  classificationType: number | null;
  isFunctionalClassroom: number;
  isInternetConnection: number | null;
  functionalClassroomTypeId: number;
  area: number | null;
  status: DataConstant.STATUS_TYPE;
  subjects?: ISubject[];
  teachers?: any[];
  teacherName: string;
  subjectName: string;
}

export interface IRoomAction extends IRoom {
  subjectIds?: number[];
  teacherIds?: number[];
}

export enum CLASSIFICATION_TYPE {
  none = 0, // Chưa đạt chuẩn
  standard = 1, // Đạt chuẩn
}

export const CLASSIFICATION_LIST = [
  {
    id: CLASSIFICATION_TYPE.none,
    label: "Chưa đạt chuẩn",
  },
  {
    id: CLASSIFICATION_TYPE.standard,
    label: "Đạt chuẩn",
  },
];

// client
export enum MANAGE_BY {
  isManageQuantity = "1",
  isManageDevice = "2",
}
export const MANAGE_BY_LIST = [
  { id: MANAGE_BY.isManageQuantity, label: "Số lượng" },
  { id: MANAGE_BY.isManageDevice, label: "Từng thiết bị" },
];

export interface ITeacher {
  id: number;

  /** Mã */
  code: string;

  /** Họ đệm */
  lastName?: string;

  /** Tên */
  firstName?: string;

  fullName: string;

  /** Ngày sinh */
  dateOfBirth?: Date;

  /** Giới tính */
  gender?: number;

  /** Số điện thoại */
  phone?: string;

  /** Email */
  email?: string;

  /** Địa chỉ */
  address?: string;

  /** Mã định danh */
  identityNumber: string;

  /** Ảnh đại diện */
  avatar?: string;

  /** Id ảnh đại diện */
  avatarAttachmentId?: number;

  /** Nhóm quyền */
  applicationFunctionCode?: string;

  /** Trạng thái */
  status: DataConstant.STATUS_TYPE;

  /** Tổ bộ môn */
  teacherGroupSubjectId?: number;

  /** Tên tổ bộ môn */
  teacherGroupSubjectName?: string;

  /** Tên đăng nhập */
  userName: string;

  /** Mật khẩu */
  password: string;
}

export interface IPeriod {
  id: number;
  createdBy: number;
  updatedBy: number;
  createdAt: Date;
  updatedAt: Date;
  session: string;
  lession: string;
}

export interface ISchoolWeek {
  id: number;
  weekCode: number;
  fromDate: string;
  toDate: string;
  grade: number;
  semester: number;
  schoolYear: number;
  weekOfYear: number;
  status: number;
}
