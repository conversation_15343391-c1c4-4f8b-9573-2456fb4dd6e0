import { IconButton, IconButtonProps } from "@mui/material";
import { DeleteIcon } from "@/components/icons";
import { memo } from "react";

const TreeDeleteButton = ({
  onClickButton,
  ...otherProps
}: ActionIconButtonProps) => {
  return (
    <IconButton
      color="primary"
      size="small"
      sx={{ fontSize: "22px", width: 24, height: 24, color: "primary.main" }}
      onClick={(e) => {
        e.stopPropagation();
        onClickButton();
      }}
      {...otherProps}
    >
      <DeleteIcon />
    </IconButton>
  );
};

export default memo(TreeDeleteButton);

type ActionIconButtonProps = IconButtonProps & {
  onClickButton: () => void;
};
