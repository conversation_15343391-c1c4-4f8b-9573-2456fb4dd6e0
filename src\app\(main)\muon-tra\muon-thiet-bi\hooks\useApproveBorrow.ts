import { useState } from "react";
import { BORROW_DEVICE_APPROVE } from "@/constant/api.const";
import http from "@/api";
import { IBorrowDeviceModal } from "../borrowDevice.modal";
import { toast } from "sonner";
import { extractErrorMessage } from "@/utils/common.utils";
import { ApiConstant, EnvConstant } from "@/constant";
import { DataListResponseModel } from "@/models/response.model";

const useApproveBorrow = () => {
  const [isLoading, setIsLoading] = useState(false);

  const approveBorrowDevices = async (
    selectedRows: IBorrowDeviceModal[],
    onSuccess?: () => void
  ) => {
    try {
      setIsLoading(true);

      const deviceIds = selectedRows.map((row) => row.id).filter(Boolean);

      if (deviceIds.length === 0) {
        toast.error("Thất bại!", {
          description: "Không có thiết bị nào đượ<PERSON> chọn",
        });
        return;
      }

      const response: DataListResponseModel<any> = await http.put(
        BORROW_DEVICE_APPROVE,
        deviceIds
      );

      if (response.code === ApiConstant.ERROR_CODE_OK) {
        onSuccess?.();
        toast.success("Thành công!", {
          description: "Ghi mượn thiết bị thành công",
        });
      } else {
        throw response;
      }
    } catch (error: any) {
      EnvConstant.IS_DEV && console.log(error);
      toast.error("Thất bại!", {
        description:
          extractErrorMessage(error) || "Có lỗi xảy ra khi ghi mượn thiết bị",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    approveBorrowDevices,
    isLoading,
  };
};

export default useApproveBorrow;
