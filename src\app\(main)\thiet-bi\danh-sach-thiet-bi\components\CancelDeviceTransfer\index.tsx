"use client";

import CancelTransferTable from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/components/CancelDeviceTransfer/CancelTransferTable";
import {
  deviceActions,
  selectorCancelIds,
} from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.slice";
import useTransferDevice from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/hooks/useTransferDevice";
import { AppModal } from "@/components/common";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { Button } from "@mui/material";
import { useCallback } from "react";
import { toast } from "sonner";

const CancelDeviceTransfer = ({
  open,
  onClose,
  fetchCurrentData,
}: {
  open: boolean;
  onClose: () => void;
  fetchCurrentData?: () => void;
}) => {
  const dispatch = useAppDispatch();
  const { handleCancelTransferDevice } = useTransferDevice();
  const cancelIds = useAppSelector(selectorCancelIds);

  const handleClose = useCallback(() => {
    dispatch(deviceActions.resetCancelIds());
    onClose();
  }, [onClose]);

  const handleCancelTransfer = useCallback(() => {
    if (cancelIds.length === 0) {
      toast.warning("Chưa chọn thiết bị cần hủy điều chuyển!", {
        description: "Vui lòng chọn thiết bị cần hủy điều chuyển.",
      });
      return;
    }
    handleCancelTransferDevice(cancelIds, () => {
      handleClose();
      fetchCurrentData?.();
    });
  }, [cancelIds, fetchCurrentData, handleCancelTransferDevice, handleClose]);

  return (
    <AppModal
      isOpen={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      sx={{
        "& .MuiDialogContent-root": {
          px: 0,
        },
      }}
      modalTitleProps={{
        title: "Hủy điều chuyển thiết bị",
      }}
      modalContentProps={{
        content: <CancelTransferTable />,
      }}
      modalActionsProps={{
        children: (
          <>
            <Button variant="outlined" color="secondary" onClick={handleClose}>
              Đóng
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleCancelTransfer}
            >
              Xác nhận
            </Button>
          </>
        ),
      }}
    />
  );
};

export default CancelDeviceTransfer;
