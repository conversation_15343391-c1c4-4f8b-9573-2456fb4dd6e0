import { IOption } from "@/components/common";
import { DataConstant } from "@/constant";
import { IEduDevice } from "@/models/eduDevice.model";
import { MANAGE_BY } from "@/models/system.model";
import { Dayjs } from "dayjs";

export interface IDeviceTransaction {
  id: number;
  documentNumber: string;
  documentDate: Date;
  schoolBudgetCategoryId: number;
  schoolBudgetCategoryName: string | null;
  totalDevices: number;
  totalPrices: number;
  notes: string;
  deviceTransactionItems: IDeviceTransactionItems[];
  createdBy: number;
  updatedBy: number | null;
  createdAt: string;
  updatedAt: string | null;
}

export interface IDevices {
  id?: number | string;
  deviceId?: number;
  code: string;
  deviceDefinitionId?: number | string;

  roomId: number | null;
  teacherName?: string;
  quantity: number;
  price: number;
  countryId: number | null;
  countryName?: string;
  entryDate: Date | null;
  serial: string;
  expireDate: Date | null;
  deviceTransactionItemId: number;

  documentEntryId?: number;
  codeIndex?: number;

  deviceName?: string;
  deviceCode?: string;
  roomName?: string;
  deviceUnitName?: string;
  totalPrices?: number;
  totalAvailable?: number;

  // Id gốc api trả về thêm ở client
  itemId?: number | string;
}

export interface IDeviceTransactionAction {
  documentNumber: string;
  documentDate: Date | null;
  schoolBudgetCategoryId: number;
  notes: string;
  deviceTransactionItems: IDeviceTransactionItems[];
  deleteDeviceDefinitionIds?: number[];
  // client
  id?: string | number;
}

export interface IDeviceTransactionItems extends IEduDevice {
  totalDevices?: number;
  totalPrices?: number;

  /*Id gốc */
  itemId?: number | string;
  totalAvailable?: number;
}
/* device interface mapping ui */
export interface IDeviceMap {
  [x: string | number]: IDevices[];
}

export interface IDeviceTransactionUI {
  id?: string | number;
  statisticCode: string;
  deviceCode: string;
  deviceName: string;
  deviceUnitId: number | IOption | null;
  schoolDeviceTypeId: number | IOption | null;
  schoolSubjectId: number | IOption | null;
  gradeCodes: Array<number | IOption>;
  userTypes: Array<number | IOption>;
  manageBy: MANAGE_BY;
  isConsumable: DataConstant.BOOLEAN_TYPE;
  isSelfMade: DataConstant.BOOLEAN_TYPE;
  minimumQuantity: number;
  totalDevices: number;
  equipmentDocumentEntryId?: number;

  totalAvailable?: number;

  maxIndexItem: number;
  devices: IDevices[];

  totalAdd?: number;

  itemId?: number;

  documentDate?: Date | Dayjs | null;
  // isShowInReport?: boolean; // nếu toggle cuối cùng được sử dụng
}

export interface IDeviceRoomGroup extends IDeviceTransactionItems {
  id?: number | string;

  code: string;
  deviceDefinitionId?: number | string;
  deviceDefinitionOriginalId?: number | string;
  roomId: number | null;
  teacherName?: string;
  quantity: number;
  price: number;
  countryId: number | null;
  countryName?: string;
  entryDate: Date | null;
  serial: string;
  expireDate: Date | null;
  deviceTransactionItemId: number;
  documentEntryId?: number;
  codeIndex?: number;
  deviceName: string;
  roomName?: string;
  deviceUnitName?: string;
  totalPrices?: number;
  itemId?: number | string;
  deviceIds: (number | string)[];

  totalAvailable?: number;
}
