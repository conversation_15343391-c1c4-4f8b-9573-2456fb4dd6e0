"use client";

import { TablePageLayout } from "@/components/common";
import { FormFieldConfig } from "@/components/common/TablePageLayout/type";
import { SCHOOL_YEAR_CONFIG } from "@/constant/api.const";
import { ColumnDef } from "@tanstack/react-table";
import React, { useMemo } from "react";
import { formatDayjsWithType } from "@/utils/format.utils";
import { useSelector } from "react-redux";
import { selectSchoolYearOptions } from "@/redux/app.slice";
import { DATE_FORMAT } from "@/constant/app.const";
import { ISchoolYearConfig } from "@/models/app.model";

const YearConfigPage = () => {
  const schoolYearOptions = useSelector(selectSchoolYearOptions);

  const CREATE_CONFIG: FormFieldConfig<ISchoolYearConfig>[] = useMemo(
    () => [
      {
        key: "schoolYearId",
        type: "select",
        label: "Năm học",
        size: 12,
        rules: { required: "Năm học không được để trống" },
        selectConfig: {
          options: schoolYearOptions,
        },
      },
      {
        key: "startOfSemester1",
        type: "date",
        label: "Ngày bắt đầu học kỳ 1",
        size: 12,
        rules: {
          required: "Ngày bắt đầu học kỳ 1 không được để trống",
          validate: (value: any, formValues: any) => {
            if (!value) return true;

            // Check against semester 2
            if (formValues.startOfSemester2) {
              if (new Date(value) >= new Date(formValues.startOfSemester2)) {
                return "Ngày bắt đầu học kỳ 1 phải trước ngày bắt đầu học kỳ 2";
              }
            }

            // Check against end of year
            if (formValues.endOfSchoolYear) {
              if (new Date(value) >= new Date(formValues.endOfSchoolYear)) {
                return "Ngày bắt đầu học kỳ 1 phải trước ngày kết thúc năm học";
              }
            }

            return true;
          },
        },
        datePickerProps: {
          maxDate: null,
        },
      },
      {
        key: "startOfSemester2",
        type: "date",
        label: "Ngày bắt đầu học kỳ 2",
        size: 12,
        rules: {
          required: "Ngày bắt đầu học kỳ 2 không được để trống",
          validate: (value: any, formValues: any) => {
            if (!value) return true;

            // Check against semester 1
            if (formValues.startOfSemester1) {
              if (new Date(value) <= new Date(formValues.startOfSemester1)) {
                return "Ngày bắt đầu học kỳ 2 phải sau ngày bắt đầu học kỳ 1";
              }
            }

            // Check against end of year
            if (formValues.endOfSchoolYear) {
              if (new Date(value) >= new Date(formValues.endOfSchoolYear)) {
                return "Ngày bắt đầu học kỳ 2 phải trước ngày kết thúc năm học";
              }
            }

            return true;
          },
        },
        datePickerProps: {
          maxDate: null,
        },
      },
      {
        key: "endOfSchoolYear",
        type: "date",
        label: "Ngày kết thúc năm học",
        size: 12,
        rules: {
          validate: (value: any, formValues: any) => {
            if (!value) return true;

            // Check against semester 1
            if (formValues.startOfSemester1) {
              if (new Date(value) <= new Date(formValues.startOfSemester1)) {
                return "Ngày kết thúc năm học phải sau ngày bắt đầu học kỳ 1";
              }
            }

            // Check against semester 2
            if (formValues.startOfSemester2) {
              if (new Date(value) <= new Date(formValues.startOfSemester2)) {
                return "Ngày kết thúc năm học phải sau ngày bắt đầu học kỳ 2";
              }
            }

            return true;
          },
        },
        datePickerProps: {
          maxDate: null,
        },
      },
    ],
    [schoolYearOptions]
  );

  return (
    <TablePageLayout<ISchoolYearConfig>
      fetchAll
      visibleCol={VISIBLE_COL}
      apiUrl={SCHOOL_YEAR_CONFIG}
      tableProps={{
        columns: COLUMNS,
        hasDefaultPagination: true,
      }}
      filterConfig={[
        {
          key: "searchKey",
          type: "text",
          label: "Tìm kiếm",
          size: 2.4,
        },
      ]}
      actions={["create", "update"]}
      formConfig={{
        detailUrl: SCHOOL_YEAR_CONFIG,
        createUrl: SCHOOL_YEAR_CONFIG,
        updateUrl: SCHOOL_YEAR_CONFIG,
        createFields: CREATE_CONFIG,
        updateFields: CREATE_CONFIG,
      }}
    />
  );
};

export default YearConfigPage;

const COLUMNS: ColumnDef<ISchoolYearConfig>[] = [
  {
    id: "year",
    header: "Năm học",
    accessorKey: "year",
    size: 60,
  },
  {
    id: "startOfSemester1",
    header: "Ngày bắt đầu học kỳ 1",
    accessorKey: "startOfSemester1",
    size: 100,
    meta: { align: "center" },
    cell: ({ row }) =>
      formatDayjsWithType(row.original.startOfSemester1, DATE_FORMAT),
  },
  {
    id: "startOfSemester2",
    header: "Ngày bắt đầu học kỳ 2",
    accessorKey: "startOfSemester2",
    size: 100,
    meta: { align: "center" },
    cell: ({ row }) =>
      formatDayjsWithType(row.original.startOfSemester2, DATE_FORMAT),
  },
  {
    id: "endOfSchoolYear",
    header: "Ngày kết thúc năm học",
    accessorKey: "endOfSchoolYear",
    size: 100,
    meta: { align: "center" },
    cell: ({ row }) =>
      formatDayjsWithType(row.original.endOfSchoolYear, DATE_FORMAT),
  },
];

const VISIBLE_COL = [
  { id: "year", name: "Năm học" },
  { id: "startOfSemester1", name: "Ngày bắt đầu học kỳ 1" },
  { id: "startOfSemester2", name: "Ngày bắt đầu học kỳ 2" },
  { id: "endOfSchoolYear", name: "Ngày kết thúc năm học" },
];
