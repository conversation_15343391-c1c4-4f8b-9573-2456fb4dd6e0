import { <PERSON><PERSON>, <PERSON>ack } from "@mui/material";

import { useEffect, useMemo, useState } from "react";
import { useWatch } from "react-hook-form";
import { HookFormComponentType } from "../ConfigDomainModalAction";
import { IDomainConfigModel } from "@/app/(main)/root/cau-hinh-domain/domain.model";
import {
  AppFormAutocomplete,
  AppFormToggle,
  AppFormTextField,
  GridFormContainer,
} from "@/components/common";
import { ApiConstant, DataConstant } from "@/constant";
import http from "@/api";
import { DataListResponseModel } from "@/models/response.model";
import { convertDataToOptions } from "@/utils/format.utils";
import { handleFetchPhongList } from "../../hooks/handleFetchPhongList";
import { handleFetchTruongList } from "../../hooks/handleFetchTruongList";
import { IDoet } from "@/models/educationUnits.model";

const AddConfigModalPanel = ({
  onSetValueForm,
  control,
  errors,
}: AddConfigModalPanelProps) => {
  const [doetList, setDoetList] = useState<any[]>([]);
  const [divisionList, setDivisionList] = useState<any[]>([]);
  const [schoolList, setSchoolList] = useState<any[]>([]);

  const divisionCode = useWatch({
    control,
    name: CONFIG_CODE_KEY.divisionCode,
  });
  const doetCode = useWatch({
    control,
    name: CONFIG_CODE_KEY.doetCode,
  });
  const groupUnitCode = useWatch({
    control,
    name: CONFIG_CODE_KEY.groupUnitCode,
  });

  useEffect(() => {
    if (!groupUnitCode) return;

    const fetchDoetList = async () => {
      try {
        const res = await http.get<DataListResponseModel<IDoet>>(
          ApiConstant.GET_DOET_LIST
        );
        if (res.code === ApiConstant.ERROR_CODE_OK) {
          setDoetList(convertDataToOptions(res.data.data));
        }
      } catch (error) {
        console.error("Error fetching Doet list:", error);
      }
    };

    fetchDoetList();
  }, [groupUnitCode]);

  // common: xử lý danh sách trường khi phòng, sở, thay đổi
  useEffect(() => {
    const handleSetTruongListWhenFilterChange = async () => {
      const isTruongType =
        groupUnitCode?.id === DataConstant.DON_VI_TYPE.truong;

      const hasDoetCodeOrDivisionCode = doetCode || divisionCode;

      if (!isTruongType || (isTruongType && !hasDoetCodeOrDivisionCode)) {
        setSchoolList([]);
      } else {
        const truongListRes = await handleFetchTruongList(
          doetCode.code,
          divisionCode?.code || ""
        );
        setSchoolList(truongListRes);
      }
    };

    handleSetTruongListWhenFilterChange();
  }, [doetCode, divisionCode]);

  // Sở thay đổi
  useEffect(() => {
    const fetchDivisionList = async () => {
      if (!doetCode && divisionList.length !== 0) {
        setDivisionList([]);
      } else if (doetCode?.code) {
        const phongListRes = await handleFetchPhongList(doetCode.code);
        setDivisionList(phongListRes);
      }
    };

    fetchDivisionList();
  }, [doetCode]);

  const unitLevelList = useMemo(() => {
    return DataConstant.DON_VI_LIST.filter(
      (item) => item.id !== DataConstant.DON_VI_TYPE.bo
    );
  }, []);

  return (
    <GridFormContainer>
      <Grid size={12}>
        <AppFormAutocomplete
          control={control}
          name={CONFIG_CODE_KEY.groupUnitCode}
          label="Cấp đơn vị"
          options={unitLevelList}
          rules={{
            required: "Cấp đơn vị không được để trống",
          }}
        />
      </Grid>

      {typeof groupUnitCode === "object" && groupUnitCode?.id && (
        <Grid size={12}>
          <AppFormAutocomplete
            control={control}
            label="Sở"
            name={CONFIG_CODE_KEY.doetCode}
            rules={{
              required:
                groupUnitCode?.id === DataConstant.DON_VI_TYPE.so
                  ? "Sở không được để trống"
                  : false,
            }}
            onChangeValueForm={(data) => {
              if (groupUnitCode?.id === DataConstant.DON_VI_TYPE.so) {
                onSetValueForm(
                  CONFIG_CODE_KEY.name,
                  typeof data === "object" && data !== null && "label" in data
                    ? (data as any).label
                    : ""
                );
              } else {
                onSetValueForm(CONFIG_CODE_KEY.name, "");
                onSetValueForm(CONFIG_CODE_KEY.divisionCode, null);
                onSetValueForm(CONFIG_CODE_KEY.schoolCode, null);
              }
            }}
            options={doetList}
          />
        </Grid>
      )}

      {groupUnitCode !== null &&
        groupUnitCode?.id !== DataConstant.DON_VI_TYPE.so && (
          <Grid size={12}>
            <AppFormAutocomplete
              control={control}
              label="Phòng"
              name={CONFIG_CODE_KEY.divisionCode}
              rules={{
                required:
                  groupUnitCode?.id === DataConstant.DON_VI_TYPE.phong
                    ? "Phòng không được để trống"
                    : false,
              }}
              onChangeValueForm={(data) => {
                if (groupUnitCode?.id === DataConstant.DON_VI_TYPE.phong) {
                  onSetValueForm(
                    CONFIG_CODE_KEY.name,
                    typeof data === "object" && data !== null && "label" in data
                      ? (data as any).label
                      : ""
                  );
                } else {
                  onSetValueForm(CONFIG_CODE_KEY.name, "");
                  onSetValueForm(CONFIG_CODE_KEY.schoolCode, null);
                }
              }}
              options={divisionList}
            />
          </Grid>
        )}

      {typeof groupUnitCode === "object" &&
        groupUnitCode?.id === DataConstant.DON_VI_TYPE.truong && (
          <Grid size={12}>
            <AppFormAutocomplete
              control={control}
              label="Trường"
              name={CONFIG_CODE_KEY.schoolCode}
              rules={{
                required:
                  groupUnitCode?.id === DataConstant.DON_VI_TYPE.truong
                    ? "Trường không được để trống"
                    : false,
              }}
              onChangeValueForm={(data) => {
                if (groupUnitCode?.id === DataConstant.DON_VI_TYPE.truong) {
                  onSetValueForm(
                    CONFIG_CODE_KEY.name,
                    typeof data === "object" && data !== null && "label" in data
                      ? (data as any).label
                      : ""
                  );
                }
              }}
              options={schoolList}
            />
          </Grid>
        )}

      <Grid size={12}>
        <AppFormTextField
          label="Tên"
          control={control}
          name={CONFIG_CODE_KEY.name}
        />
      </Grid>
      <Grid size={12}>
        <AppFormTextField
          label="Domain"
          control={control}
          name={CONFIG_CODE_KEY.domainUrl}
          rules={{
            required: "Vui lòng nhập domain!",
          }}
          textfieldProps={{
            error: Boolean(errors.domainUrl),
            helperText: errors.domainUrl?.message,
            autoFocus: true,
          }}
        />
      </Grid>

      <Grid size={4}>
        <AppFormToggle
          control={control}
          name={CONFIG_CODE_KEY.status}
          label="Trạng thái"
        />
      </Grid>

      <Grid size={5}>
        <AppFormToggle
          control={control}
          name={CONFIG_CODE_KEY.isShareDomain}
          label="Domain chung"
        />
      </Grid>
    </GridFormContainer>
  );
};

type AddConfigModalPanelProps = HookFormComponentType & {
  dataSelected?: IDomainConfigModel;
};

export enum CONFIG_CODE_KEY {
  groupUnitCode = "groupUnitCode",
  schoolCode = "schoolCode",
  doetCode = "doetCode",
  divisionCode = "divisionCode",
  name = "name",
  domainUrl = "domainUrl",
  status = "status",
  isShareDomain = "isShareDomain",
}

export default AddConfigModalPanel;
