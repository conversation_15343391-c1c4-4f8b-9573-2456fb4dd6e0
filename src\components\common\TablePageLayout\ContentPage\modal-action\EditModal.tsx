"use client";

import AppModal from "@/components/common/modal/AppModal";
import React, { memo, JSX, useEffect, ReactNode } from "react";
import { FormConfig, FormFieldConfig } from "../../type";
import { Button } from "@mui/material";
import { FormProvider, useForm } from "react-hook-form";
import FromActionsModal from "./FromActionsModal";
import useActionsData from "./hooks/useActionsData";
import useDefaultFormValues from "./hooks/useDefaultFormValues";
import { DEFAULT_UNIX } from "@/components/common/AppAutoComplete";
import { useModalAction } from "../../modal-store/useModalAction";
import { useModalData } from "../../modal-store/useModalSelector";

const EditModal = <T,>({
  isOpen,
  onClose,
  formConfig,
  createFormContent,
}: EditModalProps<T>) => {
  const { handleEditData } = useActionsData<T>();
  const modalData = useModalData();

  const defaultValues = useDefaultFormValues(formConfig?.updateFields);

  const methods = useForm({
    defaultValues,
  });

  const { control, handleSubmit, reset, setValue } = methods;

  const handleClose = () => {
    onClose();
    reset(defaultValues);
  };

  const handleSuccess = () => {
    formConfig?.onSuccess?.();
    handleClose();
  };

  const handleSubmitData = (data: Record<string, any>) => {
    handleEditData(
      { id: (modalData as any)?.id, ...data },
      formConfig,
      handleSuccess
    );
  };

  useEffect(() => {
    if (isOpen) {
      Object.keys(defaultValues).forEach((key) => {
        const newValue = (modalData as any)?.[key];
        const currentKey = formConfig?.updateFields?.find(
          (item) => item.key === key
        );
        if (newValue !== undefined) {
          if (currentKey?.type === "select") {
            if (currentKey.selectConfig?.isMulti) {
              if (Array.isArray(newValue)) {
                const multiVal = newValue.map((item: any) => {
                  return typeof item === "object" && item !== null
                    ? item?.[currentKey?.selectConfig?.valueKey || DEFAULT_UNIX]
                    : item;
                });
                setValue(key, multiVal);
              } else {
                setValue(key, []);
              }
            } else {
              setValue(key, newValue);
            }
          } else {
            setValue(key, newValue);
          }
        }
      });
    }
  }, [isOpen, modalData]);

  return (
    <FormProvider {...methods}>
      <AppModal
        onSubmit={handleSubmit(handleSubmitData)}
        isOpen={isOpen}
        onClose={handleClose}
        modalTitleProps={{
          title: "Chỉnh sửa",
        }}
        slotProps={{
          paper: {
            component: "form",
          },
        }}
        modalContentProps={{
          content: createFormContent ? (
            createFormContent(formConfig?.updateFields)
          ) : (
            <FromActionsModal<any>
              control={control}
              formField={formConfig?.updateFields}
              onSetValue={setValue}
            />
          ),
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleClose}
              >
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
        {...formConfig?.modalProps}
      />
    </FormProvider>
  );
};

type EditModalProps<T> = {
  isOpen: boolean;
  onClose: () => void;
  formConfig?: FormConfig<T>;
  createFormContent?: (formField?: FormFieldConfig<T>[]) => ReactNode;
};

export default memo(EditModal) as <T>(props: EditModalProps<T>) => JSX.Element;
