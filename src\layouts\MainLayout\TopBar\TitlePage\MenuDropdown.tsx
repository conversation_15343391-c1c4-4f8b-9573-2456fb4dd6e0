import { AppLink } from "@/components/common";
import { IMenu, IMenuGroup } from "@/models/menu.model";
import { Box, Popover, PopoverProps, Stack, Typography } from "@mui/material";
import { usePathname } from "next/navigation";
import React, { memo } from "react";
import { useAppSelector } from "@/redux/hook";
import { selectSystemMenuData } from "@/redux/app.slice";

type MenuDropdownProps = PopoverProps & {
  onMenuItemClick?: () => void;
};

const MenuDropdown = ({
  onMenuItemClick,
  ...otherProps
}: MenuDropdownProps) => {
  const pathname = usePathname();
  const systemMenuGroups = useAppSelector(selectSystemMenuData);

  const renderMenuItem = (item: IMenu) => {
    const isActive = pathname === item.href;

    return (
      <Stack
        key={item.id}
        component={AppLink}
        href={item.href}
        direction="row"
        alignItems="center"
        spacing={2}
        onClick={onMenuItemClick}
        sx={{
          px: 3.5,
          py: 1.5,
          textDecoration: "none",
          color: isActive ? "primary.main" : "text.primary",
          backgroundColor: isActive ? "grey.100" : "transparent",
          cursor: "pointer",
          borderRadius: 1,
          "&:hover": {
            backgroundColor: "grey.100",
          },
        }}
      >
        <Typography
          variant="body1"
          sx={{
            flex: 1,
          }}
        >
          {item.name}
        </Typography>
      </Stack>
    );
  };

  const renderGroupSection = (group: IMenuGroup) => {
    const isGroupActive = group.items.some((item) => pathname === item.href);

    return (
      <Box key={group.id}>
        <Stack
          component={group.items.length <= 1 ? AppLink : Box}
          href={group.items.length <= 1 ? group.items[0].href : undefined}
          direction="row"
          alignItems="center"
          spacing={2}
          onClick={group.items.length <= 1 ? onMenuItemClick : undefined}
          sx={{
            px: 2.5,
            py: 1,
            cursor: group.items.length <= 1 ? "pointer" : "default",
            backgroundColor:
              group.items.length <= 1
                ? isGroupActive
                  ? "grey.100"
                  : "transparent"
                : "transparent",
            "&:hover": {
              backgroundColor:
                group.items.length <= 1 ? "grey.100" : "transparent",
            },
          }}
        >
          <Typography
            variant="h5"
            sx={{
              fontWeight: 600,
              color:
                group.items.length <= 1
                  ? isGroupActive
                    ? "primary.main"
                    : "text.primary"
                  : "text.primary",
            }}
          >
            {group.name}
          </Typography>
        </Stack>
        {group.items.length > 1 &&
          group.items.map((item) => renderMenuItem(item))}
      </Box>
    );
  };

  return (
    <Popover
      id="menu-dropdown-popover"
      disableRestoreFocus
      sx={{
        marginTop: "8px",
      }}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "left",
      }}
      slotProps={{
        paper: {
          sx: {
            boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.15)",
            py: 1,
            maxHeight: "400px",
            minWidth: "300px",
            maxWidth: "400px",
            overflowY: "auto",
          },
        },
      }}
      {...otherProps}
    >
      <Stack spacing={0.5}>
        {systemMenuGroups.map((group) => (
          <React.Fragment key={group.id}>
            {renderGroupSection(group)}
          </React.Fragment>
        ))}
      </Stack>
    </Popover>
  );
};

export default memo(MenuDropdown);
