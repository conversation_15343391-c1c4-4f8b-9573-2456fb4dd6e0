import { ApiConstant, EnvConstant } from "@/constant";
import {
  DataListResponseModel,
  IPaginationModel,
} from "@/models/response.model";
import { PayloadAction } from "@reduxjs/toolkit";
import { call, put, select, takeLatest } from "redux-saga/effects";
import { ISchoolConfig } from "./schoolConfig.model";
import {
  getMoetSchoolListService,
  postMultiMoetSchoolListService,
} from "./schoolConfig.service";
import { schoolConfigActions } from "./schoolConfig.slice";
import { toast } from "sonner";
import { extractErrorMessage } from "@/utils/common.utils";

function* getMoetSchoolListSaga(action: PayloadAction<IPaginationModel>) {
  try {
    const params = action.payload;
    const response: DataListResponseModel<ISchoolConfig> = yield call(
      getMoetSchoolListService,
      params
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        schoolConfigActions.getMoetSchoolDataListSuccess(response.data)
      );
    } else {
      yield put(
        schoolConfigActions.getMoetSchoolDataListSuccess({
          data: [],
          totalCount: 0,
        })
      );
    }
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    yield put(
      schoolConfigActions.getMoetSchoolDataListSuccess({
        data: [],
        totalCount: 0,
      })
    );
  }
}

function* postMoetSchoolListSaga(
  action: PayloadAction<{ data: ISchoolConfig[]; onSuccess: () => void }>
) {
  try {
    const response: DataListResponseModel<ISchoolConfig> = yield call(
      postMultiMoetSchoolListService,
      action.payload.data
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      action.payload.onSuccess();
      toast.success("Thành công!", {
        description: "Thêm mới đơn vị thành công.",
      });
    } else {
      toast.error("Thất bại!", {
        description: "Thêm mới đơn vị không thành công.",
      });
    }
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    const description = extractErrorMessage(error);
    toast.error("Thất bại!", {
      description,
    });
  }
}

export function* schoolConfigSaga() {
  yield takeLatest(
    schoolConfigActions.getMoetSchoolDataList,
    getMoetSchoolListSaga
  );
  yield takeLatest(
    schoolConfigActions.postMultiMoetSchool,
    postMoetSchoolListSaga
  );
}
