export enum STATUS_TYPE {
  inActive,
  active,
}

export enum BOOLEAN_TYPE {
  false,
  true,
}

export enum SCHOOL_LEVEL {
  mamNon = 1,
  tieuHoc = 2,
  thcs = 4,
  thpt = 8,
  gdtx = 16,
}

export enum BOOLEAN_TYPE_STRING {
  false = "false",
  true = "true",
}

export enum CATEGORY_ID {
  none = 0,
  paper,
  ebook,
  audioBook,
  album,
  video,
  electure,
  skill,
  otherDocument,
  emagazine,
  interactiveBook,
  news,
}

export enum SEMESTER_TYPE {
  semester1 = 1,
  semester2 = 2,
}

export const SEMESTER_TYPE_LIST = [
  { id: SEMESTER_TYPE.semester1, label: "Học kỳ 1" },
  { id: SEMESTER_TYPE.semester2, label: "Học kỳ 2" },
];

export enum DON_VI_TYPE {
  bo = "01",
  so = "02",
  phong = "03",
  truong = "04",
  doitac = "05",
}

export const STATUS_TYPE_LIST = [
  {
    id: STATUS_TYPE.active,
    label: "Hiển thị",
  },
  {
    id: STATUS_TYPE.inActive,
    label: "Không hiển thị",
  },
];

export const STATUS_ACTIVE_LIST = [
  {
    id: STATUS_TYPE.active,
    label: "Kích hoạt",
  },
  {
    id: STATUS_TYPE.inActive,
    label: "Không kích hoạt",
  },
];

export const DON_VI_LIST = [
  {
    id: DON_VI_TYPE.bo,
    name: "Cấp Bộ",
    label: "Cấp bộ",
    schoolCode: DON_VI_TYPE.bo,
  },
  {
    id: DON_VI_TYPE.so,
    name: "Cấp Sở",
    label: "Cấp sở",
    schoolCode: DON_VI_TYPE.so,
  },
  {
    id: DON_VI_TYPE.phong,
    name: "Cấp Phòng",
    label: "Cấp phòng",
    schoolCode: DON_VI_TYPE.phong,
  },
  {
    id: DON_VI_TYPE.truong,
    name: "Cấp Trường",
    label: "Cấp trường",
    schoolCode: DON_VI_TYPE.truong,
  },
];

export enum USER_TYPE {
  teacher = 1,
  student = 2,
}

export const USER_TYPE_LIST = [
  {
    id: USER_TYPE.teacher,
    label: "Giáo viên",
  },
  {
    id: USER_TYPE.student,
    label: "Học sinh",
  },
];

export enum GENDER_TYPE {
  male = 1,
  female = 2,
}

export const GENDER_TYPE_LIST = [
  {
    id: GENDER_TYPE.male,
    label: "Nam",
  },
  {
    id: GENDER_TYPE.female,
    label: "Nữ",
  },
];

export const SCHOOL_LEVEL_LIST = [
  { id: SCHOOL_LEVEL.mamNon, label: "Mầm non" },
  { id: SCHOOL_LEVEL.tieuHoc, label: "Tiểu học" },
  { id: SCHOOL_LEVEL.thcs, label: "Trung học cơ sở" },
  { id: SCHOOL_LEVEL.thpt, label: "Trung học phổ thông" },
  { id: SCHOOL_LEVEL.gdtx, label: "Giáo dục thường xuyên" },
];
