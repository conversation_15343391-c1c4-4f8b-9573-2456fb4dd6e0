import { rootReducer } from "@/redux/reducer";
import { RootState } from "@/redux/store";
import {
  createSelector,
  createSlice,
  PayloadAction,
  WithSlice,
} from "@reduxjs/toolkit";

export interface IInitialState {
  rowSelected: number[];
}

const initialState: IInitialState = {
  rowSelected: [],
};

export const selectorRowSelected = createSelector(
  [(state: RootState) => state.schoolWeek?.rowSelected ?? []],
  (rowSelected) => rowSelected
);

const reducers = {
  setRowSelected: (state: IInitialState, action: PayloadAction<number[]>) => {
    state.rowSelected = action.payload;
  },
};

export const schoolWeekSlice = createSlice({
  name: "schoolWeek",
  initialState,
  reducers,
});

export const schoolWeekActions = schoolWeekSlice.actions;

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices extends WithSlice<typeof schoolWeekSlice> {}
}

// Inject reducer
const injectedSchoolWeekSlice = schoolWeekSlice.injectInto(rootReducer);
export const schoolWeekSelectors = injectedSchoolWeekSlice.selectors;
