import { AppConfirmModal, AppTable } from "@/components/common";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { ColumnDef } from "@tanstack/react-table";
import React, { memo, useMemo, useState } from "react";
import {
  equipmentDocumentActions,
  selectDevices,
} from "../../../equipmentDocument.slice";
import { IDevices } from "../../../equipmentDocument.model";
import { formatDayjsWithType, formatPrice } from "@/utils/format.utils";
import DeleteCell from "@/components/common/table/cell/DeleteCell";

const DetailTable = ({ tableProps }) => {
  const dispatch = useAppDispatch();

  const devices = useAppSelector(selectDevices);
  const [data, setData] = useState<IDevices | null>(null);
  const columns = useMemo(() => getColumns(setData), []);

  return (
    <>
      <AppTable
        columns={columns}
        data={devices}
        totalData={0}
        hasDefaultPagination
        {...tableProps}
      />

      {Boolean(data) && (
        <AppConfirmModal
          modalTitleProps={{
            title: "Bạn xác nhận xóa thiết bị này",
          }}
          isOpen={Boolean(data)}
          onClose={() => setData(null)}
          onConfirm={() =>
            dispatch(dispatch(equipmentDocumentActions.deleteDevice(data)))
          }
        />
      )}
    </>
  );
};

export default memo(DetailTable);

const getColumns = (onDelete): ColumnDef<IDevices>[] => [
  {
    id: "index",
    header: "STT",
    cell: ({ row }) => row.index + 1,
    size: 60,
    meta: {
      align: "center",
    },
  },
  {
    id: "delete",
    header: "Xóa",
    cell: ({ row }) => <DeleteCell onClick={() => onDelete(row.original)} />,
    size: 60,
    meta: {
      align: "center",
    },
  },
  {
    id: "deviceCode",
    header: "Mã Thiết bị",
    accessorKey: "deviceCode",
  },
  {
    id: "code",
    header: "Mã",
    accessorKey: "code",
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
  },
  {
    id: "roomName",
    header: "Kho phòng",
    accessorKey: "roomName",
  },
  {
    id: "deviceUnitName",
    header: "Đơn vị tính",
    accessorKey: "deviceUnitName",
    size: 80,
  },
  {
    id: "entryDate",
    header: "Ngày nhập",
    size: 80,
    accessorFn: (row) => formatDayjsWithType(row.entryDate),
  },
  {
    id: "quantity",
    header: "Số lượng",
    size: 50,
    accessorKey: "quantity",
    meta: {
      align: "right",
    },
  },
  {
    id: "price",
    header: "Đơn giá",
    size: 60,
    accessorFn: (row) => formatPrice(row.price),
    meta: {
      align: "right",
    },
  },
  {
    id: "totalPrices",
    header: "Tổng giá trị",
    accessorFn: (row) => formatPrice(row.totalPrices),
    meta: {
      align: "right",
    },
    size: 100,
  },
];
