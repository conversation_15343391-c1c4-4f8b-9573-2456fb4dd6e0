"use client";

import React, { memo } from "react";
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  CardActionArea,
} from "@mui/material";
import { ArrowIcon, SchoolIcon } from "@/components/icons";
import { AppLink } from "@/components/common";
import { IMenu } from "@/models/menu.model";
import { CardVariant } from "./enums";

type MenuCardProps = {
  item: IMenu;
  variant: CardVariant;
};

const MenuCard: React.FC<MenuCardProps> = ({
  item,
  variant = CardVariant.CARD,
}) => {
  const IconComponent = SchoolIcon; // Default icon, can be mapped based on item.icon

  if (variant === CardVariant.LIST) {
    return null;
  }

  return (
    <Card
      sx={{
        height: "100%",
        borderRadius: 2,
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        position: "relative",
        overflow: "hidden",
        "&:hover": {
          boxShadow: "0 4px 16px rgba(0,0,0,0.15)",
          transform: "translateY(-2px)",
          "&::after": {
            opacity: 1,
          },
        },
        "&::after": {
          content: '""',
          position: "absolute",
          bottom: 0,
          right: 0,
          width: 0,
          height: 0,
          borderLeft: "70px solid transparent",
          borderBottom: "50px solid",
          borderBottomColor: "primary.main",
          opacity: 0,
          transition: "opacity 0.3s ease-in-out",
        },
        transition: "all 0.3s ease-in-out",
      }}
    >
      <CardActionArea
        component={AppLink}
        href={item.href}
        sx={{ height: "100%" }}
      >
        <CardContent
          sx={{
            gap: 2,
            p: 3,
            height: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            textAlign: "center",
          }}
        >
          <Box
            sx={{
              width: 64,
              height: 64,
              borderRadius: "50%",
              backgroundColor: "primary.light",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <IconComponent
              sx={{
                fontSize: 32,
                color: "primary.main",
              }}
            />
          </Box>

          <Typography
            variant="h4"
            sx={{
              color: "text.primary",
            }}
          >
            {item.name}
          </Typography>

          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Typography variant="h5">Xem</Typography>
            <ArrowIcon
              sx={{
                fontSize: 18,
                transform: "rotate(180deg)",
              }}
            />
          </Box>
        </CardContent>
      </CardActionArea>
    </Card>
  );
};

export default memo(MenuCard);
