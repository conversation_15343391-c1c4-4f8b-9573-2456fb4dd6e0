import {
  AppForm<PERSON>utocomplete,
  AppFormToggle,
  AppFormRadio,
  AppFormTextField,
  GridFormContainer,
} from "@/components/common";
import { USER_TYPE_LIST } from "@/constant/data.const";
import { MANAGE_BY, MANAGE_BY_LIST } from "@/models/system.model";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import {
  selectGradeList,
  selectDeviceTypeList,
  selectSubjectList,
  selectUnitList,
} from "@/redux/system.slice";
import { Grid, Stack } from "@mui/material";
import React, { memo, useCallback, useEffect } from "react";
import { useFormContext, useFormState } from "react-hook-form";
import AppFormLayoutPanel from "@/components/common/AppFormLayoutPanel";
import TableEditForm from "./TableEditForm";
import { equipmentDocumentActions } from "../../../equipmentDocument.slice";

const DeviceForm = ({
  isEdit,
  documentDate,
}: {
  isEdit?: boolean;
  documentDate?: Date | null;
}) => {
  const dispatch = useAppDispatch();
  const { control, setValue, getValues } = useFormContext();
  const subjectList = useAppSelector(selectSubjectList);
  const unitList = useAppSelector(selectUnitList);
  const deviceTypeList = useAppSelector(selectDeviceTypeList);
  const gradeList = useAppSelector(selectGradeList);

  const changeManage = useCallback(
    (value: MANAGE_BY) => {
      dispatch(equipmentDocumentActions.togglePendingBuildDevice(true));
      setTimeout(() => {
        setValue("maxIndexItem", 1);
        setValue("devices", []);

        dispatch(equipmentDocumentActions.togglePendingBuildDevice(false));
      }, 0);
    },
    [setValue]
  );

  useEffect(() => {
    setValue("documentDate", documentDate);
  }, [documentDate]);

  return (
    <Stack spacing={1.5}>
      <AppFormLayoutPanel title="Thông tin danh mục thiết bị">
        <GridFormContainer>
          <Grid size={4}>
            <AppFormTextField
              direction="row"
              label="Mã thống kê"
              name="statisticCode"
              control={control}
              textfieldProps={{
                disabled: isEdit,
              }}
              labelProps={{ sx: { minWidth: 120 } }}
            />
          </Grid>

          <Grid size={4}>
            <FieldWithError
              name="deviceCode"
              label="Mã thiết bị"
              isEdit={isEdit}
              control={control}
              required
            />
          </Grid>

          <Grid size={4}>
            <FieldWithError
              name="deviceName"
              label="Tên thiết bị"
              control={control}
              required
              isEdit={isEdit}
            />
          </Grid>

          <Grid size={4}>
            <AppFormAutocomplete
              label="Đơn vị tính"
              name="deviceUnitId"
              control={control}
              options={unitList}
              direction="row"
              labelProps={{ sx: { minWidth: 120 } }}
              rules={{ required: "Đơn vị tính không được để trống" }}
              autocompleteProps={{
                disabled: isEdit,
                textFieldProps: useErrorTextFieldProps("deviceUnitId"),
              }}
            />
          </Grid>

          <Grid size={4}>
            <AppFormAutocomplete
              label="Loại thiết bị"
              name="schoolDeviceTypeId"
              control={control}
              options={deviceTypeList}
              direction="row"
              labelProps={{ sx: { minWidth: 120 } }}
              autocompleteProps={{
                disabled: isEdit,
              }}
            />
          </Grid>

          <Grid size={4}>
            <AppFormAutocomplete
              label="Môn học"
              name="schoolSubjectId"
              control={control}
              options={subjectList}
              direction="row"
              labelProps={{ sx: { minWidth: 120 } }}
              autocompleteProps={{
                disabled: isEdit,
              }}
            />
          </Grid>

          <Grid size={4}>
            <AppFormAutocomplete
              label="Khối lớp"
              name="gradeCodes"
              control={control}
              options={gradeList}
              isMultiple
              direction="row"
              labelProps={{ sx: { minWidth: 120 } }}
              autocompleteProps={{
                hasAllOption: true,
                limitTags: 2,
                disabled: isEdit,
              }}
            />
          </Grid>

          <Grid size={4}>
            <AppFormAutocomplete
              label="Đối tượng sử dụng"
              name="userTypes"
              control={control}
              options={USER_TYPE_LIST}
              isMultiple
              direction="row"
              labelProps={{ sx: { minWidth: 120 } }}
              autocompleteProps={{
                disabled: isEdit,
              }}
            />
          </Grid>

          <Grid size={4}>
            <AppFormRadio
              radioProps={{
                disabled: isEdit,
              }}
              label="Quản lý theo"
              name="manageBy"
              control={control}
              radioList={MANAGE_BY_LIST}
              onChangeValueForm={
                changeManage as (value: string | undefined | number) => void
              }
              direction="row"
              alignItems="center"
              formLabelProps={{ sx: { minWidth: 120 } }}
            />
          </Grid>

          <Grid size={2}>
            <AppFormToggle
              name="isConsumable"
              label="Là thiết bị tiêu hao"
              control={control}
              labelProps={{ sx: { minWidth: 122 } }}
              height="100%"
              toggleProps={{
                disabled: isEdit,
              }}
            />
          </Grid>

          <Grid size={2}>
            <AppFormToggle
              name="isSelfMade"
              label="Là thiết bị tự làm"
              control={control}
              labelProps={{ sx: { minWidth: 122 } }}
              height="100%"
              toggleProps={{
                disabled: isEdit,
              }}
            />
          </Grid>

          <Grid size={4}>
            <AppFormTextField
              direction="row"
              name="minimumQuantity"
              label="Số lượng tối thiểu"
              control={control}
              labelProps={{ sx: { minWidth: 120 } }}
              textfieldProps={{
                disabled: isEdit,
                type: "number",
                slotProps: {
                  htmlInput: {
                    min: 1,
                  },
                },
              }}
            />
          </Grid>
          <Grid size={4} />
        </GridFormContainer>
      </AppFormLayoutPanel>

      <TableEditForm />
    </Stack>
  );
};

export default memo(DeviceForm);

const FieldWithError = ({
  name,
  label,
  control,
  required,
  isEdit = false,
}: {
  name: string;
  label: string;
  control: any;
  required?: boolean;
  isEdit?: boolean;
}) => {
  const { errors } = useFormState({ name, control });

  return (
    <AppFormTextField
      direction="row"
      label={label}
      name={name}
      control={control}
      labelProps={{ sx: { minWidth: 120 } }}
      rules={
        required ? { required: `${label} không được để trống` } : undefined
      }
      textfieldProps={{
        disabled: isEdit,
        error: Boolean(errors?.[name]),
        helperText: errors?.[name]?.message as string,
      }}
    />
  );
};

function useErrorTextFieldProps(name: string) {
  const { errors } = useFormState({ name });
  return {
    error: Boolean(errors?.[name]),
    helperText: errors?.[name]?.message as string,
  };
}
