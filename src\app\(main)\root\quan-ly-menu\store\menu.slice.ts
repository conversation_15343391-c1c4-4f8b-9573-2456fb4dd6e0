import { DataConstant } from "@/constant";
import { IMenuConfigFilter, IMenuTree } from "@/models/menu.model";
import { rootReducer } from "@/redux/reducer";
import { createSlice, PayloadAction, WithSlice } from "@reduxjs/toolkit";

/* ------------- Initial State ------------- */
export interface IInitialState {
  menuConfig: IMenuTree[];
  filter: IMenuConfigFilter;
}

const initialState: IInitialState = {
  menuConfig: [],
  filter: {
    menuTypeId: null,
    groupUnitCode: DataConstant.DON_VI_TYPE.truong,
  },
};

/* ------------- Selector ------------- */
const selectors = {
  menuConfig: (state: IInitialState) => state.menuConfig,
  filter: (state: IInitialState) => state.filter,
};

/* ------------- Reducers ------------- */
const reducers = {
  setMenuConfig: (state: IInitialState, action: PayloadAction<IMenuTree[]>) => {
    state.menuConfig = action.payload;
  },
  setFilterWithKey: (
    state: IInitialState,
    action: PayloadAction<{ key: keyof IMenuConfigFilter; value: any }>
  ) => {
    state.filter[action.payload.key] = action.payload.value;
  },
  refreshFilter: (state: IInitialState) => {
    state.filter = { ...state.filter };
  },
};

export const menuSlice = createSlice({
  name: "menuReducer",
  initialState,
  reducers,
  selectors,
});

export const menuActions = menuSlice.actions;

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices extends WithSlice<typeof menuSlice> {}
}

const injectedMenuSlice = menuSlice.injectInto(rootReducer);

export const menuSelectors = injectedMenuSlice.selectors;
