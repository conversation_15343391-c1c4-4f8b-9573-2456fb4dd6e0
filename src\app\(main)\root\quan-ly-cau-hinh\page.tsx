import { TablePageLayout } from "@/components/common";
import { FormFieldConfig } from "@/components/common/TablePageLayout/type";
import { CONFIG } from "@/constant/api.const";
import { IConfig } from "@/app/(main)/root/quan-ly-cau-hinh/config.model";
import { ColumnDef } from "@tanstack/react-table";
import React from "react";

export const metadata = {
  title: "Quản lý cấu hình",
};

const ConfigPage = () => {
  const columns = getColumns();

  return (
    <TablePageLayout<IConfig>
      fetchAll
      visibleCol={VISIBLE_COL}
      apiUrl={CONFIG}
      tableProps={{
        columns,
        hasDefaultPagination: true,
      }}
      filterConfig={[
        {
          key: "searchKey",
          type: "text",
          label: "Tìm kiếm",
          size: 2.4,
        },
      ]}
      actions={["create", "update", "delete"]}
      formConfig={{
        deleteUrl: CONFIG,
        detailUrl: CONFIG,
        createUrl: CONFIG,
        updateUrl: CONFIG,
        createFields: CREATE_CONFIG,
        updateFields: UPDATE_CONFIG,
      }}
    />
  );
};

export default ConfigPage;

const getColumns = (): ColumnDef<IConfig>[] => [
  {
    id: "constKey",
    header: "Mã cấu hình",
    accessorKey: "constKey",
    size: 60,
  },
  {
    id: "name",
    header: "Tên cấu hình",
    accessorKey: "name",
    size: 150,
  },
  {
    id: "constValue",
    accessorKey: "constValue",
    header: "Giá trị cấu hình",
    size: 200,
  },
];

const VISIBLE_COL = [
  { id: "constKey", name: "Mã cấu hình" },
  { id: "name", name: "Tên cấu hình" },
  { id: "constValue", name: "Giá trị cấu hình" },
];

const CREATE_CONFIG: FormFieldConfig<IConfig>[] = [
  {
    key: "constKey",
    type: "text",
    label: "Mã cấu hình",
    size: 12,
    rules: {
      required: "Mã cấu hình không được để trống",
      maxLength: {
        value: 50,
        message: "Mã cấu hình không được dài quá 50 ký tự",
      },
    },
  },
  {
    key: "name",
    type: "text",
    label: "Tên cấu hình",
    size: 12,
    rules: {
      required: "Tên cấu hình không được để trống",
      maxLength: {
        value: 500,
        message: "Tên cấu hình không được dài quá 500 ký tự",
      },
    },
  },
  {
    key: "constValue",
    type: "text",
    label: "Giá trị cấu hình",
    rules: {
      required: "Giá trị cấu hình không được để trống",
      maxLength: {
        value: 500,
        message: "Giá trị cấu hình không được dài quá 500 ký tự",
      },
    },
    size: 12,
  },
];

const UPDATE_CONFIG: FormFieldConfig<IConfig>[] = [
  {
    key: "constKey",
    type: "text",
    label: "Mã cấu hình",
    size: 12,
    rules: { required: "Mã cấu hình không được để trống" },
    disabled: true,
  },
  {
    key: "name",
    type: "text",
    label: "Tên cấu hình",
    size: 12,
    rules: { required: "Tên cấu hình không được để trống" },
  },
  {
    key: "constValue",
    type: "text",
    label: "Giá trị cấu hình",
    rules: {
      required: "Giá trị cấu hình không được để trống",
      maxLength: {
        value: 500,
        message: "Giá trị cấu hình không được dài quá 500 ký tự",
      },
    },
    size: 12,
  },
];
