import { flexRender } from "@tanstack/react-table";
import { memo } from "react";
import AppTableCell from "../../AppTableCell";
import { getCommonPinningStyles } from "../helper";

const RowTableCell = ({ cell }: any) => {
  const row = cell.row;
  const column = cell.column;
  const columnMeta = column.columnDef.meta || {};

  const colSpan = columnMeta?.colSpanOnParentRow;

  const isParentRow =
    row.depth === 0 &&
    row
      .getAllCells()
      .some((cell: any) => cell.column.columnDef?.meta?.colSpanOnParentRow);

  const isIndexColumn = column.id === "index";

  const shouldRenderCell = !isParentRow || isIndexColumn || colSpan;

  if (cell.isRowSpanned || !shouldRenderCell) return null;

  return (
    <AppTableCell
      id={column.id}
      align={columnMeta?.align}
      className={columnMeta?.cellClassName}
      sx={{
        fontSize: 14,
        ...columnMeta?.cellSx,
      }}
      rowSpan={cell.rowSpan}
      colSpan={isParentRow && colSpan ? colSpan : undefined}
      style={{
        ...getCommonPinningStyles(column),
      }}
    >
      {flexRender(column.columnDef.cell, cell.getContext())}
    </AppTableCell>
  );
};

export default memo(RowTableCell);
