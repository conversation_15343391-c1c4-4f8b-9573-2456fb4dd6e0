import http from "@/api";
import { ApiConstant } from "@/constant";
import { IDivision, IDoet, IUnitParams } from "@/models/educationUnits.model";
import { DataResponseModel } from "@/models/response.model";

export const getDoetListService = () => {
  return http.get<DataResponseModel<IDoet[]>>(ApiConstant.GET_DOET_LIST);
};

export const getDivisionListService = (params: IUnitParams) => {
  return http.get<DataResponseModel<IDivision[]>>(
    ApiConstant.GET_DIVISION_LIST,
    {
      params,
    }
  );
};
