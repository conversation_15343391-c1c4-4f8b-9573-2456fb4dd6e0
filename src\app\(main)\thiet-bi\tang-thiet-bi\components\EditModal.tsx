"use client";

import AppModal, { AppModalProps } from "@/components/common/modal/AppModal";
import React, { memo, useCallback, useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { Button } from "@mui/material";
import FormPanel from "./FormPanel";
import {
  IDeviceTransaction,
  IDeviceTransactionAction,
} from "../equipmentDocument.model";
import { useAppDispatch } from "@/redux/hook";
import { systemActions } from "@/redux/system.slice";
import { equipmentDocumentActions } from "../equipmentDocument.slice";

const EditModal = ({
  fetchCurrentData,
  onClose,
  modalData,
  ...otherProps
}: CreateModalProps) => {
  const dispatch = useAppDispatch();

  const methods = useForm({
    defaultValues: INIT_VALUE,
  });

  const { handleSubmit, reset, setValue } = methods;

  const handleClose = () => {
    onClose();
    reset(INIT_VALUE);
    dispatch(equipmentDocumentActions.clearActionData());
  };

  const handleSubmitData = useCallback(
    (data) => {
      dispatch(
        equipmentDocumentActions.updateDocumentEntry({
          data: {
            ...data,
            id: modalData?.id,
          },
          defaultData: modalData,
          onSuccess: () => {
            fetchCurrentData?.();
            handleClose();
          },
        })
      );
    },
    [fetchCurrentData, modalData]
  );

  useEffect(() => {
    if (modalData?.id) {
      setValue("documentNumber", modalData.documentNumber);
      setValue("documentDate", modalData.documentDate);
      setValue("notes", modalData.notes);
      setValue("schoolBudgetCategoryId", {
        id: modalData.schoolBudgetCategoryId,
        label: modalData.schoolBudgetCategoryName,
      } as any);
      dispatch(equipmentDocumentActions.initDocumentEntry(modalData));
    }
  }, [modalData?.id]);

  useEffect(() => {
    dispatch(systemActions.getSourceList());
    dispatch(systemActions.getUnitList());
    dispatch(systemActions.getDeviceTypeList());
    dispatch(systemActions.getSubjectList());
    dispatch(systemActions.getGradeList());
    dispatch(systemActions.getRoomList());
    dispatch(systemActions.getTeacherComboList());
    dispatch(systemActions.getCountryList());
  }, []);

  return (
    <FormProvider {...methods}>
      <AppModal
        component="form"
        onClose={handleClose}
        onSubmit={handleSubmit(handleSubmitData)}
        fullScreen
        modalTitleProps={{
          title: "Cập nhật chứng từ",
        }}
        modalContentProps={{
          sx: {
            display: "flex",
            flexDirection: "column",
            bgcolor: "background.grey",
            px: "12px",
            py: "12px",
          },
          content: (
            <>
              <FormPanel />
            </>
          ),
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                onClick={handleClose}
                variant="outlined"
                color="secondary"
              >
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
        {...otherProps}
      />
    </FormProvider>
  );
};

export default memo(EditModal);

type CreateModalProps = AppModalProps & {
  fetchCurrentData?: () => void;
  modalData: IDeviceTransaction | null;
};

export const INIT_VALUE: IDeviceTransactionAction = {
  documentNumber: "",
  documentDate: null,
  schoolBudgetCategoryId: 0,
  notes: "",
  deviceTransactionItems: [],
  // deleteDeviceDefinitionIds: [],
};
