import {
  App<PERSON><PERSON>Complete,
  AppTextField,
  GridFormContainer,
} from "@/components/common";
import { AppConstant, DataConstant } from "@/constant";
import {
  educationUnitsActions,
  selectPhongList,
  selectSoList,
} from "@/redux/educationUnits.slice";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { CommonUtils } from "@/utils";
import { Grid } from "@mui/material";
import { useCallback, useEffect, useMemo } from "react";
import {
  IMoetSchoolFilter,
  schoolConfigActions,
  selectFilterModal,
} from "../../../schoolConfig.slice";

const Filter = () => {
  const dispatch = useAppDispatch();
  const filter = useAppSelector(selectFilterModal);
  const soList = useAppSelector(selectSoList);
  const phongList = useAppSelector(selectPhongList);

  const handleChangeFilterWithKey = useCallback(
    (key: keyof IMoetSchoolFilter) => (value) => {
      dispatch(
        schoolConfigActions.changeFilterModalWithKey({
          key,
          value,
        })
      );
    },
    []
  );

  useEffect(() => {
    // Set default options
    handleChangeFilterWithKey("schoolLevelMoet")(
      DataConstant.SCHOOL_LEVEL_LIST[0]
    );

    handleChangeFilterWithKey("doetCode")(soList[0]);

    dispatch(
      educationUnitsActions.getPhongList({
        doetCode: soList[0]?.code,
      })
    );
  }, []);

  return (
    <GridFormContainer id="modal-filter">
      <Grid size={3}>
        <AppAutoComplete
          value={filter.schoolLevelMoet}
          options={DataConstant.SCHOOL_LEVEL_LIST}
          label="Cấp học"
          disableClearable
          onChange={(_, data) =>
            handleChangeFilterWithKey("schoolLevelMoet")(data)
          }
        />
      </Grid>
      <Grid size={3}>
        <AppAutoComplete
          options={soList}
          label="Sở"
          disableClearable
          value={filter.doetCode}
          onChange={(_, data) => {
            handleChangeFilterWithKey("doetCode")(data);
            handleChangeFilterWithKey("divisionCode")(null);

            dispatch(
              educationUnitsActions.getPhongList({
                doetCode:
                  typeof data === "object" && data !== null && "code" in data
                    ? (data as any).code
                    : undefined,
              })
            );
          }}
        />
      </Grid>
      <Grid size={3}>
        <AppAutoComplete
          options={phongList}
          label="Phòng"
          value={filter.divisionCode}
          onChange={(_, data) =>
            handleChangeFilterWithKey("divisionCode")(data)
          }
        />
      </Grid>

      <Grid size={3}>
        <AppTextField
          label="Mã trường"
          value={filter.schoolCode}
          onChange={(event) =>
            handleChangeFilterWithKey("schoolCode")(event.target.value)
          }
        />
      </Grid>
    </GridFormContainer>
  );
};

export default Filter;
