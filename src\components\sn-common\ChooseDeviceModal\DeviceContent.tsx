import { useAppDispatch, useAppSelector } from "@/redux/hook";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import { But<PERSON>, Grid, Stack } from "@mui/material";
import {
  AppAutoComplete,
  AppSearchDebounceTextFiled,
  AppTable,
  IOption,
} from "@/components/common";
import { STATUS_TYPE_LIST } from "@/constant/data.const";
import { ColumnDef } from "@tanstack/react-table";
import { IDeviceParams, IEduDevice } from "@/models/eduDevice.model";
import { AppConstant } from "@/constant";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import {
  eduDeviceActions,
  selectDeviceChooseList,
  selectTotalDeviceChoose, 
} from "@/redux/device/eduDevice.slice";

const DeviceContent = ({ onClose, onChooseDevice }) => {
  const dispatch = useAppDispatch();
  const data = useAppSelector(selectDeviceChooseList);
  const totalData = useAppSelector(selectTotalDeviceChoose);

  const [filter, setFilter] = useState<IDeviceParams>({});
  const [pagination, setPagination] = useState(
    AppConstant.DEFAULT_PAGINATION_SKIP_TAKE
  );

  const handleChooseDevice = useCallback(
    (data: IEduDevice) => {
      dispatch(
        eduDeviceActions.chooseDevice({
          data,
          onSuccess: (data) => {
            onClose();
            onChooseDevice(data);
          },
        })
      );
    },
    [onClose]
  );

  const columns = useMemo(
    () => getColumns({ skip: pagination.skip, onSelected: handleChooseDevice }),
    [pagination.skip, handleChooseDevice]
  );

  const handleChangeFilterWithKey = useCallback(
    (key: keyof IDeviceParams) => (value) => {
      setFilter((pre) => ({
        ...pre,
        [key]: value,
      }));
    },
    []
  );

  useEffect(() => {
    dispatch(
      eduDeviceActions.getDevice({
        ...filter,
      })
    );
  }, [filter]);

  return (
    <Stack spacing={1} height="100%">
      <Grid container spacing={2}>
        <Grid size={3}>
          <AppSearchDebounceTextFiled
            label="Tìm kiếm"
            valueInput={filter.searchKey ?? ""}
            onChangeValue={handleChangeFilterWithKey("searchKey")}
          />
        </Grid>
        <Grid size={3}>
          <AppAutoComplete
            options={STATUS_TYPE_LIST}
            label="Trạng thái hiển thị"
            onChange={(_, value) =>
              handleChangeFilterWithKey("status")((value as IOption)?.id)
            }
            value={
              STATUS_TYPE_LIST.find((item) => item.id === filter.status) ?? null
            }
          />
        </Grid>
      </Grid>
      <AppTable
        columns={columns}
        totalData={totalData}
        data={data}
        paginationData={pagination}
        onPageChange={setPagination}
        {...TABLE_MODAL_FULL_HEIGHT}
      />
    </Stack>
  );
};

export default memo(DeviceContent);

const getColumns = ({
  skip,
  onSelected,
}: {
  skip: number;
  onSelected: (IEduDevice) => void;
}): ColumnDef<IEduDevice>[] => [
  {
    id: "index",
    header: "STT",
    cell: ({ row }) => row.index + skip + 1,
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "edit",
    header: "Chọn",
    cell: ({ row }) => (
      <Button
        variant="contained"
        size="small"
        onClick={() => onSelected(row.original)}
      >
        Chọn
      </Button>
    ),
    size: 70,
    meta: {
      align: "center",
    },
  },
  {
    id: "deviceCode",
    header: "Mã",
    accessorKey: "deviceCode",
    size: 60,
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
  },
  {
    id: "schoolDeviceTypeName",
    header: "Loại thiết bị",
    accessorKey: "schoolDeviceTypeName",
    size: 100,
  },
  {
    id: "deviceDTITypeName",
    header: "Loại thiết bị theo DTI",
    accessorKey: "deviceDTITypeName",
    size: 100,
  },
  {
    id: "deviceUnitName",
    header: "Đơn vị tính",
    accessorKey: "deviceUnitName",
    size: 100,
  },
  {
    id: "statisticCode",
    header: "Mã thống kê",
    accessorKey: "statisticCode",
    size: 100,
  },
  {
    id: "description",
    header: "Mô tả",
    accessorKey: "description",
    size: 100,
  },
];
