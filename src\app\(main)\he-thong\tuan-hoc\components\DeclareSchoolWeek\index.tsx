import DeclareForm from "@/app/(main)/he-thong/tuan-hoc/components/DeclareSchoolWeek/DeclareForm";
import useDeclareSchoolWeek from "@/app/(main)/he-thong/tuan-hoc/hooks/useDeclareSchoolWeek";
import { AppModal } from "@/components/common";
import { Button } from "@mui/material";
import React, { memo, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";

const DeclareSchoolWeek = ({ onFetchData }: { onFetchData?: () => void }) => {
  const [isOpen, setIsOpen] = useState(false);
  const methods = useForm({
    defaultValues: INITIAL_VALUES,
  });
  const { reset, handleSubmit } = methods;
  const { handleConfigDeclare } = useDeclareSchoolWeek();

  const handleClose = () => {
    setIsOpen(false);
    reset(INITIAL_VALUES);
  };

  const handleSubmitData = async (data: any) => {
    await handleConfigDeclare(data, () => {
      handleClose();
      onFetchData?.();
    });
  };

  return (
    <>
      <Button
        variant="outlined"
        color="secondary"
        onClick={() => setIsOpen(true)}
      >
        Khai báo tuần học
      </Button>
      <FormProvider {...methods}>
        <AppModal
          isOpen={isOpen}
          onClose={handleClose}
          component="form"
          onSubmit={handleSubmit(handleSubmitData)}
          modalTitleProps={{
            title: "Khai báo tuần học",
          }}
          modalActionsProps={{
            children: (
              <>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={handleClose}
                >
                  Đóng
                </Button>
                <Button variant="contained" color="primary" type="submit">
                  Ghi
                </Button>
              </>
            ),
          }}
          modalContentProps={{
            content: <DeclareForm />,
          }}
        />
      </FormProvider>
    </>
  );
};

export default memo(DeclareSchoolWeek);

const INITIAL_VALUES = {
  semester: null,
  fromDate: null,
  fromWeek: "",
  toWeek: "",
};
