"use client";

import React, { memo, useMemo } from "react";
import { Box, Tab, Tabs } from "@mui/material";
import { usePathname } from "next/navigation";
import { useAppSelector } from "@/redux/hook";
import { transformMenuItemsToTabs } from "./helper";
import { PathConstant } from "@/constant";
import { AppLink } from "@/components/common";

const TabNavigation: React.FC = () => {
  const pathname = usePathname();
  const menuSidebar = useAppSelector((state) => state.appReducer.menuSidebar);

  const isShow = pathname.startsWith(PathConstant.SYSTEM + "/");

  const tabItems = useMemo(() => {
    if (!isShow) return [];

    const systemMenuItem = menuSidebar.find(
      (item) => item.href === PathConstant.SYSTEM
    );
    return transformMenuItemsToTabs(systemMenuItem?.children || [], pathname);
  }, [menuSidebar, pathname, isShow]);

  if (!isShow || tabItems.length <= 1) {
    return null;
  }

  return (
    <Box
      sx={{
        borderBottom: 1,
        borderColor: "divider",
        backgroundColor: "background.grey",
      }}
    >
      <Tabs
        value={pathname}
        sx={{
          minHeight: "unset",
        }}
      >
        {tabItems.map((item) => (
          <Tab
            key={item.id}
            label={item.name}
            value={item.href}
            component={AppLink as React.ElementType}
            href={item.href}
            sx={{
              textTransform: "none",
              minHeight: 36,
              py: 0,
            }}
          />
        ))}
      </Tabs>
    </Box>
  );
};

export default memo(TabNavigation);
