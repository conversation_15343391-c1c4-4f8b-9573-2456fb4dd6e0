import {
  createSlice,
  createSelector,
  WithSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { rootReducer } from "@/redux/reducer";
import { RootState } from "@/redux/store";
import { IDeviceParams, IEduDevice } from "@/models/eduDevice.model";
import { IPaginationModel } from "@/models/response.model";

/* ------------- State & Interface ------------- */
export interface IInitialState {
  isFetching: boolean;

  /* Danh sách dòng chứng từ */
  deviceChooseList: IEduDevice[];
  deviceOriginal: IEduDevice[];
  totalData: number;
}

const initialState: IInitialState = {
  isFetching: false,

  deviceChooseList: [],
  deviceOriginal: [],
  totalData: 0,
};

/* ------------- Selector ------------- */
export const selectDeviceChooseList = createSelector(
  [(state: RootState) => state.eduDeviceReducer?.deviceChooseList],
  (deviceChooseList) => [...(deviceChooseList ?? [])]
);
export const selectDeviceOriginalList = createSelector(
  [(state: RootState) => state.eduDeviceReducer?.deviceOriginal],
  (deviceOriginal) => [...(deviceOriginal ?? [])]
);

export const selectTotalDeviceChoose = createSelector(
  [(state: RootState) => state.eduDeviceReducer],
  (eduDeviceReducer) => eduDeviceReducer?.totalData ?? 0
);

/* ------------- Reducers ------------- */
const reducers = {
  getDevice: (state: IInitialState, action: PayloadAction<IDeviceParams>) => {},
  getDeviceSuccess: (
    state: IInitialState,
    action: PayloadAction<{ data: IEduDevice[]; totalCount: number }>
  ) => {
    state.deviceChooseList = action.payload.data;
    state.totalData = action.payload.totalCount;
  },
  chooseDevice: (
    state: IInitialState,
    action: PayloadAction<{
      data: IEduDevice;
      onSuccess: (data: IEduDevice) => void;
    }>
  ) => {},
  getDeviceOriginal: (
    state: IInitialState,
    action: PayloadAction<{
      params: IDeviceParams & IPaginationModel;
      onTotalChange: (total: number) => void;
      isReset?: boolean;
    }>
  ) => {},
  getDeviceOriginalSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      data: IEduDevice[];
      totalCount: number;
      isReset?: boolean;
    }>
  ) => {
    if (action.payload.isReset) {
      state.deviceOriginal = action.payload.data;
    } else {
      state.deviceOriginal = [...state.deviceOriginal, ...action.payload.data];
    }
  },

  resetDeviceOriginal: (state: IInitialState) => {
    state.deviceOriginal = [];
  },
  setFetching: (state: IInitialState, action: { payload: boolean }) => {
    state.isFetching = action.payload;
  },
  reset: (state: IInitialState) => {
    state.isFetching = false;
    state.deviceChooseList = [];
    state.deviceOriginal = [];
    state.totalData = 0;
  },
};

/* ------------- Selectors ------------- */
const selectors = {
  selectSlice: (state: IInitialState) => state,
  selectIsFetching: (state: IInitialState) => state.isFetching,
};

/* ------------- Slice ------------- */
export const eduDeviceSlice = createSlice({
  name: "eduDeviceReducer",
  initialState,
  reducers,
  selectors,
});

/* ------------- Export Actions ------------- */
export const eduDeviceActions = eduDeviceSlice.actions;

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices extends WithSlice<typeof eduDeviceSlice> {}
}

const injectedEduDeviceSlice = eduDeviceSlice.injectInto(rootReducer);

/* ------------- Export Selectors ------------- */
export const eduDeviceSelectors = {
  selectSlice: injectedEduDeviceSlice.selectors.selectSlice,
  selectIsFetching: createSelector(
    [injectedEduDeviceSlice.selectors.selectSlice],
    (slice) => slice.isFetching
  ),
};
