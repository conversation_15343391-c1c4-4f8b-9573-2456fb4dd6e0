import { IOption } from "@/components/common";
import { IBorrowRequest } from "@/models/eduDevice.model";

export interface IBorrowRequestAction
  extends Omit<IBorrowRequest, "schoolWeekConfigId" | "teacherId"> {
  /** danh sách id thiết bị mượn cần xoá */
  deleteBorrowRequestDeviceIds: number[];

  /** danh sách id phòng mượn cần xoá */
  deleteBorrowRequestRoomIds: number[];
  schoolWeekConfigId: IOption | null;
  teacherId: IOption | null;
}

export enum PURPOSE_USE_ROOM {
  /**
   * Thực hành
   */
  Practice = 1,

  /**
   * <PERSON><PERSON><PERSON><PERSON> diễn
   */
  Perform = 2,
}

export const PURPOSE_USE_ROOM_LIST = [
  { id: PURPOSE_USE_ROOM.Practice, label: "Thực hành" },
  { id: PURPOSE_USE_ROOM.Perform, label: "<PERSON><PERSON><PERSON><PERSON> diễn" },
];
