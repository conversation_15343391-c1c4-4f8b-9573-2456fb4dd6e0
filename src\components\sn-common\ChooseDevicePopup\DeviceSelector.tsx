"use client";

import React, { memo, useState, useCallback } from "react";
import { useFormContext } from "react-hook-form";
import { InputAdornment } from "@mui/material";
import { AppFormTextField } from "@/components/common";
import { ArrowDropDownIcon } from "@mui/x-date-pickers";
import DevicePopup from "@/components/sn-common/ChooseDevicePopup/DevicePopup";
import { formatNumber } from "@/utils/format.utils";

interface DeviceSelectorProps {}

const DeviceSelector = ({}: DeviceSelectorProps) => {
  const {
    control,
    setValue,
    formState: { errors },
  } = useFormContext();
  const [devicePopupAnchor, setDevicePopupAnchor] =
    useState<HTMLElement | null>(null);

  const handleSelectDevice = useCallback(
    (device: any) => {
      setValue("deviceId", device.id);
      setValue(
        "deviceName",
        `${device.deviceName} (${device.code} - SL: ${formatNumber(
          device.quantity
        )})`
      );
      setValue("totalAvailable", device.totalAvailable);
    },
    [setValue]
  );

  const handleOpenDevicePopup = useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      setDevicePopupAnchor(event.currentTarget);
    },
    []
  );

  const handleCloseDevicePopup = useCallback(() => {
    setDevicePopupAnchor(null);
  }, []);

  return (
    <>
      <AppFormTextField
        control={control}
        name="deviceName"
        label="Tên thiết bị"
        rules={{
          required: "Tên thiết bị không được để trống",
        }}
        textfieldProps={{
          error: !!errors.deviceName || !!errors.deviceId,
          helperText: (errors.deviceName?.message ||
            errors.deviceId?.message) as string,
          placeholder: "Chọn thiết bị",
          sx: {
            "&.MuiTextField-root .MuiInputBase-root": {
              pr: 0.5,
            },
          },
          InputProps: {
            readOnly: true,
            endAdornment: (
              <InputAdornment position="end">
                <ArrowDropDownIcon
                  sx={{
                    color: "text.primary",
                    transform: devicePopupAnchor
                      ? "rotate(180deg)"
                      : "rotate(0deg)",
                  }}
                />
              </InputAdornment>
            ),
          },
        }}
        onClick={handleOpenDevicePopup}
      />
      <DevicePopup
        isOpen={!!devicePopupAnchor}
        onClose={handleCloseDevicePopup}
        onSelectDevice={handleSelectDevice}
        anchorEl={devicePopupAnchor}
      />
    </>
  );
};

export default memo(DeviceSelector);
