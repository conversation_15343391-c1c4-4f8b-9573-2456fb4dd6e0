import React, { memo, useMemo } from "react";
import { useFieldArray, useFormContext, useWatch } from "react-hook-form";
import { IBorrowRequestAction } from "../../../borrowRequestModel";
import {
  AppFormAutocomplete,
  AppFormTextField,
  AppTable,
} from "@/components/common";
import { ColumnDef } from "@tanstack/react-table";
import { Typography } from "@mui/material";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import { useAppSelector } from "@/redux/hook";
import { selectRoomList, selectSubjectList } from "@/redux/system.slice";
import DeleteCell from "@/components/common/table/cell/DeleteCell";
import {
  BorrowStatusEnum,
  IBorrowRequestDevice,
} from "@/models/eduDevice.model";

const DeviceSelectedTable = () => {
  const { control } = useFormContext<IBorrowRequestAction>();
  const { fields, remove } = useFieldArray({
    control,
    name: "borrowRequestDevices",
  });

  const column = useMemo(() => {
    return getColumn(remove);
  }, [remove]);

  return (
    <AppTable
      columns={column}
      data={fields}
      totalData={fields.length}
      hasDefaultPagination
      {...TABLE_MODAL_FULL_HEIGHT}
    />
  );
};

export default DeviceSelectedTable;

const getColumn = (remove): ColumnDef<IBorrowRequestDevice>[] => [
  {
    id: "index",
    header: "STT",
    meta: {
      align: "center",
    },
    cell: ({ row }) => row.index + 1,
    size: 50,
  },
  {
    id: "delete",
    header: "xóa",
    size: 50,
    meta: {
      align: "center",
    },
    cell: ({ row }) => (
      <DeleteCell
        disabled={row.original.status !== BorrowStatusEnum.Register}
        onClick={() => remove(row.index)}
      />
    ),
  },
  {
    header: "Thiết bị",
    size: 200,
    cell: ({ row }) => {
      return (
        <>
          <Typography>{row.original.deviceName}</Typography>
          <Typography sx={{ color: "grey.500", fontSize: 12 }}>
            {row.original.deviceCode}
          </Typography>
        </>
      );
    },
  },
  {
    header: "Đơn vị tính",
    size: 50,
    accessorKey: "deviceUnitName",
    id: "deviceUnitName",
  },
  {
    id: "sl",
    header: "SL đăng ký",
    size: 150,
    cell: ({ row }) => (
      <QuantityInputCell
        disabled={row.original.status !== BorrowStatusEnum.Register}
        index={row.index}
        max={row.original.totalBorrowReady}
      />
    ),
  },
  {
    id: "sj",
    header: "Môn học",
    cell: ({ row }) => (
      <SubjectSelectEditForm
        disabled={row.original.status !== BorrowStatusEnum.Register}
        rowIndex={row.index}
      />
    ),
    size: 250,
  },
  {
    id: "class",
    header: "Khối lớp",
    accessorKey: "gradeName",
    size: 100,
  },
  {
    id: "room",
    header: "Kho phòng",
    cell: ({ row }) => (
      <RoomSelectEditForm
        disabled={row.original.status !== BorrowStatusEnum.Register}
        rowIndex={row.index}
      />
    ),
    size: 250,
  },
  {
    id: "empty",
    accessorKey: "empty",
    header: "",
    meta: {
      headerSx: {
        width: "100%",
      },
    },
  },
];
const QuantityInputCell = memo(
  ({
    index,
    disabled,
    max,
  }: {
    disabled?: boolean;
    index: number;
    max?: number;
  }) => {
    const { control } = useFormContext<IBorrowRequestAction>();

    return (
      <AppFormTextField
        name={`borrowRequestDevices.${index}.quantity`}
        control={control}
        textfieldProps={{
          disabled,
          type: "number",
          inputProps: { min: 1, max },
        }}
      />
    );
  }
);

export const RoomSelectEditForm = memo(
  ({ rowIndex, disabled }: { disabled?: boolean; rowIndex }) => {
    const roomList = useAppSelector(selectRoomList);

    const {
      control,
      formState: { errors },
    } = useFormContext();

    const fieldError = errors?.borrowRequestDevices?.[rowIndex]?.roomId;

    return (
      <AppFormAutocomplete
        control={control}
        name={`borrowRequestDevices.${rowIndex}.roomId`}
        autocompleteProps={{
          disabled,
          textFieldProps: {
            error: Boolean(fieldError),
            helperText: fieldError?.message,
          },
        }}
        options={roomList}
      />
    );
  }
);

export const SubjectSelectEditForm = memo(
  ({ rowIndex, disabled }: { disabled?: boolean; rowIndex }) => {
    const subjectList = useAppSelector(selectSubjectList);

    const {
      control,
      formState: { errors },
    } = useFormContext();

    const fieldError = errors?.borrowRequestDevices?.[rowIndex]?.subjectId;

    return (
      <AppFormAutocomplete
        control={control}
        name={`borrowRequestDevices.${rowIndex}.subjectId`}
        autocompleteProps={{
          disabled,
          textFieldProps: {
            error: Boolean(fieldError),
            helperText: fieldError?.message,
          },
        }}
        options={subjectList}
      />
    );
  }
);
