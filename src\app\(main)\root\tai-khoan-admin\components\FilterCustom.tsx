"use client";

import { AppAutoComplete } from "@/components/common";
import {
  DEFAULT_FILTER_SIZE,
  FilterCustomProps,
} from "@/components/common/TablePageLayout/ContentPage/HeaderFilter";
import { AppConstant, DataConstant } from "@/constant";
import {
  educationUnitsActions,
  educationUnitsSelectors,
  selectPhongList,
  selectPhongValue,
  selectSchoolLevelList,
  selectSchoolLevelValue,
  selectSoList,
  selectSoValue,
} from "@/redux/educationUnits.slice";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { CommonUtils } from "@/utils";
import { Grid } from "@mui/material";
import { memo, useCallback, useEffect, useMemo } from "react";

const FilterCustom = ({ props }: { props: FilterCustomProps }) => {
  const dispatch = useAppDispatch();
  const soList = useAppSelector(selectSoList);
  const phongList = useAppSelector(selectPhongList);
  const soValue = useAppSelector(selectSoValue);
  const phongValue = useAppSelector(selectPhongValue);
  const schoolLevelList = useAppSelector(selectSchoolLevelList);
  const schoolLevelValue = useAppSelector(selectSchoolLevelValue);

  const getFilterValue = useCallback(
    (key: string): string | undefined => {
      const raw = props?.filter?.find((item) => item.key === key)?.value;
      return typeof raw === "string"
        ? raw
        : raw !== undefined
        ? String(raw)
        : undefined;
    },
    [props?.filter]
  );

  const groupUnitCode = getFilterValue("groupUnitCode");
  const doetCode = getFilterValue("doetCode");

  const isTruong = useMemo(
    () => groupUnitCode === DataConstant.DON_VI_TYPE.truong,
    [groupUnitCode]
  );

  const isPhong = useMemo(
    () => groupUnitCode === DataConstant.DON_VI_TYPE.phong,
    [groupUnitCode]
  );

  useEffect(() => {
    if (isTruong || isPhong) {
      dispatch(educationUnitsActions.getSoList());
    }
    if (isTruong) {
      dispatch(educationUnitsActions.getSchoolLevelList());
    }
  }, [isTruong, isPhong]);

  const handleDoetChange = useCallback(
    async (_: any, data: any) => {
      if (data) {
        const doetCode = getCodeFromData(data, "code");
        props.onChangeFilter?.("doetCode")(doetCode);

        dispatch(
          educationUnitsActions.handleChangeValue({
            field: "soValue",
            value: data,
          })
        );

        dispatch(
          educationUnitsActions.getPhongList({
            doetCode,
          })
        );
        dispatch(
          educationUnitsActions.getTruongList({
            doetCode: data.code,
            ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
          })
        );
      } else {
        props.onChangeFilter?.("doetCode")(null);
        props.onChangeFilter?.("divisionCode")(null);
        props.onChangeFilter?.("schoolCode")(null);
        dispatch(
          educationUnitsActions.handleChangeValue({
            field: "soValue",
            value: null,
          })
        );
      }
    },
    [getCodeFromData, props.onChangeFilter]
  );

  const handlePhongChange = useCallback(
    async (_: any, data: any) => {
      if (data) {
        dispatch(
          educationUnitsActions.handleChangeValue({
            field: "phongValue",
            value: data,
          })
        );
        const divisionCode = getCodeFromData(data, "code");

        props.onChangeFilter?.("divisionCode")(divisionCode);
        dispatch(
          educationUnitsActions.getTruongList({
            doetCode,
            divisionCode: data?.code || undefined,
            ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
          })
        );
      } else {
        dispatch(
          educationUnitsActions.handleChangeValue({
            field: "phongValue",
            value: null,
          })
        );
        props.onChangeFilter?.("divisionCode")(null);
      }
    },
    [getCodeFromData, getFilterValue, props.onChangeFilter]
  );

  const handleSchoolLevelChange = useCallback(
    async (_: any, data: any) => {
      if (data) {
        props.onChangeFilter?.("schoolLevel")(data.id);
        dispatch(
          educationUnitsActions.handleChangeValue({
            field: "schoolLevelValue",
            value: data,
          })
        );
      } else {
        props.onChangeFilter?.("schoolLevel")(null);
        dispatch(
          educationUnitsActions.handleChangeValue({
            field: "schoolLevelValue",
            value: null,
          })
        );
      }
    },
    [props.onChangeFilter, dispatch]
  );

  return (
    <>
      {(isPhong || isTruong) && (
        <Grid size={DEFAULT_FILTER_SIZE}>
          <AppAutoComplete
            options={soList}
            label="Sở"
            onChange={handleDoetChange}
            value={soValue}
          />
        </Grid>
      )}
      {isTruong && (
        <>
          <Grid size={DEFAULT_FILTER_SIZE}>
            <AppAutoComplete
              options={phongList}
              label="Phòng"
              onChange={handlePhongChange}
              value={phongValue}
            />
          </Grid>
        </>
      )}
      {isTruong && (
        <Grid size={DEFAULT_FILTER_SIZE}>
          <AppAutoComplete
            options={schoolLevelList}
            label="Cấp học"
            onChange={handleSchoolLevelChange}
            value={schoolLevelValue}
          />
        </Grid>
      )}
    </>
  );
};

export default memo(FilterCustom);

const getCodeFromData = (data: any, key: string, fallback: string = "") => {
  if (typeof data === "object" && data !== null && key in data)
    return data[key];
  if (typeof data === "string") return data;
  return fallback;
};
