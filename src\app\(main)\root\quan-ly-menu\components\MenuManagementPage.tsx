"use client";

import MenuConfigFilter from "./MenuConfigFilter";
import { ITreeData } from "@/models/types";
import { Box, Stack, Typography } from "@mui/material";
import { AppConfirmModal } from "@/components/common";
import { useEffect, useState } from "react";
import useMenuActions from "../hooks/useMenuActions";
import MenuConfigModal from "./MenuConfigModal";
import { convertDataTreeItem } from "@/utils/tree.utils";
import TreeView from "@/components/common/tree/TreeView";
import TreeItem from "@/components/common/tree/TreeItem";
import TreeEditButton from "@/components/common/tree/TreeEditButton";
import TreeDeleteButton from "@/components/common/tree/TreeDeleteButton";
import useMenuConfig from "@/app/(main)/root/quan-ly-menu/hooks/useMenuConfig";
import { useAppSelector } from "@/redux/hook";
import { menuSelectors } from "@/app/(main)/root/quan-ly-menu/store/menu.slice";

const MenuManagementPage = () => {
  const { fetchMenuConfig } = useMenuConfig();
  const menuTreeList = useAppSelector(menuSelectors.menuConfig);
  const filter = useAppSelector(menuSelectors.filter);
  const [menuId, setMenuId] = useState<number | null>(null);
  const [menuConfigId, setMenuConfigId] = useState<number | null>(null);
  const { handleDeleteMenu } = useMenuActions();
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);

  useEffect(() => {
    if (filter.menuTypeId && filter.groupUnitCode) {
      fetchMenuConfig();
    }
  }, [filter]);

  const handleRenderIcon = (data: ITreeData) => {
    return (
      <Stack direction="row" spacing={1}>
        <TreeEditButton
          onClickButton={() => {
            setMenuConfigId(data?.id as number);
            setIsOpenModal(true);
          }}
        />
        <TreeDeleteButton
          onClickButton={() => {
            setMenuId(data.id as number);
          }}
        />
      </Stack>
    );
  };
  return (
    <Box>
      <MenuConfigFilter onOpenModal={() => setIsOpenModal(true)} />
      <Box px={2} pt={3} width={1 / 3}>
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          pb={2}
        >
          <Typography fontSize={20} whiteSpace="nowrap">
            Thư mục các tính năng
          </Typography>
        </Stack>
        <TreeView>
          <TreeItem
            data={convertDataTreeItem(menuTreeList)}
            hasMoreICon={false}
            labelItemProps={{ justifyContent: "space-between" }}
            onCustomIcon={handleRenderIcon}
          />
        </TreeView>
      </Box>
      <AppConfirmModal
        modalTitleProps={{
          title: "Bạn xác nhận muốn xóa menu này?",
        }}
        isOpen={Boolean(menuId)}
        onClose={() => {
          setMenuId(null);
        }}
        onConfirm={() => {
          handleDeleteMenu(menuId as number);
        }}
      />
      <MenuConfigModal
        isOpen={isOpenModal}
        onClose={() => {
          setIsOpenModal(false);
          setMenuConfigId(null);
        }}
        menuConfigId={menuConfigId}
      />
    </Box>
  );
};

export default MenuManagementPage;
