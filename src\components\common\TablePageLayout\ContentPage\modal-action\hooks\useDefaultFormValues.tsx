import { useMemo } from "react";
import dayjs from "dayjs";
import { DataConstant } from "@/constant";
import { FormFieldConfig } from "@/components/common/TablePageLayout/type";

const useDefaultFormValues = <T,>(formField?: FormFieldConfig<T>[]) => {
  return useMemo(() => {
    const values: Record<string, any> = {};

    formField?.forEach((field) => {
      switch (field.type) {
        case "text":
        case "number":
        case "editor":
          values[field.key] = field.defaultValue ?? "";
          break;
        case "select":
          values[field.key] = field.selectConfig?.isMulti
            ? field.defaultValue ?? []
            : null;
          break;
        case "toggle":
          values[field.key] =
            field.defaultValue ?? DataConstant.BOOLEAN_TYPE.false;
          break;
        case "date":
          values[field.key] = field.defaultValue
            ? dayjs(field.defaultValue as string)
            : null;
          break;
        default:
          values[field.key] = field.defaultValue ?? "";
      }
    });

    return values;
  }, [formField]);
};

export default useDefaultFormValues;
