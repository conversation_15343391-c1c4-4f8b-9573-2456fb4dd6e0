import { useCallback } from "react";
import { useDispatch } from "react-redux";
import {
  openCreateModal,
  openEditModal,
  openDeleteModal,
  closeModal,
} from "../../../../redux/modalAction.slice";
import { useModalConfig } from "./ModalActionContext";
import { toggleAppProgress } from "@/utils/common.utils";
import { ApiConstant } from "@/constant";
import http from "@/api";
import { toast } from "sonner";
import { DataResponseModel } from "@/models/response.model";

// Hàm lấy dữ liệu chi tiết
const getDataDetail = async <T = unknown>(
  onSuccess: (data: T) => void,
  id?: string | number,
  url?: string
) => {
  try {
    toggleAppProgress(true);

    if (!id) throw new Error("Id không xác định");
    if (!url) throw new Error("Url không xác định");

    const res: DataResponseModel<T> = await http.get(url + "/" + id);

    if (res.code === ApiConstant.ERROR_CODE_OK) {
      onSuccess(res.data);
    } else {
      throw res;
    }
  } catch (error) {
    console.error("Lỗi khi lấy chi tiết dữ liệu:", error);

    toast.error("Thất bại!", {
      description: "Lỗi khi lấy chi tiết dữ liệu",
    });
  } finally {
    toggleAppProgress(false);
  }
};

export const useModalAction = () => {
  const dispatch = useDispatch();
  const { detailUrl, formatDetailData } = useModalConfig();

  const openEdit = useCallback(
    (data?: any) => {
      dispatch(closeModal()); // Đóng modal cũ nếu có

      if (!data?.id || !detailUrl) {
        dispatch(openEditModal(data ?? null));
        return;
      }

      getDataDetail(
        (detailData) => {
          const formatted = formatDetailData
            ? formatDetailData(detailData)
            : detailData;
          dispatch(openEditModal(formatted));
        },
        data.id,
        detailUrl
      );
    },
    [dispatch, detailUrl, formatDetailData]
  );

  const openCreate = useCallback(() => {
    dispatch(openCreateModal());
  }, [dispatch]);

  const openDelete = useCallback(
    (data: any) => {
      dispatch(openDeleteModal(data));
    },
    [dispatch]
  );

  const close = useCallback(() => {
    dispatch(closeModal());
  }, [dispatch]);

  return {
    openCreateModal: openCreate,
    openEditModal: openEdit,
    openDeleteModal: openDelete,
    closeModal: close,
  };
};
