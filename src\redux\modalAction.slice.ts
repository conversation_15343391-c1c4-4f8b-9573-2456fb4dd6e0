import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export type ModalType = "create" | "edit" | "delete" | null;

interface ModalState {
  modalType: ModalType;
  modalData: any | null;
}

const initialState: ModalState = {
  modalType: null,
  modalData: null,
};

export const modalSlice = createSlice({
  name: "modal",
  initialState,
  reducers: {
    openCreateModal(state) {
      state.modalType = "create";
      state.modalData = null;
    },
    openEditModal(state, action: PayloadAction<any>) {
      state.modalType = "edit";
      state.modalData = action.payload;
    },
    openDeleteModal(state, action: PayloadAction<any>) {
      state.modalType = "delete";
      state.modalData = action.payload;
    },
    closeModal(state) {
      state.modalType = null;
      state.modalData = null;
    },
  },
});

export const { openCreateModal, openEditModal, openDeleteModal, closeModal } =
  modalSlice.actions;

export default modalSlice.reducer;
