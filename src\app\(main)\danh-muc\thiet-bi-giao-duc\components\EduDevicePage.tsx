"use client";

import { TablePageLayout } from "@/components/common";
import StatusCell from "@/components/common/table/cell/StatusCell";
import {
  FormFieldConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import {
  DEVICE_TYPE,
  DEVICE_TYPE_DTI,
  DEVICE_UNIT,
  EDU_DEVICE,
  GET_GRADE,
  STATUS_EDU_DEVICE,
  SUBJECT,
} from "@/constant/api.const";
import { ColumnDef } from "@tanstack/react-table";
import React, { useRef } from "react";
import {
  STATUS_TYPE_LIST,
  USER_TYPE,
  USER_TYPE_LIST,
} from "@/constant/data.const";
import { IEduDevice } from "@/models/eduDevice.model";
import { updateStatusService } from "@/services/app.service";
import { DataConstant } from "@/constant";
import { CheckIcon } from "@/components/icons";
import { FormatUtils } from "@/utils";
import { MANAGE_BY, MANAGE_BY_LIST } from "@/models/system.model";

const EduDevicePage = () => {
  const tableRef = useRef<ITableRef>(null);
  const columns = getColumns(tableRef);

  const cleanData = (data: IEduDevice) => {
    const newData = {
      ...data,
    };
    newData.isManageDevice =
      newData.manageBy === MANAGE_BY.isManageDevice
        ? DataConstant.BOOLEAN_TYPE.true
        : DataConstant.BOOLEAN_TYPE.false;
    newData.isManageQuantity =
      newData.manageBy === MANAGE_BY.isManageDevice
        ? DataConstant.BOOLEAN_TYPE.false
        : DataConstant.BOOLEAN_TYPE.true;
    delete newData.manageBy;
    return newData;
  };

  const formatDetailData = (data: IEduDevice) => {
    const newData = { ...data };
    if (newData.isManageDevice) newData.manageBy = MANAGE_BY.isManageDevice;
    else newData.manageBy = MANAGE_BY.isManageQuantity;

    return newData;
  };

  return (
    <TablePageLayout<IEduDevice>
      ref={tableRef}
      cleanDataFormFiled={cleanData}
      formatDetailData={formatDetailData}
      visibleCol={VISIBLE_COL}
      apiUrl={EDU_DEVICE}
      tableProps={{
        columns,
      }}
      filterConfig={[
        {
          key: "searchKey",
          type: "text",
          label: "Tìm kiếm",
          size: 2.4,
        },
        {
          key: "status",
          type: "select",
          label: "Trạng thái hiển thị",
          size: 2.4,
          options: STATUS_TYPE_LIST,
        },
      ]}
      actions={["create", "update", "delete"]}
      formConfig={{
        modalProps: {
          maxWidth: "md",
        },
        deleteUrl: EDU_DEVICE,
        detailUrl: EDU_DEVICE,
        createUrl: EDU_DEVICE,
        updateUrl: EDU_DEVICE,
        createFields: CREATE_CONFIG,
        updateFields: CREATE_CONFIG,
      }}
    />
  );
};

export default EduDevicePage;

const getColumns = (
  tableRef: React.RefObject<ITableRef | null>
): ColumnDef<IEduDevice>[] => [
  {
    id: "deviceCode",
    header: "Mã thiết bị",
    accessorKey: "deviceCode",
    size: 60,
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
    size: 150,
  },
  {
    id: "schoolDeviceTypeName",
    accessorKey: "schoolDeviceTypeName",
    header: "Loại thiết bị",
  },
  {
    id: "deviceDTITypeName",
    accessorKey: "deviceDTITypeName",
    header: "Loại thiết bị theo DTI",
  },
  {
    id: "deviceUnitName",
    accessorKey: "deviceUnitName",
    header: "Đơn vị tính",
    size: 50,
  },
  // {
  //   id: "gradeCode",
  //   accessorKey: "gradeCode",
  //   header: "Khối lớp",
  // },
  {
    id: "schoolSubjectName",
    accessorKey: "schoolSubjectName",
    header: "Môn học",
  },
  {
    id: "userTypes",
    header: "Đối tượng sử dụng",

    columns: [
      {
        id: "teacher",
        header: "Giáo viên",
        meta: {
          align: "center",
        },
        size: 20,
        cell: ({ row }) =>
          row.original.userTypes?.includes(USER_TYPE.teacher) && (
            <CheckIcon sx={{ fontSize: 24, color: "primary.main" }} />
          ),
      },
      {
        id: "student",
        header: "Học sinh",
        meta: {
          align: "center",
        },
        size: 20,
        cell: ({ row }) =>
          row.original.userTypes?.includes(USER_TYPE.student) && (
            <CheckIcon sx={{ fontSize: 24, color: "primary.main" }} />
          ),
      },
    ],
  },
  {
    id: "isSelfMade",
    header: "TB tự làm",
    accessorKey: "isSelfMade",
    size: 50,
    meta: {
      align: "center",
    },
    cell: ({ row }) =>
      Boolean(row.original.isSelfMade) && (
        <CheckIcon sx={{ fontSize: 24, color: "primary.main" }} />
      ),
  },
  {
    id: "isConsumable",
    header: "TB tiêu hao",
    accessorKey: "isConsumable",
    size: 50,
    cell: ({ row }) =>
      Boolean(row.original.isConsumable) && (
        <CheckIcon sx={{ fontSize: 24, color: "primary.main" }} />
      ),
    meta: {
      align: "center",
    },
  },
  {
    id: "followBy",
    header: "Quản lý mượn trả theo",
    accessorFn: (row) =>
      row.isManageQuantity
        ? "Số lượng"
        : row.isManageDevice
        ? "Từng thiết bị"
        : "",
  },
  {
    id: "desc",
    header: "Mô tả",
    accessorKey: "description",
    size: 250,
  },
  {
    id: "statisticCode",
    header: "Mã thống kê",
    accessorKey: "statisticCode",
  },
  {
    id: "minimumQuantity",
    header: "Số lượng tối thiểu",
    accessorFn: (row) => FormatUtils.formatNumber(row.minimumQuantity),
    meta: {
      align: "center",
    },
  },

  {
    id: "status",
    accessorKey: "status",
    header: "Hiển thị",
    size: 60,
    cell: ({ row }) => (
      <StatusCell
        status={row.original.status}
        onStatusChange={(_, value) => {
          updateStatusService({
            id: row.original.id as number,
            status: Number(value),
            onSuccess: tableRef?.current?.fetchCurrentData,
            url: STATUS_EDU_DEVICE,
          });
        }}
      />
    ),
    meta: { align: "center" },
  },
];

const VISIBLE_COL = [
  { id: "deviceCode", name: "Mã Thiết bị" },
  { id: "deviceName", name: "Tên thiết bị" },
  { id: "schoolDeviceTypeName", name: "Loại thiết bị" },
  { id: "deviceDTITypeName", name: "Loại thiết bị theo DTI" },
  { id: "deviceUnitName", name: "Đơn vị tính" },
  { id: "schoolSubjectName", name: "Môn học" },
  { id: "student", name: "Giáo viên" },
  { id: "teacher", name: "Học sinh" },
  { id: "isSelfMade", name: "TB tự làm" },
  { id: "isConsumable", name: "TB tiêu hao" },
  { id: "followBy", name: "Quản lý mượn trả theo" },
  { id: "desc", name: "Mô tả" },
  { id: "statisticCode", name: "Mã thống kê" },
  { id: "minimumQuantity", name: "Số lượng tối thiểu" },
  { id: "status", name: "Hiển thị" },
];

const CREATE_CONFIG: FormFieldConfig<IEduDevice>[] = [
  {
    key: "statisticCode",
    type: "text",
    label: "Mã thống kê",
    size: 5,
    fieldProps: {
      direction: "row",
      labelProps: {
        sx: {
          minWidth: 130,
        },
      },
    },
  },
  {
    key: "deviceCode",
    type: "text",
    label: "Mã thiết bị",
    size: 7,
    rules: { required: "Mã thiết bị không được để trống" },
    fieldProps: {
      direction: "row",
      labelProps: {
        sx: {
          minWidth: 80,
        },
      },
    },
  },
  {
    key: "deviceName",
    type: "text",
    label: "Tên thiết bị",
    rules: { required: "Tên thiết bị không được để trống" },
    size: 12,
    fieldProps: {
      direction: "row",
      labelProps: {
        sx: {
          minWidth: 130,
        },
      },
    },
  },
  {
    key: "deviceUnitId",
    type: "select",
    label: "Đơn vị tính",
    rules: { required: "Đơn vị tính không được để trống" },
    size: 5,
    apiListUrl: DEVICE_UNIT,
    fieldProps: {
      direction: "row",
      labelProps: {
        sx: {
          minWidth: 130,
        },
      },
    },
  },
  {
    key: "schoolDeviceTypeId",
    type: "select",
    label: "Loại thiết bị",
    size: 7,
    apiListUrl: DEVICE_TYPE,
    fieldProps: {
      direction: "row",
      labelProps: {
        sx: {
          minWidth: 80,
        },
      },
    },
  },
  {
    key: "deviceDTITypeId",
    type: "select",
    label: "Loại thiết bị theo DTI",
    size: 12,
    apiListUrl: DEVICE_TYPE_DTI,
    fieldProps: {
      direction: "row",
      labelProps: {
        sx: {
          minWidth: 130,
        },
      },
    },
  },
  {
    key: "schoolSubjectId",
    type: "select",
    label: "Môn học",
    size: 5,
    apiListUrl: SUBJECT,
    fieldProps: {
      direction: "row",
      labelProps: {
        sx: {
          minWidth: 130,
        },
      },
    },
  },
  {
    key: "gradeCodes",
    type: "select",
    label: "Khối lớp",
    size: 7,
    apiListUrl: GET_GRADE,
    selectConfig: {
      isMulti: true,
      valueKey: "code",
    },
    fieldProps: {
      direction: "row",
      labelProps: {
        sx: {
          minWidth: 80,
        },
      },
    },
  },
  {
    key: "userTypes",
    type: "select",
    label: "Đối tượng sử dụng",
    size: 12,
    selectConfig: {
      options: USER_TYPE_LIST,
      isMulti: true,
    },
    fieldProps: {
      direction: "row",
      labelProps: {
        sx: {
          minWidth: 130,
        },
      },
    },
  },
  {
    key: "manageBy",
    type: "radio",
    label: "Quản lý theo",
    size: 5,
    defaultValue: MANAGE_BY.isManageQuantity,
    selectConfig: {
      options: MANAGE_BY_LIST,
    },
  },
  {
    key: "minimumQuantity",
    type: "number",
    label: "Số lượng tối thiểu",
    size: 7,
  },
  {
    key: "isConsumable",
    type: "toggle",
    label: "Là thiết bị tiêu hao",
    size: 4,
    defaultValue: DataConstant.STATUS_TYPE.inActive,
  },
  {
    key: "isSelfMade",
    type: "toggle",
    label: "Là thiết bị tự làm",
    size: 4,
    defaultValue: DataConstant.STATUS_TYPE.inActive,
  },
  {
    key: "status",
    type: "toggle",
    label: "Trạng thái hiển thị",
    size: 4,
    defaultValue: DataConstant.STATUS_TYPE.active,
  },
  {
    key: "description",
    type: "text",
    label: "Mô tả",
    textFieldProps: {
      multiline: true,
      minRows: 3,
    },
    fieldProps: {
      direction: "row",
      labelProps: {
        sx: {
          minWidth: 130,
        },
      },
    },
  },
];
