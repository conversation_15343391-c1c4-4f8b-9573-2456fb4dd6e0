import http from "@/api";
import { ApiConstant } from "@/constant";
import { DataListResponseModel } from "@/models/response.model";
import {
  IClass,
  ICountry,
  IDeviceType,
  IGrade,
  IPeriod,
  IRoom,
  ISchoolWeek,
  ISource,
  ISubject,
  ITeacher,
  IUnit,
} from "@/models/system.model";

export const getSourceListService = () => {
  return http.get<DataListResponseModel<ISource>>(ApiConstant.SOURCE);
};

export const getUnitListService = () => {
  return http.get<DataListResponseModel<IUnit>>(ApiConstant.DEVICE_UNIT);
};

export const getDeviceTypeListService = () => {
  return http.get<DataListResponseModel<IDeviceType>>(ApiConstant.DEVICE_TYPE);
};

export const getSubjectListService = () => {
  return http.get<DataListResponseModel<ISubject>>(ApiConstant.SUBJECT);
};

export const getClassListService = () => {
  return http.get<DataListResponseModel<IClass>>(ApiConstant.CLASS);
};

export const getGradeListService = () => {
  return http.get<DataListResponseModel<IGrade>>(ApiConstant.GET_GRADE);
};

export const getRoomListService = () => {
  return http.get<DataListResponseModel<IRoom>>(ApiConstant.ROOM);
};

export const getTeacherComboListService = () => {
  return http.get<DataListResponseModel<ITeacher>>(ApiConstant.TEACHER_COMBO);
};

export const getCountryListService = () => {
  return http.get<DataListResponseModel<ICountry>>(ApiConstant.COUNTRY);
};

export const getPeriodListService = () => {
  return http.get<DataListResponseModel<IPeriod>>(ApiConstant.GET_PERIOD);
};

export const getSchoolWeekListService = () => {
  return http.get<DataListResponseModel<ISchoolWeek>>(ApiConstant.SCHOOL_WEEK);
};
