import { CommonUtils } from "@/utils";
import { useEffect } from "react";

const useConfigHeightOfTableInModal = (classModal?: string) => {
  useEffect(() => {
    handleConfigHeightOfTableInModal(classModal);
  }, [classModal]);
};

export default useConfigHeightOfTableInModal;

export const handleConfigHeightOfTableInModal = (classModal?: string) => {
  const tableModalEl: HTMLElement = document.querySelector(
    `${classModal || ".table-modal"} #table`
  ) as HTMLElement;

  const footerModalEl: HTMLElement = CommonUtils.getElementById(
    "footer-modal"
  ) as HTMLElement;

  if (tableModalEl && footerModalEl) {
    const headerModalEl: HTMLElement = CommonUtils.getElementById(
      "modal-filter"
    ) as HTMLElement;
    const footerModalTop = footerModalEl.getBoundingClientRect().top;
    const titleModalTop = headerModalEl?.getBoundingClientRect?.()?.bottom || 0;
    const paginationHeight = 40;
    const spacing = 16;
    const paddingBottomModal = 16;

    const resultHeight =
      footerModalTop -
      paginationHeight -
      spacing -
      titleModalTop -
      paddingBottomModal;

    tableModalEl.style.height = `${resultHeight}px`;
  }
};
