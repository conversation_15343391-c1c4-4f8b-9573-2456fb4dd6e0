import { memo, useState } from "react";
import { Grid } from "@mui/material";
import { AppDateRangePicker } from "@/components/common";
import { DateRangeValue } from "@/models/types";
import { DateObject } from "react-multi-date-picker";
import dayjs from "dayjs";

interface TimeRangeSelectorProps {
  onDateChange?: (fromDate: Date, toDate: Date) => void;
}

const TimeRangeSelector = ({ onDateChange }: TimeRangeSelectorProps) => {
  const [fromDate, setFromDate] = useState<Date | null>(dayjs().toDate());
  const [toDate, setToDate] = useState<Date | null>(dayjs().toDate());

  const handleDateRangeChange = (dates: DateRangeValue) => {
    const [startDate, endDate] = dates;
    setFromDate(startDate);
    setToDate(endDate);

    if (startDate && endDate && onDateChange) {
      onDateChange(startDate, endDate);
    }
  };

  return (
    <AppDateRangePicker
      key={`${fromDate?.getTime()}-${toDate?.getTime()}`}
      onDateChange={handleDateRangeChange}
      label="Khoảng thời gian"
      value={[fromDate || dayjs().toDate(), toDate || dayjs().toDate()]}
      maxDate={undefined}
    />
  );
};

export const DEFAULT_DATE = [dayjs().toDate(), dayjs().toDate()];

export default memo(TimeRangeSelector);
