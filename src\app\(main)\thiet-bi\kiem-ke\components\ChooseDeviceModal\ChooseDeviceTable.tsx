import { useAppDispatch, useAppSelector } from "@/redux/hook";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import { Grid, Stack, Typography } from "@mui/material";
import {
  AppCheckbox,
  AppSearchDebounceTextFiled,
  AppTable,
  IOption,
} from "@/components/common";
import { ColumnDef } from "@tanstack/react-table";
import { AppConstant } from "@/constant";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import {
  inventoryTransactionActions,
  inventoryTransactionSelectors,
} from "@/app/(main)/thiet-bi/kiem-ke/inventoryTransaction.slice";
import { IInventoryDeviceParams } from "@/app/(main)/thiet-bi/kiem-ke/type";
import { IDevice } from "@/models/eduDevice.model";

const ChooseDeviceTable = () => {
  const dispatch = useAppDispatch();
  const devices = useAppSelector(inventoryTransactionSelectors.chooseDevices);
  const filterChooseDevices = useAppSelector(
    inventoryTransactionSelectors.filterChooseDevices
  );
  const totalData = useAppSelector(
    inventoryTransactionSelectors.totalChooseDevices
  );
  const deviceSelected = useAppSelector(
    inventoryTransactionSelectors.selectedIds
  );
  const deviceChooseList = useAppSelector(
    inventoryTransactionSelectors.chooseDevices
  );

  const devicesInPage = useMemo(() => {
    return deviceChooseList.filter((item) => deviceSelected.includes(item.id));
  }, [deviceChooseList, deviceSelected]);

  const [pagination, setPagination] = useState(
    AppConstant.DEFAULT_PAGINATION_SKIP_TAKE
  );

  const columns = useMemo(
    () => getColumns({ skip: pagination.skip }),
    [pagination.skip]
  );

  const handleChangeFilterWithKey = useCallback(
    (key: keyof IInventoryDeviceParams) => (value) => {
      dispatch(
        inventoryTransactionActions.changeFilterChooseDevices({
          key,
          value,
        })
      );
    },
    []
  );

  useEffect(() => {
    dispatch(
      inventoryTransactionActions.getChooseDevice({
        ...filterChooseDevices,
        ...pagination,
      })
    );
  }, [filterChooseDevices, pagination]);

  return (
    <Stack spacing={1} height="100%">
      <Grid container spacing={2}>
        <Grid size={3}>
          <AppSearchDebounceTextFiled
            label="Tìm kiếm"
            valueInput={filterChooseDevices.searchKey ?? ""}
            onChangeValue={handleChangeFilterWithKey("searchKey")}
          />
        </Grid>
        <Grid size={9}>
          <Stack
            height={"100%"}
            direction="row"
            alignItems="flex-end"
            justifyContent="flex-end"
          >
            <Typography color="primary">
              {devicesInPage.length}/{totalData} thiết bị đã được chọn
            </Typography>
          </Stack>
        </Grid>
      </Grid>
      <AppTable
        columns={columns}
        totalData={totalData}
        data={devices}
        paginationData={pagination}
        onPageChange={setPagination}
        {...TABLE_MODAL_FULL_HEIGHT}
      />
    </Stack>
  );
};

export default memo(ChooseDeviceTable);

const getColumns = ({ skip }: { skip: number }): ColumnDef<IDevice>[] => [
  {
    id: "index",
    header: "STT",
    cell: ({ row }) => row.index + skip + 1,
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "select",
    accessorKey: "select",
    meta: {
      align: "center",
    },
    size: 50,
    header: ({ table }) => <HeaderCheckbox table={table} />,
    cell: ({ row }) => <RowCheckbox row={row} />,
  },
  {
    id: "deviceCode",
    header: "Mã",
    accessorKey: "deviceCode",
    size: 60,
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
  },
  {
    id: "schoolDeviceTypeName",
    header: "Loại thiết bị",
    accessorKey: "schoolDeviceTypeName",
    size: 100,
  },
  {
    id: "gradeName",
    header: "Khối/Lớp",
    accessorKey: "gradeName",
    size: 100,
  },
  {
    id: "roomName",
    header: "Kho/Phòng",
    accessorKey: "roomName",
    size: 100,
  },
  {
    id: "schoolSubjectName",
    header: "Môn học",
    accessorKey: "schoolSubjectName",
    size: 100,
  },
];

const HeaderCheckbox = ({ table }) => {
  const dispatch = useAppDispatch();
  const devices = useAppSelector(inventoryTransactionSelectors.inventoryItems);
  const deviceSelected = useAppSelector(
    inventoryTransactionSelectors.selectedIds
  );
  const deviceChooseList = useAppSelector(
    inventoryTransactionSelectors.chooseDevices
  );

  const idsExists = useMemo(() => {
    return devices.map((item) => item.deviceId);
  }, [devices]);

  const idsExistsAll = useMemo(() => {
    return (
      deviceChooseList.every((item) => idsExists.includes(item.id)) &&
      deviceChooseList.length !== 0
    );
  }, [deviceChooseList]);

  const isCheckedAll = useMemo(() => {
    return (
      deviceChooseList.every((item) => deviceSelected.includes(item.id)) &&
      deviceChooseList.length !== 0
    );
  }, [deviceChooseList, deviceSelected]);

  const handleCheckedAll = (_, checked: boolean) => {
    table.toggleAllPageRowsSelected(!!checked);
    dispatch(
      inventoryTransactionActions.setSelectedAllIds({
        checked,
      })
    );
  };

  return (
    <AppCheckbox
      checked={isCheckedAll || idsExistsAll}
      onChange={handleCheckedAll}
      disabled={idsExistsAll}
    />
  );
};

const RowCheckbox = ({ row }) => {
  const dispatch = useAppDispatch();
  const deviceSelected = useAppSelector(
    inventoryTransactionSelectors.selectedIds
  );
  const devices = useAppSelector(inventoryTransactionSelectors.inventoryItems);

  const idsExists = useMemo(() => {
    return devices.map((item) => item.deviceId);
  }, [devices]);

  const handleCheckedRow = (id) => {
    dispatch(inventoryTransactionActions.setSelectedIds(id));
  };

  const isChecked = useMemo(() => {
    return deviceSelected.includes(row.original.id);
  }, [deviceSelected, row.original.id]);

  return (
    <AppCheckbox
      onChange={(_, value) => handleCheckedRow(row.original.id)}
      checked={isChecked || idsExists.includes(row.original.id)}
      disabled={idsExists.includes(row.original.id)}
    />
  );
};
