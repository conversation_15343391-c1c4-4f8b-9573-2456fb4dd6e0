import { DataConstant } from "@/constant";
import { MANAGE_BY } from "./system.model";
import { IOption } from "@/components/common";

export interface IEduDevice {
  /** ID thiết bị */
  id?: number | string;

  /** Mã thống kê */
  statisticCode: string;

  /** Mã thiết bị */
  deviceCode: string;

  /** Tên thiết bị */
  deviceName: string;

  /** Mô tả chi tiết */
  description?: string;

  /** Đơn vị tính */
  deviceUnitId: number;

  /** Tên Đơn vị tính */
  deviceUnitName?: string;

  /** <PERSON><PERSON><PERSON> thiết bị */
  schoolDeviceTypeId: number;

  /** Tên <PERSON> thiết bị */
  schoolDeviceTypeName?: string;

  /** Loại thiết bị theo DTI */
  deviceDTITypeId: number | null;

  /** Tên Loạ<PERSON> thiết bị theo DTI */
  deviceDTITypeName?: string;

  /** <PERSON><PERSON><PERSON> họ<PERSON> */
  schoolSubjectId: number;

  /** Tên <PERSON> h<PERSON> */
  schoolSubjectName?: string;

  /** Danh sách khối */
  gradeCodes: (number | string)[];

  /** Khối lớp */
  gradeCode?: string;
  gradeName?: string;

  /** Đối tượng sử dụng */
  userType?: string;

  /** Danh sách Đối tượng sử dụng: 1 => giáo viên, 2 => học sinh */
  userTypes: (number | string)[];

  /** Quản lý theo số lượng */
  isManageQuantity: DataConstant.BOOLEAN_TYPE;

  /** Quản lý theo Tường thiết bị */
  isManageDevice: DataConstant.BOOLEAN_TYPE;

  /** Là thiết bị tiêu hao */
  isConsumable: DataConstant.BOOLEAN_TYPE;

  /** Là thiết bị tự làm */
  isSelfMade: DataConstant.BOOLEAN_TYPE;

  /** Trạng thái */
  status: DataConstant.STATUS_TYPE;

  /** Số lượng tối thiểu */
  minimumQuantity: number;

  /** Danh sách thiết bị con */
  devices?: IDeviceItem[];

  /** Số đăng ký thiết bị */
  maxIndexItem?: number;

  /** Gán thêm nếu cần biết loại quản lý nào (client-only) */
  manageBy?: MANAGE_BY;

  /** Id gốc từ be (only client) */
  idOriginal?: number | string;

  roomName?: string;
  code?: string;
  roomId?: number | null;
  totalAvailable?: number;
  totalBorrowReady?: number;
}

export interface IDeviceParams {
  searchKey?: string;
  status?: DataConstant.STATUS_TYPE;
  isLost?: boolean;
  isBroken?: boolean;
  isAvailable?: boolean;
  roomId?: number | IOption;
  schoolSubjectId?: number | IOption;
  gradeCode?: number | IOption;
  deviceDefinitionId?: number;
  deviceDefinitionIds?: number[];
}

export interface IDeviceItem {
  id?: number | string;
  code: string;
  deviceDefinitionId?: number | string;

  roomId: number | null;
  teacherId?: number | null;
  teacherName?: string;
  quantity: number;
  price: number;
  countryId: number | null;
  countryName?: string;
  entryDate: Date | null;
  serial: string;
  expireDate: Date | null;
  deviceTransactionItemId: number;

  documentEntryId?: number;
  codeIndex?: number;

  deviceName?: string;
  deviceCode?: string;
  roomName?: string;
  deviceUnitName?: string;
  totalPrices?: number;

  // Id gốc api trả về thêm ở client
  itemId?: number | string;
}

export interface IDevice {
  id: number;
  code: string;
  deviceTransactionId: number;
  deviceDefinitionId: number;
  roomId: number;
  roomName: string;
  teacherId: number;
  teacherName: string;
  quantity: number;
  price: number;
  totalPrices: number;
  countryId: number;
  countryName: string;
  entryDate: string;
  serial: string;
  expireDate: string;
  totalBroken: number;
  totalLost: number;
  totalAvailable: number;
  totalBorrowReady?: number;
  totalRegister: number;
  totalBorrowing: number;
  statisticCode: string;
  deviceCode: string;
  deviceName: string;
  deviceUnitId: number;
  deviceUnitName: string;
  schoolDeviceTypeId: number;
  schoolDeviceTypeName: string;
  deviceDTITypeId: number;
  deviceDTITypeName: string;
  schoolSubjectId: number;
  schoolSubjectName: string;
  gradeCodes: number[];
  gradeName: string;
  gradeCode: string;
  userType: string;
  userTypes: number[];
  documentNumber: string;
  documentDate: string;
  deviceTransactionItemId: number;
  transactionTotalBroken: number;
  transactionTotalLost: number;
  transactionTotalAvailable: number;
}

export enum BorrowStatusEnum {
  /// <summary>
  /// Đăng ký
  /// </summary>
  Register,

  /// <summary>
  /// Đã duyệt (đang mượn)
  /// </summary>
  Borrowing,

  /// <summary>
  /// Đã trả
  /// </summary>
  Returned,

  /// <summary>
  /// Từ chối
  /// </summary>
  Reject,
}

export interface IBorrowRequest {
  id?: number;
  /** từ ngày */
  borrowFromDate: Date;

  /** đến ngày */
  borrowToDate: Date;

  /** kế hoạch */
  borrowType?: number;

  /** id giáo viên */
  teacherId: number;

  /** tên giáo viên */
  teacherName: string;

  /** id cấu hình tuần học */
  schoolWeekConfigId?: number;

  /** tên cấu hình tuần học */
  schoolWeekConfigName?: string;

  /** danh sách chi tiết mượn thiết bị */
  borrowRequestDevices: IBorrowRequestDevice[];

  /** danh sách chi tiết mượn phòng */
  borrowRequestRooms: IBorrowRequestRoom[];
}

export interface IBorrowRequestDevice {
  id?: number | string;

  /** id thiết bị */
  deviceId: number;

  /** mã thiết bị */
  deviceCode: string;

  /** tên thiết bị */
  deviceName: string;

  /** id môn học */
  subjectId?: number | IOption;

  /** tên môn học */
  subjectName?: string;

  /** id kho phòng */
  roomId?: number | IOption;

  /** tên kho phòng */
  roomName?: string;

  /** từ ngày */
  fromDate: Date;

  /** đến ngày */
  toDate: Date;

  /** số lượng */
  quantity: number;

  /** guid thiết bị phòng */
  roomDeviceGuid?: string | null;

  borrowType?: BORROW_TYPE;

  deviceUnitName?: string;
  deviceUnitId?: number;
  gradeName?: string;
  status: BorrowStatusEnum;

  /** Số lượng sẵn sàng cho mượn */
  totalBorrowReady?: number;

  parentId?: string | number;
}

export interface IBorrowRequestRoom {
  id?: number | string;

  /** id phòng */
  roomId: number | null;

  /** tên phòng */
  roomName: string;

  /** danh sách tiết học */
  periodIds: number[];

  /** id môn học */
  subjectId?: number | null;

  /** tên môn học */
  subjectName?: string;

  /** danh sách lớp học */
  schoolClassIds: number[];

  /** từ ngày */
  fromDate: Date;

  /** đến ngày */
  toDate: Date;

  /** guid thiết bị phòng */
  roomDeviceGuid?: string | null;

  /** Mục đích */
  purpose: IOption | null;

  borrowType?: BORROW_TYPE;

  status: BorrowStatusEnum;
}

export interface IBorrowRoom {
  id: number;
  borrowRequestId: number;
  teacherId: number;
  teacherName: string;
  roomId: number;
  roomName: string;
  periodIds: number[];
  periodName: string;
  subjectId: number;
  subjectName: string;
  classId: string;
  schoolClassIds: number[];
  schoolClassName: string;
  s1: number;
  s2: number;
  s3: number;
  s4: number;
  s5: number;
  c1: number;
  c2: number;
  c3: number;
  c4: number;
  c5: number;
  fromDate: string;
  toDate: string;
  roomDeviceGuid: string;
  purpose: number;
  purposeName: string;
  attachedDeviceName: string;
  status: number;
  statusName: string;
  notes: string;
}

export enum BORROW_TYPE {
  week = 1,
  longTerm,
}

export const BORROW_TYPE_LIST = [
  {
    id: BORROW_TYPE.week,
    label: "Theo tuần",
  },
  {
    id: BORROW_TYPE.longTerm,
    label: "Dài hạn",
  },
];
