import { ITableRef } from "@/components/common/TablePageLayout/type";
import { DataConstant } from "@/constant";
import { IStyleProps } from "@/models/types";
import { educationUnitsActions } from "@/redux/educationUnits.slice";
import { useAppDispatch } from "@/redux/hook";
import { Stack, Tab, Tabs } from "@mui/material";
import { ReactNode, RefObject, useMemo, useState } from "react";

const TabsFilterLayout = ({
  tableRef,
  children,
}: {
  tableRef: RefObject<ITableRef | null>;
  children: ReactNode;
}) => {
  const dispatch = useAppDispatch();
  const [valueTab, setValueTab] = useState(DataConstant.DON_VI_TYPE.truong);

  const unitLevelList = useMemo(() => {
    const result: typeof DataConstant.DON_VI_LIST = [];
    for (const item of DataConstant.DON_VI_LIST) {
      if (item.id !== DataConstant.DON_VI_TYPE.bo) {
        result.unshift(item);
      }
    }
    return result;
  }, []);

  const handleChangeTab = (_: React.SyntheticEvent, value: any) => {
    setValueTab(value);
    tableRef.current?.handleChangeFilter?.("groupUnitCode")(value);
    dispatch(educationUnitsActions.clearSoPhongTruongValue());
  };

  return (
    <>
      <Stack bgcolor="#ecedf1" px={3}>
        <Tabs onChange={handleChangeTab} value={valueTab} sx={style.appTabMain}>
          {unitLevelList.map((item) => (
            <Tab
              label={item.label || item?.name}
              key={item.id}
              value={item.id}
              iconPosition="start"
              sx={style.appTabChild}
            />
          ))}
        </Tabs>
      </Stack>
      {children}
    </>
  );
};

export default TabsFilterLayout;

const style: IStyleProps = {
  boxTab: {
    mt: 1,
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  appTabMain: {
    maxWidth: "100%",
    "& .MuiTabScrollButton-root.Mui-disabled": {
      display: "none",
    },
  },
  appTabChild: {
    overflow: "visible",
    position: "relative",
    fontSize: "16px",
    fontWeight: 500,
    letterSpacing: "0.2px",
    color: "text.primary",
    whiteSpace: "nowrap",
  },
};
