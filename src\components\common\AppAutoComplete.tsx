import {
  Autocomplete,
  AutocompleteProps,
  AutocompleteRenderOptionState,
  Checkbox,
  Chip,
  MenuItem,
  TextFieldProps,
  Typography,
} from "@mui/material";
import { forwardRef, memo, useCallback, useMemo } from "react";
import AppTextField from "./AppTextField";
import { AppConstant, DataConstant } from "@/constant";

const AppAutoComplete = forwardRef(
  (
    {
      sx,
      label,
      textFieldProps,
      options,
      multiple,
      hasAllOption = false,
      onChange,
      value,
      ...otherProps
    }: AppAutoCompleteProps,
    ref
  ) => {
    const handleCustomIsValue = (option: IOption, valueObj: IOption) => {
      if (valueObj?.id) {
        return option?.id === valueObj?.id;
      } else if (valueObj?.code) {
        return option?.code === valueObj?.code;
      } else {
        return option?.label === valueObj?.label;
      }
    };

    const valueData = useMemo(() => {
      if (
        multiple &&
        hasAllOption &&
        Array.isArray(value) &&
        value.length === options.length &&
        options.length > 0
      ) {
        return [OptionAll, ...value];
      }
      return value;
    }, [value, multiple, hasAllOption, options]);

    const handleChangeOption = useCallback(
      (e, selectedOptions, reason, details) => {
        if (!multiple || !hasAllOption) {
          onChange?.(e, selectedOptions, reason, details);
          return;
        }

        let newValue: IOption[] = [];

        const isArray = Array.isArray(selectedOptions);
        const selectedList = isArray ? selectedOptions : [];

        const isSelectAll = details?.option?.id === OptionAll.id;

        if (reason === "selectOption") {
          if (isSelectAll) {
            newValue = [...options];
          } else {
            newValue =
              selectedList.length === options.length
                ? [...options]
                : selectedList.filter((opt) => opt.id !== OptionAll.id);
          }
        } else if (reason === "removeOption") {
          if (isSelectAll) {
            newValue = [];
          } else {
            newValue = selectedList.filter((opt) => opt.id !== OptionAll.id);
          }
        } else if (reason === "clear") {
          newValue = [];
        } else {
          newValue = selectedList;
        }

        onChange?.(e, newValue, reason, details);
      },
      [options, onChange, hasAllOption, multiple]
    );

    const renderTags = useCallback(
      (value, getItemProps) => {
        if (multiple && Array.isArray(value) && value.length > 0) {
          const allOptionFind = value.find(
            (item) => (item as IOption).id === OptionAll.id
          );

          if (allOptionFind && value.length === 1) {
            return [];
          }
          if (hasAllOption && allOptionFind) {
            return [
              <Chip
                {...getItemProps({
                  index: value.indexOf(allOptionFind),
                })}
                key={allOptionFind.id}
                label={allOptionFind.label}
              />,
            ];
          }
          return value.map((item) => (
            <Chip
              {...getItemProps({ index: value.indexOf(item) })}
              key={(item as IOption).id}
              label={(item as IOption).label}
            />
          ));
        }
      },
      [hasAllOption, multiple]
    );

    return (
      <Autocomplete
        options={hasAllOption && multiple ? [OptionAll, ...options] : options}
        ref={ref}
        getOptionLabel={(option) => {
          if (typeof option === "string") return option.toString();

          if (
            typeof option === "object" &&
            options.find(
              (item) =>
                item.id === option.id ||
                item.code === option.code ||
                item._id === option._id
            )
          ) {
            return option.label || "";
          }

          return "";
        }}
        renderTags={renderTags}
        value={valueData}
        isOptionEqualToValue={handleCustomIsValue}
        fullWidth
        size="medium"
        loadingText="Đang tải..."
        noOptionsText="Không có dữ liệu"
        clearText="Xóa"
        closeText="Đóng"
        classes={{
          listbox: "custom-scrollbar",
        }}
        sx={{
          "& .MuiInputBase-root": {
            py: "0 !important",
            minHeight: 36,
            height: "fit-content !important",
          },
          "&.MuiAutocomplete-hasClearIcon .MuiOutlinedInput-root.MuiInputBase-sizeSmall":
            {
              paddingRight: "50px",
            },
          "&&& .MuiInputBase-input": {
            py: "0",
            minWidth: 5,
          },
          "&& .MuiAutocomplete-endAdornment": {
            top: "50%",
            right: 0,
            transform: "translateY(-50%)",
            "& .MuiAutocomplete-clearIndicator svg": {
              fontSize: 14,
            },
          },
          "&+.MuiAutocomplete-popper": {
            "& .MuiAutocomplete-paper": {
              minWidth: "fit-content",
              "& .MuiAutocomplete-listbox .MuiAutocomplete-option": {
                whiteSpace: "nowrap",
                "&.MuiAutocomplete-option": {
                  backgroundColor: "common.white",
                },
                "&:hover": {
                  color: "primary.main",
                  backgroundColor: "common.white",
                },
              },
            },
          },
          "& .MuiAutocomplete-tag": {
            maxWidth: "calc(100% - 43px)",
          },
          ...sx,
        }}
        disableCloseOnSelect={multiple}
        multiple={multiple}
        renderInput={(params) => (
          <AppTextField {...params} label={label} {...textFieldProps} />
        )}
        renderOption={multiple ? renderOptionMultiType : renderOption}
        onChange={handleChangeOption}
        {...otherProps}
      />
    );
  }
);

export interface IOption {
  schoolCode?: string;
  code?: number | string | null;
  id: string | number;
  _id?: string | number;
  schoolLevel?: DataConstant.SCHOOL_LEVEL;
  label: string;
  [x: string]: any;
}

export const OptionAll: IOption = {
  id: AppConstant.DEFAULT_ALL_VALUE,
  label: "Tất cả",
};

export const DEFAULT_UNIX = "id";

AppAutoComplete.displayName = "AppAutoComplete";

export type AppAutoCompleteProps = Omit<
  AutocompleteProps<
    IOption,
    boolean | undefined,
    boolean | undefined,
    boolean | undefined
  >,
  "renderInput" | "renderOption"
> & {
  label?: string;
  textFieldProps?: TextFieldProps;
  hasAllOption?: boolean;
};

const renderOption = (
  props: React.HTMLAttributes<HTMLLIElement> & {
    key: any;
  },
  option: IOption,
  { selected }: AutocompleteRenderOptionState
) => {
  return (
    <MenuItem
      {...props}
      key={[option?.id, option?.code, option?.value].filter(Boolean).join("_")}
    >
      {option?.label}
    </MenuItem>
  );
};

const renderOptionMultiType = (
  props: React.HTMLAttributes<HTMLLIElement> & {
    key: any;
  },
  option: IOption,
  { selected }: AutocompleteRenderOptionState
) => {
  const { key, ...otherProps } = props;

  return (
    <li
      key={option.id}
      {...otherProps}
      style={{
        backgroundColor: option.id === OptionAll.id ? "#f0f0f0" : "white",
      }}
    >
      <Checkbox sx={{ mr: 1, width: 24, height: 24 }} checked={selected} />
      <Typography fontWeight={option.id === OptionAll.id ? 700 : 400}>
        {option.label}
      </Typography>
    </li>
  );
};

export const getOptionId = (option?: IOption | null | number) =>
  ((option as IOption)?.id as number) ?? null;
export const getOptionCode = (option?: IOption | null | number) =>
  (option as IOption)?.code ?? "";
export const getOptionLabel = (option?: IOption | null | number) =>
  (option as IOption)?.label ?? "";
export const toOption = (id?: number | null, label?: string): IOption | null =>
  id != null ? { id, label: label ?? "" } : null;
export const mapIdsToOptions = (
  ids: (number | string)[] = [],
  source: IOption[] = []
) =>
  ids
    .map((id) => source.find((opt) => opt.id === id))
    .filter((opt): opt is IOption => Boolean(opt));

export default memo(AppAutoComplete);
