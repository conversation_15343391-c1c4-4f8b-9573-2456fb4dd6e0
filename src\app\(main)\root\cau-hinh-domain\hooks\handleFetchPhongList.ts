import http from "@/api";
import { ApiConstant } from "@/constant";
import { DataListResponseModel } from "@/models/response.model";
import { convertDataToOptions } from "@/utils/format.utils";

export const handleFetchPhongList = async (doetCode: string) => {
  try {
    const response = await http.get<DataListResponseModel<any>>(
      ApiConstant.GET_DIVISION_LIST,
      { params: { doetCode } }
    );
    return response.code === ApiConstant.ERROR_CODE_OK
      ? convertDataToOptions(response.data.data)
      : [];
  } catch (error) {
    console.error("Error fetching Phong list:", error);
    return [];
  }
};
