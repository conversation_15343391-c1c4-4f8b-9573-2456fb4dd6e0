import {
  deviceActions,
  selectorTransferDevice,
  selectorTransferInputByDeviceId,
} from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.slice";
import {
  AppAutoComplete,
  AppTable,
  AppTextField,
  IOption,
} from "@/components/common";
import DeleteCell from "@/components/common/table/cell/DeleteCell";
import { AppConstant } from "@/constant";
import { IDevice } from "@/models/eduDevice.model";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { selectRoomList } from "@/redux/system.slice";
import { CommonUtils } from "@/utils";
import { ColumnDef } from "@tanstack/react-table";
import { memo, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";

const TransferDeviceTable = () => {
  const dispatch = useAppDispatch();
  const data = useAppSelector(selectorTransferDevice);

  const columns = getColumns({
    onDelete: (id) => {
      dispatch(deviceActions.deleteTransferDevice(id));
    },
  });

  useEffect(() => {
    handleConfigHeightOfTableInModal();
  }, []);

  return (
    <AppTable
      columns={columns}
      data={data}
      className="table-modal"
      totalData={data.length}
      hasDefaultPagination
    />
  );
};

export default memo(TransferDeviceTable);

const getColumns = ({
  onDelete,
}: {
  onDelete: (id: number) => void;
}): ColumnDef<IDevice>[] => [
  {
    id: "index",
    header: "STT",
    size: 50,
    cell: ({ row }) => {
      return row.index + 1;
    },
    meta: {
      align: "center",
    },
  },
  {
    id: "delete",
    header: "Xóa",
    size: 50,
    cell: ({ row }) => {
      return <DeleteCell onClick={() => onDelete(row.original.id)} />;
    },
    meta: {
      align: "center",
    },
  },
  {
    id: "code",
    header: "Mã thiết bị",
    accessorKey: "code",
    size: 120,
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
    size: 200,
  },
  {
    id: "fromRoom",
    header: "Điều chuyển từ",
    accessorKey: "roomName",
    size: 120,
  },
  {
    id: "toRoom",
    header: "Điều chuyển đến",
    accessorKey: "roomName",
    size: 180,
    cell: ({ row }) => (
      <TransferToRoomCell
        rowId={row.original.id}
        roomId={row.original.roomId}
      />
    ),
  },
  {
    id: "totalBroken",
    header: "Hỏng",
    size: 120,
    cell: ({ row }) => (
      <TransferBrokenCell
        rowId={row.original.id}
        value={row.original.totalBroken}
        max={row.original.totalBroken}
        deviceName={row.original.deviceName}
      />
    ),
  },
  {
    id: "totalLost",
    header: "Mất",
    size: 120,
    cell: ({ row }) => (
      <TransferLostCell
        rowId={row.original.id}
        value={row.original.totalLost}
        max={row.original.totalLost}
        deviceName={row.original.deviceName}
      />
    ),
  },
  {
    id: "totalAvailable",
    header: "Còn SD",
    size: 120,
    cell: ({ row }) => (
      <TransferAvailableCell
        rowId={row.original.id}
        value={row.original.totalAvailable}
        max={row.original.totalAvailable}
        deviceName={row.original.deviceName}
      />
    ),
  },
];

const TransferToRoomCell = memo(
  ({ rowId, roomId }: { rowId: number; roomId: number }) => {
    const dispatch = useAppDispatch();
    const data = useAppSelector(selectRoomList);
    const transferInput = useAppSelector(
      selectorTransferInputByDeviceId(rowId)
    );
    const [inputValue, setInputValue] = useState<IOption | null>(null);

    const options = useMemo(() => {
      return data.filter((item) => item.id !== roomId);
    }, [data, roomId]);

    const handleChangeValue = (value: IOption) => {
      setInputValue(value);
      dispatch(
        deviceActions.changeTransferDevice({
          deviceId: rowId,
          toRoomId: value.id as number,
        })
      );
    };

    useEffect(() => {
      if (transferInput?.toRoomId) {
        setInputValue(
          options.find((item) => item.id === transferInput.toRoomId)
        );
      }
    }, []);

    return (
      <AppAutoComplete
        options={options}
        onChange={(_, valueInput) => handleChangeValue(valueInput as IOption)}
        value={inputValue}
      />
    );
  }
);

const TransferBrokenCell = memo(
  ({
    rowId,
    value,
    max,
    deviceName,
  }: {
    rowId: number;
    value: number;
    max: number;
    deviceName: string;
  }) => {
    const dispatch = useAppDispatch();
    const [inputValue, setInputValue] = useState(`${value ?? 0}`);
    const [error, setError] = useState(false);
    const transferInput = useAppSelector(
      selectorTransferInputByDeviceId(rowId)
    );

    const changeValueDebounce = useMemo(
      () =>
        CommonUtils.debounce((newValue: number) => {
          dispatch(
            deviceActions.changeTransferDevice({
              deviceId: rowId,
              totalBroken: newValue,
              isError: newValue > max || newValue < 0,
            })
          );
        }, AppConstant.DEBOUNCE_TIME_IN_MILLISECOND),
      [rowId, max]
    );

    const handleChange = (val: string) => {
      setInputValue(val);
      changeValueDebounce(Number(val));
    };

    const handleBlur = () => {
      if (inputValue === "") {
        setInputValue("0");
        setError(true);
        return;
      }
      const inputVal = Number(inputValue);
      if (inputVal > max) {
        setError(true);
        toast.warning(
          `Không thể nhập số lượng hỏng cho thiết bị ${deviceName} lớn hơn tổng số lượng, vui lòng kiểm tra lại. (Tối đa: ${max})`
        );
      } else {
        setError(false);
      }
    };

    useEffect(() => {
      if (transferInput?.totalBroken) {
        setInputValue(transferInput?.totalBroken?.toString());
        setError(Number(transferInput?.totalBroken) > max);
      }
    }, []);

    return (
      <AppTextField
        value={inputValue}
        onChange={(e) => handleChange(e.currentTarget.value)}
        onBlur={handleBlur}
        type="number"
        error={error}
        sx={{ "& input": { minWidth: "unset" } }}
        slotProps={{ htmlInput: { min: 0 } }}
      />
    );
  }
);

const TransferLostCell = memo(
  ({
    rowId,
    value,
    max,
    deviceName,
  }: {
    rowId: number;
    value: number;
    max: number;
    deviceName: string;
  }) => {
    const dispatch = useAppDispatch();
    const [inputValue, setInputValue] = useState(`${value ?? 0}`);
    const [error, setError] = useState(false);
    const transferInput = useAppSelector(
      selectorTransferInputByDeviceId(rowId)
    );

    const changeValueDebounce = useMemo(
      () =>
        CommonUtils.debounce((newValue: number) => {
          dispatch(
            deviceActions.changeTransferDevice({
              deviceId: rowId,
              totalLost: newValue,
              isError: newValue > max || newValue < 0,
            })
          );
        }, AppConstant.DEBOUNCE_TIME_IN_MILLISECOND),
      [rowId, max]
    );

    const handleChange = (val: string) => {
      setInputValue(val);
      changeValueDebounce(Number(val));
    };

    const handleBlur = () => {
      if (inputValue === "") {
        setInputValue("0");
        setError(true);
        return;
      }
      const inputVal = Number(inputValue);
      if (inputVal > max) {
        setError(true);
        toast.warning(
          `Không thể nhập số lượng mất cho thiết bị ${deviceName} lớn hơn tổng số lượng, vui lòng kiểm tra lại. (Tối đa: ${max})`
        );
      } else {
        setError(false);
      }
    };

    useEffect(() => {
      if (transferInput?.totalLost) {
        setInputValue(transferInput?.totalLost?.toString());
        setError(Number(transferInput?.totalLost) > max);
      }
    }, []);

    return (
      <AppTextField
        value={inputValue}
        onChange={(e) => handleChange(e.currentTarget.value)}
        onBlur={handleBlur}
        type="number"
        error={error}
        sx={{ "& input": { minWidth: "unset" } }}
        slotProps={{ htmlInput: { min: 0 } }}
      />
    );
  }
);

const TransferAvailableCell = memo(
  ({
    rowId,
    value,
    max,
    deviceName,
  }: {
    rowId: number;
    value: number;
    max: number;
    deviceName: string;
  }) => {
    const dispatch = useAppDispatch();
    const [inputValue, setInputValue] = useState(`${value ?? 0}`);
    const [error, setError] = useState(false);
    const transferInput = useAppSelector(
      selectorTransferInputByDeviceId(rowId)
    );

    const changeValueDebounce = useMemo(
      () =>
        CommonUtils.debounce((newValue: number) => {
          dispatch(
            deviceActions.changeTransferDevice({
              deviceId: rowId,
              totalAvailable: newValue,
              isError: newValue > max || newValue < 0,
            })
          );
        }, AppConstant.DEBOUNCE_TIME_IN_MILLISECOND),
      [rowId, max]
    );

    const handleChange = (val: string) => {
      setInputValue(val);
      changeValueDebounce(Number(val));
    };

    const handleBlur = () => {
      if (inputValue === "") {
        setInputValue("0");
        setError(true);
        return;
      }
      const inputVal = Number(inputValue);
      if (inputVal > max) {
        setError(true);
        toast.warning(
          `Không thể nhập số lượng còn sử dụng cho thiết bị ${deviceName} lớn hơn tổng số lượng, vui lòng kiểm tra lại. (Tối đa: ${max})`
        );
      } else {
        setError(false);
      }
    };

    useEffect(() => {
      if (transferInput?.totalAvailable) {
        setInputValue(transferInput?.totalAvailable?.toString());
        setError(Number(transferInput?.totalAvailable) > max);
      }
    }, []);

    return (
      <AppTextField
        value={inputValue}
        onChange={(e) => handleChange(e.currentTarget.value)}
        onBlur={handleBlur}
        type="number"
        error={error}
        sx={{ "& input": { minWidth: "unset" } }}
        slotProps={{ htmlInput: { min: 0 } }}
      />
    );
  }
);

export const handleConfigHeightOfTableInModal = (classModal?: string) => {
  const tableModalEl: HTMLElement = document.querySelector(
    `${classModal || ".table-modal"} #table`
  ) as HTMLElement;

  const modalContent: HTMLElement = document.querySelector(
    `#dialog-content`
  ) as HTMLElement;

  if (tableModalEl && modalContent) {
    const tableContainerHeight = modalContent.getBoundingClientRect().height;
    const paginationHeight = 40;
    const spacing = 16;
    const paddingBottomModal = 16;
    const resultHeight =
      tableContainerHeight - paginationHeight - spacing - paddingBottomModal;

    tableModalEl.style.height = `${resultHeight}px`;
  }
};
