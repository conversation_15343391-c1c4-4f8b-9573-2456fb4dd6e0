import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useFieldArray, useFormContext, useWatch } from "react-hook-form";
import { v4 as uuid } from "uuid";
import { ColumnDef } from "@tanstack/react-table";
import { AppConfirmModal, AppTable, AppTextField } from "@/components/common";
import {
  CountrySelectEditForm,
  DateEditForm,
  InputEditForm,
  RoomSelectEditForm,
} from "./form";
import {
  IDeviceTransactionUI,
  IDevices,
} from "../../../../equipmentDocument.model";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import { Skeleton, Stack } from "@mui/material";
import { formatNumber } from "@/utils/format.utils";
import DeleteCell from "@/components/common/table/cell/DeleteCell";
import dayjs from "dayjs";
import AppFormLayoutPanel from "@/components/common/AppFormLayoutPanel";
import AddDeviceAction, { MAX_REGISTER } from "./AddDeviceAction";
import { buildDeviceRow, renderDeviceCode } from "../../../../helper";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import {
  equipmentDocumentActions,
  selectIsPendingBuildDevice,
} from "../../../../equipmentDocument.slice";
import { MANAGE_BY } from "@/models/system.model";
import { toast } from "sonner";

const TableEditForm = () => {
  const dispatch = useAppDispatch();
  const { control, getValues, setValue } =
    useFormContext<IDeviceTransactionUI>();

  const isPending = useAppSelector(selectIsPendingBuildDevice);

  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);

  const { remove, fields, prepend } = useFieldArray({
    control,
    name: "devices",
  });

  const documentDate = useWatch({ control, name: "documentDate" });

  const columns = useMemo(
    () => getColumn(fields, setDeleteIndex, documentDate),
    [fields, documentDate]
  );

  const handleAddDevice = useCallback(
    (startRegister: number, onSetValueStart) => {
      const totalDeviceAdd = Number(getValues("totalAdd"));
      if (!totalDeviceAdd || !startRegister) return;
      if (totalDeviceAdd + startRegister - 1 > MAX_REGISTER) {
        toast.warning("Cảnh báo", {
          description: `Số đăng ký thiết bị vượt quá ${MAX_REGISTER}. Vui lòng kiểm tra lại`,
        });
        return;
      }
      const devices = getValues("devices");
      if (totalDeviceAdd > 10 || devices.length > 40) {
        dispatch(equipmentDocumentActions.togglePendingBuildDevice(true));
      }
      setTimeout(() => {
        const documentDate = getValues("documentDate");
        const deviceCode = getValues("deviceCode");
        const manageBy = getValues("manageBy");

        const newRows: IDevices[] = [];

        for (let i = 0; i < totalDeviceAdd; i++) {
          newRows.push(
            buildDeviceRow(
              {
                code: renderDeviceCode(
                  deviceCode,
                  manageBy === MANAGE_BY.isManageDevice ? startRegister : 1,
                  manageBy === MANAGE_BY.isManageDevice ? i : 0
                ),
              },
              1,
              documentDate
            )
          );
        }

        prepend(newRows.toReversed());
        if (manageBy === MANAGE_BY.isManageDevice) {
          onSetValueStart(Number(startRegister) + Number(totalDeviceAdd));
        }
        dispatch(equipmentDocumentActions.togglePendingBuildDevice(false));
      }, 0);
    },
    []
  );

  useEffect(() => {
    dispatch(equipmentDocumentActions.togglePendingBuildDevice(true));
    setTimeout(() => {
      dispatch(equipmentDocumentActions.togglePendingBuildDevice(false));
    }, 1000);
  }, []);

  return (
    <>
      <AppFormLayoutPanel
        title="Thông tin thiết bị"
        childrenProps={{ height: 500 }}
        titleProps={{
          justifyContent: "space-between",
        }}
        actions={<AddDeviceAction onAddDevice={handleAddDevice} />}
      >
        <Stack position="relative" height="100%">
          {isPending && <LoadingSkeleton />}
          <AppTable
            columns={columns}
            data={isPending ? [] : fields}
            totalData={isPending ? 0 : fields.length}
            hasDefaultPagination
            {...TABLE_MODAL_FULL_HEIGHT}
          />
        </Stack>
        <AppConfirmModal
          isOpen={typeof deleteIndex === "number"}
          modalTitleProps={{
            title: "Bạn xác nhận xóa thiết bị hiện tại",
          }}
          onClose={() => setDeleteIndex(null)}
          onConfirm={() => remove(deleteIndex as number)}
        />
      </AppFormLayoutPanel>
    </>
  );
};

export default memo(TableEditForm);

const getColumn = (
  fields: IDevices[],
  remove,
  documentDate
): ColumnDef<IDevices>[] => [
  {
    id: "index",
    header: "STT",
    cell: ({ row }) => row.index + 1,
    size: 50,
    meta: { align: "center" },
  },
  {
    id: "delete",
    accessorKey: "Xóa",
    cell: ({ row }) => <DeleteCell onClick={() => remove(row.index)} />,
    size: 40,
  },
  {
    id: "code",
    header: "Mã",
    accessorKey: "code",
    size: 50,
    meta: {
      cellSx: {
        whiteSpace: "nowrap",
      },
    },
  },
  {
    id: "serial",
    header: "Serial",
    cell: ({ row }) => (
      <InputEditForm
        rowIndex={row.index}
        keyChange="serial"
        defaultValue={fields[row.index]?.serial}
      />
    ),
    size: 200,
  },
  {
    id: "price",
    header: "Đơn giá",
    cell: ({ row }) => (
      <InputEditForm
        keyChange="price"
        rowIndex={row.index}
        defaultValue={fields[row.index]?.price}
        textfieldProps={{
          type: "number",
          isCurrency: true,
          slotProps: { htmlInput: { min: 0 } },
        }}
      />
    ),
  },
  {
    id: "quantity",
    header: "Số lượng",
    size: 100,
    meta: {
      align: "right",
    },
    cell: ({ row }) => (
      <QuantityCell
        rowIndex={row.index}
        defaultValue={fields[row.index]?.quantity}
      />
    ),
  },
  {
    id: "roomId",
    header: "Kho phòng",
    cell: ({ row }) => (
      <RoomSelectEditForm
        rowIndex={row.index}
        defaultValue={row.original.roomId}
      />
    ),
    size: 250,
  },
  {
    id: "countryId",
    header: "Quốc gia",
    cell: ({ row }) => (
      <CountrySelectEditForm
        rowIndex={row.index}
        defaultValue={row.original.countryId}
      />
    ),
    size: 200,
  },
  {
    id: "entryDate",
    header: "Ngày nhập",
    cell: ({ row }) => (
      <DateEditForm
        datePickerProps={{
          minDate: dayjs(documentDate),
          maxDate: null,
        }}
        rowIndex={row.index}
        defaultValue={row.original.entryDate as any}
        keyChange="entryDate"
        rules={{
          validate: (value) => {
            if (!value) return true;
            if (documentDate) {
              const inputDate = dayjs(value);
              const docDate = dayjs(documentDate);

              if (inputDate.isBefore(docDate)) {
                return "Ngày nhập không được nhỏ hơn ngày chứng từ";
              }
            }
            return true;
          },
        }}
      />
    ),
    size: 170,
  },
  {
    id: "expireDate",
    header: "Hạn sử dụng",
    cell: ({ row }) => (
      <DateEditForm
        rowIndex={row.index}
        defaultValue={row.original.expireDate as any}
        keyChange="expireDate"
        datePickerProps={{
          maxDate: undefined,
        }}
      />
    ),
    size: 170,
  },
];

// Loading Skeleton
const LoadingSkeleton = memo(() => (
  <Stack
    spacing={2}
    position="absolute"
    top={0}
    left={0}
    right={0}
    bottom={0}
    zIndex={1000}
    bgcolor="white"
    flex={1}
    minHeight={0}
    overflow="hidden"
  >
    <Skeleton variant="rectangular" sx={{ minHeight: 40 }} />
    {Array.from({ length: 15 }).map((_, i) => (
      <Skeleton key={i} variant="text" />
    ))}
  </Stack>
));
const QuantityCell = ({
  rowIndex,
  defaultValue,
}: {
  rowIndex: number;
  defaultValue: string | number;
}) => {
  let isWarningShown = false;
  const { control, getValues, setValue } = useFormContext();
  const manageBy = getValues("manageBy");

  const quantity = useWatch({
    control,
    name: `devices.${rowIndex}.quantity`,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = Number(e.target.value);
    if (isNaN(newValue) || newValue < 1) return;

    const prevValue = quantity;
    let delta = newValue - prevValue;
    const currentTotal = Number(getValues("totalAvailable"));

    // Nếu đang giảm nhiều hơn totalAvailable, thì chỉ giảm được tối đa
    if (delta < 0 && Math.abs(delta) > currentTotal) {
      if (!isWarningShown) {
        isWarningShown = true;
        toast.warning("Cảnh báo", {
          description: "Không thể giảm số lượng vượt quá số thiết bị hiện có.",
        });

        setTimeout(() => {
          isWarningShown = false;
        }, 4000);
      }

      delta = -currentTotal;
    }

    const finalValue = prevValue + delta;
    const nextTotal = currentTotal + delta;

    setValue(`devices.${rowIndex}.quantity`, finalValue, { shouldDirty: true });
    setValue("totalAvailable", nextTotal);
  };

  return manageBy === MANAGE_BY.isManageDevice ? (
    <>{formatNumber(quantity)}</>
  ) : (
    <AppTextField
      type="number"
      value={quantity}
      onChange={handleChange}
      slotProps={{ htmlInput: { min: 1 } }}
    />
  );
};
