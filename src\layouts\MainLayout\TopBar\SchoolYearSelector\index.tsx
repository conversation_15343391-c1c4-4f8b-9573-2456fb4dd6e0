"use client";

import { Button } from "@mui/material";
import React, { memo, useState, useMemo, useCallback } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import dynamic from "next/dynamic";

const ChangeSchoolYearModal = dynamic(() => import("./ChangeSchoolYearModal"), {
  ssr: false,
});

const SchoolYearSelector = () => {
  const [isOpen, setIsOpen] = useState(false);

  const { schoolYearSelected, semesterSelected } = useSelector(
    (state: RootState) => state.appReducer
  );

  const displayText = useMemo(() => {
    const schoolYear = schoolYearSelected?.label || "";
    const semester = semesterSelected?.id || "";
    return `Họ<PERSON> kỳ ${semester} - Năm học: ${schoolYear} `;
  }, [schoolYearSelected, semesterSelected]);

  const handleClose = useCallback(() => setIsOpen(false), []);

  return (
    <>
      <Button
        variant="outlined"
        onClick={() => setIsOpen(true)}
        sx={{
          fontSize: 12,
          lineHeight: "15px",
          fontWeight: 400,
          px: 2,
          height: "28px",
          minWidth: "200px",
        }}
      >
        {displayText}
      </Button>
      {isOpen && (
        <ChangeSchoolYearModal isOpen={isOpen} onClose={handleClose} />
      )}
    </>
  );
};

export default memo(SchoolYearSelector);
