import {
  AppF<PERSON><PERSON>oggle,
  AppF<PERSON><PERSON><PERSON>t<PERSON>ield,
  GridFormContainer,
} from "@/components/common";
import AppFormDatePicker from "@/components/common/form/AppFormDatePicker";
import { Box, Grid, Tooltip, Typography } from "@mui/material";
import { useWatch, useFormState, useFormContext } from "react-hook-form";

const EditContent = () => {
  const { control } = useFormContext();
  const inventoryTransactionId = useWatch({
    control,
    name: "inventoryTransactionId",
  });
  const inventoryTransactionName = useWatch({
    control,
    name: "inventoryTransactionName",
  });
  const inventoryTransactionNumber = useWatch({
    control,
    name: "inventoryTransactionNumber",
  });
  const isDisabled =
    !!inventoryTransactionId && Number(inventoryTransactionId) > 0;
  const { errors } = useFormState({ control });

  return (
    <GridFormContainer>
      <Grid size={12}>
        <AppFormTextField
          control={control}
          name="deviceName"
          label="Tên thiết bị"
          textfieldProps={{
            disabled: true,
          }}
        />
      </Grid>
      <Grid size={12}>
        <AppFormDatePicker
          control={control}
          name="reportedDate"
          label="Thời gian"
          datePickerProps={{
            maxDate: null,
            disabled: isDisabled,
          }}
        />
      </Grid>
      <Grid size={6}>
        <AppFormTextField
          control={control}
          name="totalBroken"
          label="Số lượng hỏng"
          textfieldProps={{
            type: "number",
            disabled: isDisabled,
            inputProps: { min: 0 },
            error: !!errors.totalBroken,
            helperText: errors.totalBroken?.message as string,
          }}
          rules={{
            validate: (value, formValues) => {
              if (Number(value) < Number(formValues.totalFixed)) {
                return `Số lượng hỏng không được nhỏ hơn số lượng sửa chữa: ${formValues.totalFixed}`;
              }
              return true;
            },
          }}
        />
      </Grid>
      <Grid size={6}>
        <AppFormTextField
          control={control}
          name="totalLost"
          label="Số lượng mất"
          textfieldProps={{
            type: "number",
            disabled: isDisabled,
            inputProps: { min: 0 },
          }}
        />
      </Grid>
      <Grid size={12}>
        <AppFormTextField
          control={control}
          name="notes"
          label="Ghi chú"
          textfieldProps={{
            multiline: true,
            rows: 3,
            disabled: isDisabled,
          }}
        />
      </Grid>
      <Grid size={12}>
        {isDisabled && (
          <Typography variant="body1" color="red">
            Vui lòng cập nhật lại số liệu hỏng, mất từ mục kiểm kê{" "}
            {inventoryTransactionName} ({inventoryTransactionNumber}) !
          </Typography>
        )}
      </Grid>
    </GridFormContainer>
  );
};

export default EditContent;
