"use client";

import { But<PERSON> } from "@mui/material";
import React, { memo, useState, useMemo } from "react";
import ApproveModal from "./ApproveModal";
import { IBorrowDeviceModal } from "../../borrowDevice.modal";
import { BorrowStatusEnum } from "@/models/eduDevice.model";

const ApproveBorrow = ({
  fetchCurrentData,
  selectedRows = [],
  borrowType,
  onClearSelectedRows,
}: {
  fetchCurrentData?: () => void;
  selectedRows?: IBorrowDeviceModal[];
  borrowType?: number;
  onClearSelectedRows?: () => void;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const selectedRowsStatus = useMemo(() => {
    return selectedRows.filter(
      (row) => row.status === BorrowStatusEnum.Register
    );
  }, [selectedRows]);

  return (
    <>
      <Button
        variant="contained"
        color="primary"
        onClick={() => setIsOpen(true)}
        disabled={selectedRows.length === 0 || selectedRowsStatus.length === 0}
      >
        <PERSON><PERSON> mượn ({selectedRowsStatus.length})
      </Button>
      <ApproveModal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        selectedRows={selectedRowsStatus}
        borrowType={borrowType}
        fetchCurrentData={fetchCurrentData}
        onClearSelectedRows={onClearSelectedRows}
      />
    </>
  );
};

export default memo(ApproveBorrow);
