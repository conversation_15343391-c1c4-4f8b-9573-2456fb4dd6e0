import { useMemo, useState, memo, useEffect } from "react";
import { Tabs, Tab, Box } from "@mui/material";
import dayjs from "dayjs";
import { formatDayjsWithType } from "@/utils/format.utils";
import { AppConstant } from "@/constant";

const WEEK_DAYS = [
  { label: "Thứ 2", value: 1 },
  { label: "Thứ 3", value: 2 },
  { label: "Thứ 4", value: 3 },
  { label: "Thứ 5", value: 4 },
  { label: "Thứ 6", value: 5 },
  { label: "Thứ 7", value: 6 },
];

interface IWeekInfo {
  id: number;
  fromDate: Date | string;
  toDate: Date | string;
  [key: string]: any;
}

interface WeekTabSelectorProps {
  /** Tuần hiện tại được chọn */
  currentWeek?: IWeekInfo | null;
  /** Callback khi thay đổi ngày */
  onDateChange?: (dates: { fromDate: string; toDate: string }) => void;
  /** <PERSON><PERSON> hiển thị tab "Tất cả" không */
  showAllTab?: boolean;
  /** Label cho tab "Tất cả" */
  allTabLabel?: string;
  /** Style wrapper */
  sx?: any;
  /** Current filter dates để detect clear filter */
  currentFilterDates?: { fromDate?: string; toDate?: string };
}

const WeekTabSelector = ({
  currentWeek,
  onDateChange,
  showAllTab = true,
  allTabLabel = "Tất cả",
  sx,
  currentFilterDates,
}: WeekTabSelectorProps) => {
  const [selectedTab, setSelectedTab] = useState<string>("all");

  const weekDays = useMemo(() => {
    if (!currentWeek) return [];

    const startOfWeek = dayjs(currentWeek.fromDate);

    return WEEK_DAYS.map((d, idx) => {
      const date = startOfWeek.add(idx, "day");
      return {
        ...d,
        date: date.toDate(),
        formattedDate: date.format("DD/MM"),
        fullLabel: `${d.label}`,
      };
    });
  }, [currentWeek]);

  useEffect(() => {
    setSelectedTab("all");
  }, [currentWeek]);

  useEffect(() => {
    if (
      !currentWeek ||
      !currentFilterDates?.fromDate ||
      !currentFilterDates?.toDate
    ) {
      return;
    }

    const weekStart = dayjs(currentWeek.fromDate).format("YYYY-MM-DD");
    const weekEnd = dayjs(currentWeek.toDate).format("YYYY-MM-DD");
    const filterStart = dayjs(currentFilterDates.fromDate).format("YYYY-MM-DD");
    const filterEnd = dayjs(currentFilterDates.toDate).format("YYYY-MM-DD");

    if (filterStart === weekStart && filterEnd === weekEnd) {
      setSelectedTab("all");
    }
  }, [currentFilterDates, currentWeek]);

  const handleTabChange = (_: any, newValue: string) => {
    setSelectedTab(newValue);

    if (newValue === "all") {
      if (currentWeek && onDateChange) {
        onDateChange({
          toDate: formatDayjsWithType(
            currentWeek.toDate,
            AppConstant.DATE_TIME_YYYYescape
          ),
          fromDate: formatDayjsWithType(
            currentWeek.fromDate,
            AppConstant.DATE_TIME_YYYYescape
          ),
        });
      }
    } else {
      const idx = parseInt(newValue, 10);
      const day = weekDays[idx];
      if (day && onDateChange) {
        onDateChange({
          toDate: formatDayjsWithType(
            day.date,
            AppConstant.DATE_TIME_YYYYescape
          ),
          fromDate: formatDayjsWithType(
            day.date,
            AppConstant.DATE_TIME_YYYYescape
          ),
        });
      }
    }
  };

  if (!currentWeek) return null;

  return (
    <Box
      sx={{
        display: "flex",
        flex: 1,
        justifyContent: "flex-end",
        ...sx,
      }}
    >
      <Tabs
        sx={{
          p: 0,
          m: 0,
          borderRadius: 20,
          "& .MuiTabs-indicator": {
            display: "none",
          },
          "& .MuiTab-root": {
            "&.Mui-selected": {
              backgroundColor: "primary.main",
              color: "white",
              borderRadius: 20,
            },
          },
        }}
        value={selectedTab}
        onChange={handleTabChange}
      >
        {showAllTab && <Tab label={allTabLabel} value="all" />}
        {weekDays.map((d, idx) => (
          <Tab
            key={`${d.value}-${d.formattedDate}`}
            label={d.fullLabel}
            value={String(idx)}
          />
        ))}
      </Tabs>
    </Box>
  );
};

export default memo(WeekTabSelector);
