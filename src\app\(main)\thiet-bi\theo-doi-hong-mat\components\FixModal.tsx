"use client";

import {
  AppFormTextField,
  AppModal,
  GridFormContainer,
} from "@/components/common";
import { AppModalProps } from "@/components/common/modal/AppModal";
import { Button } from "@mui/material";
import { useCallback, useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { ILostDamageDevice } from "../lostDamgeDevice.model";
import { useAppDispatch } from "@/redux/hook";
import { fixLostDamageDevice } from "../redux/lostDamageDevice.slice";

const FixModal = ({
  data,
  onClose,
  onSuccess,
  ...otherProps
}: FixModalProps) => {
  const methods = useForm({
    defaultValues: { totalFixed: 0, totalBroken: 0 },
  });

  const {
    control,
    reset,
    handleSubmit,
    setValue,
    formState: { errors },
  } = methods;

  useEffect(() => {
    if (data?.totalFixed !== undefined) {
      setValue("totalFixed", data.totalFixed);
    }
  }, [data?.totalFixed, setValue]);

  const dispatch = useAppDispatch();

  const handleCloseModal = useCallback(() => {
    onClose();
    reset();
  }, [onClose, reset]);

  const handleSuccessAndClose = useCallback(() => {
    onClose();
    reset();
    onSuccess?.();
  }, [onClose, reset, onSuccess]);

  const handleSubmitData = useCallback(
    (values: { totalFixed: number }) => {
      if (data?.id) {
        const payload = {
          totalFixed: Number(values.totalFixed),
        };
        dispatch(
          fixLostDamageDevice({
            id: data.id,
            data: payload,
            onSuccess: handleSuccessAndClose,
          })
        );
      }
    },
    [data, handleSuccessAndClose]
  );

  return (
    <FormProvider {...methods}>
      <AppModal
        component="form"
        onSubmit={handleSubmit(handleSubmitData)}
        onClose={handleCloseModal}
        modalTitleProps={{
          title: "Sửa chữa",
        }}
        modalContentProps={{
          content: (
            <GridFormContainer>
              <AppFormTextField
                label="Số lượng đã sửa"
                control={control}
                name="totalFixed"
                textfieldProps={{
                  type: "number",
                  inputProps: { min: 0 },
                  error: !!errors?.totalFixed,
                  helperText: errors?.totalFixed?.message,
                }}
                rules={{
                  validate: (value) => {
                    if (value > (data?.totalBroken ?? 0)) {
                      return (
                        "Số lượng đã sửa không được lớn hơn số lượng đã mất: " +
                        (data?.totalBroken ?? 0)
                      );
                    }
                    return true;
                  },
                }}
              />
            </GridFormContainer>
          ),
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                color="secondary"
                variant="outlined"
                onClick={handleCloseModal}
              >
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
        {...otherProps}
      />
    </FormProvider>
  );
};

type FixModalProps = AppModalProps & {
  data: ILostDamageDevice | null;
  onSuccess?: () => void;
};

export default FixModal;
