import http from "@/api";
import { ApiConstant, EnvConstant } from "@/constant";
import { ADD_MENU, DELETE_MENU, UPDATE_MENU } from "@/constant/api.const";
import { DataResponseModel } from "@/models/response.model";
import { extractErrorMessage } from "@/utils/common.utils";
import { toast } from "sonner";
import stringFormat from "string-format";
import { IAddMenuConfig } from "@/models/menu.model";
import { useAppDispatch } from "@/redux/hook";
import { menuActions } from "@/app/(main)/root/quan-ly-menu/store/menu.slice";
import { CommonUtils } from "@/utils";

const useMenuActions = () => {
  const dispatch = useAppDispatch();

  const handleDeleteMenu = async (menuId: number) => {
    CommonUtils.toggleAppProgress(true);
    try {
      const response: DataResponseModel<any> = await http.delete(
        stringFormat(DELETE_MENU, { id: menuId })
      );
      if (response.code === ApiConstant.ERROR_CODE_OK) {
        toast.success("Thành công!", {
          description: "Dữ liệu đã cập nhật thành công.",
        });
        dispatch(menuActions.refreshFilter());
      } else {
        throw new Error(response?.message || "Đã có lỗi xảy ra");
      }
    } catch (error) {
      EnvConstant.IS_DEV &&
        console.error("Lỗi khi lấy chi tiết dữ liệu:", error);
      toast.error("Thất bại!", {
        description: extractErrorMessage(error),
      });
    } finally {
      CommonUtils.toggleAppProgress(false);
    }
  };

  const handleAddMenu = async (data: IAddMenuConfig, callback?: () => void) => {
    CommonUtils.toggleAppProgress(true);
    try {
      const response: DataResponseModel<any> = await http.post(ADD_MENU, data);
      if (response.code === ApiConstant.ERROR_CODE_OK) {
        toast.success("Thành công!", {
          description: "Dữ liệu đã cập nhật thành công.",
        });
        dispatch(menuActions.refreshFilter());
        callback?.();
      } else {
        throw new Error(response?.message || "Đã có lỗi xảy ra");
      }
    } catch (error) {
      toast.error("Thất bại!", {
        description: extractErrorMessage(error),
      });
    } finally {
      CommonUtils.toggleAppProgress(false);
    }
  };

  const handleUpdateMenu = async (
    data: IAddMenuConfig,
    menuId: number,
    callback?: () => void
  ) => {
    CommonUtils.toggleAppProgress(true);
    try {
      const response: DataResponseModel<any> = await http.put(
        stringFormat(UPDATE_MENU, { id: menuId }),
        data
      );
      if (response.code === ApiConstant.ERROR_CODE_OK) {
        toast.success("Thành công!", {
          description: "Dữ liệu đã cập nhật thành công.",
        });
        dispatch(menuActions.refreshFilter());
        callback?.();
      } else {
        throw new Error(response?.message || "Đã có lỗi xảy ra");
      }
    } catch (error) {
      toast.error("Thất bại!", {
        description: extractErrorMessage(error),
      });
    } finally {
      CommonUtils.toggleAppProgress(false);
    }
  };

  return {
    handleAddMenu,
    handleDeleteMenu,
    handleUpdateMenu,
  };
};

export default useMenuActions;
