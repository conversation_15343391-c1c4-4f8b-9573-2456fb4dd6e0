import { AppCheckbox, AppTable } from "@/components/common";
import DeleteCell from "@/components/common/table/cell/DeleteCell";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { Typography } from "@mui/material";
import { ColumnDef, Row } from "@tanstack/react-table";
import { memo } from "react";
import { ITransactionTeams } from "../../deviceLiquidation.model";
import {
  deviceLiquidationActions,
  selectTeamlead,
} from "../../deviceLiquidation.slice";
import { TeamleadEditableCell } from "./TeamleadEditableCell";

const TeamleadTable = ({ tableProps }) => {
  const dispatch = useAppDispatch();
  const teamlead = useAppSelector(selectTeamlead);

  const columns = getColumns((id) => {
    dispatch(deviceLiquidationActions.deleleTeamlead(id));
  });

  return (
    <AppTable
      columns={columns}
      data={teamlead}
      totalData={teamlead?.length || 0}
      hasDefaultPagination
      {...tableProps}
    />
  );
};

export default TeamleadTable;

const getColumns = (onDelete): ColumnDef<ITransactionTeams>[] => [
  {
    id: "index",
    header: "STT",
    cell: ({ row }) => {
      return <Typography>{row.index + 1}</Typography>;
    },
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "delete",
    header: "Xóa",
    cell: ({ row }) => <DeleteCell onClick={() => onDelete(row.original.id)} />,
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "teacherName",
    header: "Tên thành viên",
    accessorKey: "teacherName",
    size: 80,
  },
  {
    id: "teacherCode",
    header: "Mã giáo viên",
    accessorKey: "teacherCode",
    size: 60,
  },
  {
    id: "position",
    header: "Vị trí",
    cell: ({ row }) => (
      <TeamleadEditableCell
        teacherCode={row.original.teacherCode}
        field="position"
        defaultValue={row.original.position ?? ""}
      />
    ),
  },
  {
    id: "role",
    header: "Vai trò",
    cell: ({ row }) => (
      <TeamleadEditableCell
        teacherCode={row.original.teacherCode}
        field="role"
        defaultValue={row.original.role ?? ""}
      />
    ),
  },
  {
    id: "note",
    header: "Ghi chú",
    cell: ({ row }) => (
      <TeamleadEditableCell
        teacherCode={row.original.teacherCode}
        field="note"
        defaultValue={row.original.note ?? ""}
      />
    ),
  },
  {
    id: "isTeamlead",
    header: "Trường hội đồng thanh lý",
    meta: {
      align: "center",
    },
    cell: ({ row }) => <TeamleadCheck row={row} />,
  },
];

const TeamleadCheck = memo(({ row }: { row: Row<ITransactionTeams> }) => {
  const dispatch = useAppDispatch();
  const isChecked = !!row.original.isTeamlead;

  const handleChange = () => {
    dispatch(
      deviceLiquidationActions.updateTeamleadField({
        teacherCode: row.original.teacherCode,
        field: "isTeamlead",
        value: !isChecked,
      })
    );
  };

  return <AppCheckbox checked={isChecked} onChange={handleChange} />;
});
