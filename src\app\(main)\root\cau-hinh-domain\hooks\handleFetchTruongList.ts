import http from "@/api";
import { ApiConstant } from "@/constant";
import { DataListResponseModel } from "@/models/response.model";
import { convertDataToOptions } from "@/utils/format.utils";

export const handleFetchTruongList = async (
  doetCode?: string,
  divisionCode?: string
) => {
  try {
    const response = await http.get<DataListResponseModel<any>>(
      ApiConstant.GET_DON_VI,
      { params: { doetCode, divisionCode } }
    );
    return response.code === ApiConstant.ERROR_CODE_OK
      ? convertDataToOptions(response.data.data)
      : [];
  } catch (error) {
    console.error("Error fetching Truong list:", error);
    return [];
  }
};
