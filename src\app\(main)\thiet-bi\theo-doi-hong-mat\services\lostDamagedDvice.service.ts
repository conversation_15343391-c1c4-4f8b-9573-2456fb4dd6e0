import http from "@/api";
import { ApiConstant } from "@/constant";

export interface ILostDamageDevicePayload {
  deviceId: number;
  reportedDate: string;
  totalBroken: number;
  totalLost: number;
  notes: string;
}

export const submitLostDamageDeviceService = (
  data: ILostDamageDevicePayload
) => {
  return http.post(ApiConstant.DEVICE_ISSUE, data);
};

export const updateLostDamageDeviceService = (
  id: number,
  data: ILostDamageDevicePayload
) => {
  return http.put(`${ApiConstant.DEVICE_ISSUE}/${id}`, data);
};

export const fixLostDamageDeviceService = (
  id: number,
  data: { totalFixed: number }
) => {
  return http.put(`${ApiConstant.DEVICE_ISSUE_FIX}/${id}`, data);
};
