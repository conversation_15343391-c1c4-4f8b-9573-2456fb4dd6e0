"use client";

import { TablePageLayout } from "@/components/common";
import { DEVICE_ISSUE } from "@/constant/api.const";
import { ColumnDef } from "@tanstack/react-table";
import React, {
  useEffect,
  useState,
  useMemo,
  useCallback,
  useRef,
  memo,
} from "react";
import { ILostDamageDevice } from "../lostDamgeDevice.model";
import CreateModal from "./AddLostDamgeDeviceModal/CreateModal";
import EditModal from "./EditLostDamageDeviceModal";
import { formatDayjsWithType, formatNumber } from "@/utils/format.utils";
import { ApiConstant } from "@/constant";
import { IconButton, Box } from "@mui/material";
import FixModal from "./FixModal";
import FixIconCell from "./FixIconCell";
import { injectableDeviceSaga } from "../redux/lostDamageDevice.saga";
import { useStore } from "react-redux";
import { AppStore } from "@/redux/store";
import { ITableRef } from "@/components/common/TablePageLayout/type";

const LostDamageDevice = () => {
  const [fixData, setFixData] = useState<ILostDamageDevice | null>(null);
  const tableRef = useRef<ITableRef>(null);

  const handleFixClick = useCallback((data: ILostDamageDevice) => {
    setFixData(data);
  }, []);

  const handleCloseFixModal = useCallback(() => {
    setFixData(null);
  }, []);

  const handleFixSuccess = useCallback(() => {
    setFixData(null);
    tableRef.current?.fetchCurrentData?.();
  }, []);

  const columns = useMemo(
    (): ColumnDef<ILostDamageDevice>[] => [
      {
        id: "fix",
        header: "Sửa chữa",
        accessorKey: "fix",
        meta: { align: "center" },
        size: 50,
        cell: ({ row }) => (
          <FixIconCell data={row.original} onFixClick={handleFixClick} />
        ),
      },
      {
        id: "reportedDate",
        header: "Thời gian",
        accessorKey: "reportedDate",
        size: 60,
        cell: ({ row }) => formatDayjsWithType(row.original.reportedDate),
      },
      {
        id: "deviceName",
        header: "Tên thiết bị",
        accessorKey: "deviceName",
        size: 150,
        cell: ({ row }) => (
          <>
            <div>{row.original.deviceName}</div>
            <Box sx={{ fontSize: "12px", color: "text.secondary" }}>
              {row.original.deviceCode}
            </Box>
          </>
        ),
      },
      {
        id: "roomName",
        accessorKey: "roomName",
        header: "Kho/phòng",
        size: 60,
      },
      {
        id: "schoolSubjectName",
        accessorKey: "schoolSubjectName",
        header: "Môn học",
        size: 60,
      },
      {
        id: "deviceUnitName",
        accessorKey: "deviceUnitName",
        header: "DVT",
        size: 60,
      },
      {
        id: "totalBroken",
        accessorKey: "totalBroken",
        header: "Hỏng",
        size: 60,
        meta: { align: "right" },
        cell: ({ row }) => formatNumber(row.original.totalBroken),
      },
      {
        id: "totalFixed",
        accessorKey: "totalFixed",
        header: "Đã sửa",
        size: 60,
        meta: { align: "right" },
        cell: ({ row }) => formatNumber(row.original.totalFixed),
      },
      {
        id: "totalLost",
        accessorKey: "totalLost",
        header: "Mất",
        size: 60,
        meta: { align: "right" },
        cell: ({ row }) => formatNumber(row.original.totalLost),
      },
      {
        id: "inventoryTransactionName",
        accessorKey: "inventoryTransactionName",
        header: "Đợt kiểm kê",
        size: 100,
        cell: ({ row }) => (
          <>
            <div>{row.original.inventoryTransactionName}</div>
            <Box sx={{ fontSize: "12px", color: "text.secondary" }}>
              {row.original.inventoryTransactionNumber}
            </Box>
          </>
        ),
      },
      {
        id: "notes",
        accessorKey: "notes",
        header: "Ghi chú",
        size: 60,
      },
    ],
    []
  );

  const store = useStore() as AppStore;

  useEffect(() => {
    injectableDeviceSaga.injectInto(store);
  }, [store]);

  const cleanDataFormFiled = useCallback((data: any) => {
    const newData = { ...data };
    delete newData.deviceName;
    return newData;
  }, []);

  return (
    <>
      <TablePageLayout<ILostDamageDevice>
        ref={tableRef}
        visibleCol={VISIBLE_COL}
        apiUrl={DEVICE_ISSUE}
        tableProps={{
          columns: columns,
        }}
        cleanDataFormFiled={cleanDataFormFiled}
        filterConfig={FILTER_CONFIG}
        actions={["create", "update", "delete"]}
        formConfig={{
          deleteUrl: DEVICE_ISSUE,
          detailUrl: DEVICE_ISSUE,
        }}
        CreateModalComponent={({ isOpen, onClose, fetchCurrentData }) => (
          <CreateModal
            isOpen={isOpen}
            onClose={onClose}
            fetchCurrentData={fetchCurrentData}
          />
        )}
        EditModalComponent={({
          isOpen,
          onClose,
          modalData,
          fetchCurrentData,
        }) => (
          <EditModal
            isOpen={isOpen}
            onClose={onClose}
            data={modalData}
            onSuccess={fetchCurrentData}
          />
        )}
      />
      {Boolean(fixData) && (
        <FixModal
          data={fixData}
          isOpen={Boolean(fixData)}
          onClose={handleCloseFixModal}
          onSuccess={handleFixSuccess}
        />
      )}
    </>
  );
};

export default memo(LostDamageDevice);

const VISIBLE_COL = [
  { id: "reportedDate", name: "Thời gian" },
  { id: "deviceName", name: "Tên thiết bị" },
  { id: "roomName", name: "Kho/phòng" },
  { id: "schoolSubjectName", name: "Môn học" },
  { id: "deviceUnitName", name: "DVT" },
  { id: "totalBroken", name: "Hỏng" },
  { id: "totalFixed", name: "Đã sửa" },
  { id: "totalLost", name: "Mất" },
  { id: "inventoryTransactionName", name: "Đợt kiểm kê" },
  { id: "notes", name: "Ghi chú" },
];

const FILTER_CONFIG = [
  {
    key: "dateRange",
    type: "dateRange" as const,
    label: "Thời gian",
    keyDateRange: ["fromDate", "toDate"],
    value: [null, null],
  },
  {
    key: "searchKey",
    type: "text" as const,
    label: "Tìm kiếm",
    size: 2.4,
  },
  {
    key: "roomId",
    type: "select" as const,
    label: "Kho/phòng",
    size: 2.4,
    apiListUrl: ApiConstant.ROOM,
  },
  {
    key: "schoolSubjectId",
    type: "select" as const,
    label: "Môn học",
    size: 2.4,
    apiListUrl: ApiConstant.SUBJECT,
  },
];
