import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: true,
  experimental: {
    optimizePackageImports: [
      "@emotion/react",
      "@emotion/styled",
      "@mui/x-date-pickers",
      "@mui/x-tree-view",
      "@reduxjs/toolkit",
      "react-hook-form",
      "react-dropzone",
      "react-infinite-scroll-component",
      "react-multi-date-picker",
      "react-redux",
      "redux-saga",
      "zustand",
    ],
  },
};

export default nextConfig;
