"use client";

import { useMemo } from "react";
import { Control, useWatch } from "react-hook-form";
import ListBoxInfiniteScroll from "./ListBoxInfiniteScroll";
import {
  AutocompleteCloseReason,
  AutocompleteInputChangeReason,
} from "@mui/material";
import { ILogin } from "../../../login.model";
import { AppFormAutocomplete, IOption } from "@/components/common";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { loginActions, loginSelectors } from "../../../login.slice";
import { CommonUtils } from "@/utils";
import { AppConstant } from "@/constant";
import { convertDonViToOptions } from "@/utils/common.utils";

const TruongFilter = ({
  control,
  divisionCode,
  doetCode,
}: {
  control: Control<ILogin, any, ILogin>;
  divisionCode?: IOption | null;
  doetCode?: IOption | null;
}) => {
  const dispatch = useAppDispatch();
  const truongList = useAppSelector(loginSelectors.selectTruongList);

  const schoolId = useWatch({
    control,
    name: "schoolId",
  });

  const truongListOptions = useMemo(() => {
    const options = convertDonViToOptions(truongList);

    if (schoolId) {
      const newOptions = options.filter((item) => item.id !== schoolId.id);
      return [schoolId, ...newOptions];
    }
    return options;
  }, [schoolId, truongList]);

  const handleKeyDown = CommonUtils.debounce(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      const input = event.target as HTMLInputElement;
      if (event.key === "Escape") return;

      if (doetCode || divisionCode) {
        dispatch(
          loginActions.getTruongList({
            doetCode: doetCode?.code as string,
            divisionCode: divisionCode?.code as string,
            searchKey: input.value,
            ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
          })
        );
      }
    },
    AppConstant.DEBOUNCE_TIME_IN_MILLISECOND
  );

  const handleClose = (
    event: React.SyntheticEvent,
    reason: AutocompleteCloseReason
  ) => {
    const input = event.target as HTMLInputElement;
    const notChoose = !truongListOptions.find(
      (item) => item.label === input.value
    );

    if ((reason === "blur" || reason === "escape") && notChoose) {
      if (doetCode || divisionCode) {
        dispatch(
          loginActions.getTruongList({
            doetCode: doetCode?.code as string,
            divisionCode: divisionCode?.code as string,
            ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
          })
        );
      }
    }
  };

  const handleOnClickClear = (
    event: React.SyntheticEvent,
    value: string,
    reason: AutocompleteInputChangeReason
  ) => {
    if (reason === "clear") {
      if (doetCode || divisionCode) {
        dispatch(
          loginActions.getTruongList({
            doetCode: doetCode?.code as string,
            divisionCode: divisionCode?.code as string,
            ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
          })
        );
      }
    }
  };

  return (
    <>
      <AppFormAutocomplete
        rules={{
          required: true,
        }}
        control={control}
        name={"schoolId"}
        options={truongListOptions}
        autocompleteProps={{
          onClose: handleClose,
          onInputChange: handleOnClickClear,
          textFieldProps: {
            onKeyDown: handleKeyDown,
            placeholder: "Trường",
          },
          ListboxComponent: ListBoxInfiniteScroll,
          ListboxProps: {
            divisionCode,
            doetCode,
            sx: {
              overflow: "hidden !important",
              "& li": {
                whiteSpace: "normal !important",
              },
            },
          } as any,
        }}
      />
    </>
  );
};

export default TruongFilter;
