import { FilterCustomProps } from "@/components/common/TablePageLayout/ContentPage/HeaderFilter";
import { AppConstant } from "@/constant";
import { BORROW_TYPE } from "@/models/eduDevice.model";
import { useAppSelector } from "@/redux/hook";
import {
  Box,
  FormControlLabel,
  FormLabel,
  Grid,
  Radio,
  RadioGroup,
} from "@mui/material";
import { useCallback, useMemo, memo } from "react";
import TimeRangeSelector, { DEFAULT_DATE } from "./TimeRangeSelector";
import { formatDayjsWithType } from "@/utils/format.utils";
import dayjs from "dayjs";
import { selectSchoolWeekList } from "@/redux/system.slice";
import WeekSelector from "@/components/sn-common/WeekSelector";

const FilterCustom = ({ props }: { props: FilterCustomProps }) => {
  const schoolWeekList = useAppSelector(selectSchoolWeekList);

  const borrowType = useMemo(() => {
    const filterValue = props.filter?.find((item) => item.key === "borrowType")
      ?.value as string;
    return filterValue ? Number(filterValue) : BORROW_TYPE.week;
  }, [props.filter]);

  const fromDate = useMemo(() => {
    return props.filter?.find((item) => item.key === "fromDate")
      ?.value as string;
  }, [props.filter]);

  const toDate = useMemo(() => {
    return props.filter?.find((item) => item.key === "toDate")?.value as string;
  }, [props.filter]);

  const handleChangeBorrowType = useCallback(
    (value: string) => {
      const newValue = Number(value);
      if (newValue == BORROW_TYPE.longTerm) {
        props.onChangeFilterObj?.({
          borrowType: newValue,
          toDate: DEFAULT_DATE[0],
          fromDate: DEFAULT_DATE[1],
        });
      } else {
        const currentDate = dayjs();
        let defaultWeek = schoolWeekList.find((week) => {
          const weekStart = dayjs(week.fromDate);
          const weekEnd = dayjs(week.toDate);
          return currentDate.isBetween(weekStart, weekEnd, "day", "[]");
        });
        if (!defaultWeek) {
          defaultWeek = schoolWeekList?.[schoolWeekList.length - 1];
        }

        props.onChangeFilterObj?.({
          borrowType: newValue,
          toDate: defaultWeek.toDate,
          fromDate: defaultWeek.fromDate,
        });
        props.onChangeFilter("borrowType")(newValue);
      }
    },
    [props.onChangeFilter, schoolWeekList]
  );

  const handleDateChange = useCallback(
    (fromDate: Date, toDate: Date) => {
      props.onChangeFilter("fromDate")(
        formatDayjsWithType(fromDate, AppConstant.DATE_TIME_YYYYescape)
      );
      props.onChangeFilter("toDate")(
        formatDayjsWithType(toDate, AppConstant.DATE_TIME_YYYYescape)
      );
    },
    [props.onChangeFilter]
  );

  return (
    <>
      <Grid container spacing={2} direction="row" alignItems="center">
        <FormLabel>Kế hoạch</FormLabel>
        <RadioGroup
          row
          value={borrowType ?? BORROW_TYPE.week}
          onChange={(_, value) => {
            handleChangeBorrowType(value);
          }}
        >
          <FormControlLabel
            value={BORROW_TYPE.week}
            control={<Radio checked={borrowType === BORROW_TYPE.week} />}
            label="Tuần"
          />
          <FormControlLabel
            value={BORROW_TYPE.longTerm}
            control={<Radio checked={borrowType === BORROW_TYPE.longTerm} />}
            label="Dài hạn"
          />
        </RadioGroup>
      </Grid>
      <Grid width={350} direction="row" display="flex">
        {Number(borrowType) === BORROW_TYPE.week && (
          <WeekSelector
            onDateChange={handleDateChange}
            fromDate={fromDate}
            toDate={toDate}
          />
        )}
        {Number(borrowType) === BORROW_TYPE.longTerm && (
          <TimeRangeSelector onDateChange={handleDateChange} />
        )}
      </Grid>
    </>
  );
};

export default memo(FilterCustom);
