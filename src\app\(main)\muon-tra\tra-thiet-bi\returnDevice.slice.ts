import { createSlice, PayloadAction, WithSlice } from "@reduxjs/toolkit";
import { createSelector } from "@reduxjs/toolkit";
import {
  EditedDataRecord,
  IEditedDeviceData,
  ReturnDeviceFieldName,
} from "./returnDevice.model";

interface ReturnDeviceState {
  editedData: EditedDataRecord;
  headerDate: string | null;
}

const initialState: ReturnDeviceState = {
  editedData: {},
  headerDate: null,
};

export const returnDeviceSlice = createSlice({
  name: "returnDevice",
  initialState,
  reducers: {
    updateEditedValue: (
      state,
      action: PayloadAction<{
        rowId: number;
        field: ReturnDeviceFieldName;
        value: number | string;
      }>
    ) => {
      const { rowId, field, value } = action.payload;
      if (!state.editedData[rowId]) {
        state.editedData[rowId] = {};
      }
      (state.editedData[rowId] as any)[field] = value;
    },
    resetEditedData: (state) => {
      state.editedData = {};
    },
    setEditedData: (state, action: PayloadAction<EditedDataRecord>) => {
      state.editedData = action.payload;
    },
    setHeaderDate: (state, action: PayloadAction<string | null>) => {
      state.headerDate = action.payload;
    },
    updateHeaderDateForSelectedRows: (
      state,
      action: PayloadAction<{
        selectedRowIds: number[];
        headerDate: string | null;
      }>
    ) => {
      const { selectedRowIds, headerDate } = action.payload;
      selectedRowIds.forEach((rowId) => {
        if (!state.editedData[rowId]) {
          state.editedData[rowId] = {};
        }
        state.editedData[rowId].borrowReturnDate = headerDate || "";
      });
    },
  },
  selectors: {
    selectReturnDeviceState: (state) => state,
    selectEditedData: (state) => state.editedData,
    selectHeaderDate: (state) => state.headerDate,
  },
});

export const {
  updateEditedValue,
  resetEditedData,
  setEditedData,
  setHeaderDate,
  updateHeaderDateForSelectedRows,
} = returnDeviceSlice.actions;
export const { selectReturnDeviceState, selectEditedData, selectHeaderDate } =
  returnDeviceSlice.selectors;

// Extend LazyLoadedSlices để có thể inject động
declare module "@/redux/reducer" {
  export interface LazyLoadedSlices
    extends WithSlice<typeof returnDeviceSlice> {}
}

// Inject reducer
import { rootReducer } from "@/redux/reducer";
const injectedReturnDeviceSlice = returnDeviceSlice.injectInto(rootReducer);
export const returnDeviceSelectors = injectedReturnDeviceSlice.selectors;

// Stable empty objects để tránh tạo reference mới
const EMPTY_DEVICE_DATA: IEditedDeviceData = {};

// Memoized selectors với createSelector cho từng row - sử dụng injected selectors
export const makeSelectEditedDataById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId] ?? EMPTY_DEVICE_DATA
  );

export const makeSelectTotalBrokenById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId]?.totalBroken
  );

export const makeSelectTotalLostById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId]?.totalLost
  );

export const makeSelectTotalConsumedById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId]?.totalConsumed
  );

export const makeSelectNotesById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId]?.notes
  );

export const makeSelectBorrowReturnDateById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId]?.borrowReturnDate
  );

// Selector để lấy tất cả edited rows cho submit
export const selectEditedRows = returnDeviceSelectors.selectEditedData;

// Selector để lấy header date
export const selectHeaderDateFromRedux = returnDeviceSelectors.selectHeaderDate;

// Selector để kiểm tra xem tất cả các row đã chọn có cùng ngày không
export const makeSelectIsAllRowsSameDate = () =>
  createSelector(
    [
      returnDeviceSelectors.selectEditedData,
      (
        _: any,
        selectedRowIds: number[],
        originalDates: Record<number, string>
      ) => ({
        selectedRowIds,
        originalDates,
      }),
    ],
    (editedData, { selectedRowIds, originalDates }) => {
      if (!selectedRowIds || selectedRowIds.length === 0) return true;

      const allDates = selectedRowIds.map((rowId) => {
        const edited = editedData[rowId];
        return edited?.borrowReturnDate ?? originalDates[rowId];
      });

      const firstDate = allDates[0];
      return allDates.every((date) => date === firstDate);
    }
  );

export default returnDeviceSlice.reducer;
