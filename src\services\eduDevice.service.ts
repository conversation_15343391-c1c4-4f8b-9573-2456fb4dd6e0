import http from "@/api";
import { ApiConstant } from "@/constant";
import {
  DataListResponseModel,
  IPaginationModel,
} from "@/models/response.model";
import stringFormat from "string-format";
import { IDevice, IDeviceParams, IEduDevice } from "@/models/eduDevice.model";

export const getDeviceDefinitionService = (params: IDeviceParams) => {
  return http.get<DataListResponseModel<IEduDevice>>(ApiConstant.EDU_DEVICE, {
    params,
  });
};

export const getDeviceService = (params: IDeviceParams) => {
  return http.get<
    DataListResponseModel<IDeviceParams & Partial<IPaginationModel>>
  >(ApiConstant.DEVICE_LIST, {
    params,
  });
};

export const getDeviceDetailService = (id: number) => {
  return http.get<DataListResponseModel<IEduDevice>>(
    stringFormat(ApiConstant.EDU_DEVICE_DETAIL, { id })
  );
};
