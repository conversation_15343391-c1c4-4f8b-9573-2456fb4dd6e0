import { useForm, useWatch, FormProvider } from "react-hook-form";
import { Button } from "@mui/material";
import { AppModal } from "@/components/common";
import { useCallback, useEffect } from "react";
import { updateLostDamageDevice } from "../../redux/lostDamageDevice.slice";
import EditContent from "./EditContent";
import { useAppDispatch } from "@/redux/hook";
import {
  LostDamageDeviceFormData,
  EditLostDamageDeviceModalActionProps,
} from "../../lostDamgeDevice.model";
import { toast } from "sonner";

const EditLostDamageDeviceModal = ({
  isOpen,
  onClose,
  onSuccess,
  data,
}: EditLostDamageDeviceModalActionProps) => {
  const methods = useForm<LostDamageDeviceFormData>({
    defaultValues: INIT_VALUE,
  });
  const {
    control,
    reset,
    handleSubmit,
    setValue: handleSetValueForm,
  } = methods;

  const dispatch = useAppDispatch();

  const handleCloseModal = useCallback(() => {
    reset(INIT_VALUE);
    onClose();
  }, [onClose, reset]);

  const handleResetState = useCallback(() => {
    reset(INIT_VALUE);
    onClose();
    onSuccess?.();
  }, [onClose, reset, onSuccess]);

  useEffect(() => {
    if (data) {
      handleSetValueForm("inventoryTransactionId", data.inventoryTransactionId);
      handleSetValueForm(
        "inventoryTransactionName",
        data.inventoryTransactionName
      );
      handleSetValueForm(
        "inventoryTransactionNumber",
        data.inventoryTransactionNumber
      );
      handleSetValueForm("deviceId", data.deviceId);
      handleSetValueForm("deviceName", data.deviceName);
      handleSetValueForm("reportedDate", data.reportedDate);
      handleSetValueForm("totalBroken", data.totalBroken);
      handleSetValueForm("totalLost", data.totalLost);
      handleSetValueForm("totalFixed", data.totalFixed);
      handleSetValueForm("notes", data.notes);
    }
  }, [data]);

  const handleSubmitData = (valueForm: LostDamageDeviceFormData) => {
    const broken =
      Number(valueForm.totalBroken) - Number(data?.totalBroken ?? 0);
    const lost = Number(valueForm.totalLost) - Number(data?.totalLost ?? 0);

    if (broken + lost > Number(data?.deviceTotalAvailable ?? 0)) {
      toast.error(
        `Tổng số thiết bị hỏng và mất không được vượt quá số thiết bị khả dụng (${data?.deviceTotalAvailable})`
      );
      return;
    }
    if (data?.id) {
      dispatch(
        updateLostDamageDevice({
          id: Number(data.id),
          data: valueForm,
          onSuccess: handleResetState,
        })
      );
    }
  };

  const inventoryTransactionId = useWatch({
    control,
    name: "inventoryTransactionId",
  });

  return (
    <FormProvider {...methods}>
      <AppModal
        component="form"
        onSubmit={handleSubmit(handleSubmitData)}
        isOpen={isOpen}
        onClose={handleCloseModal}
        maxWidth="sm"
        fullWidth
        modalTitleProps={{
          title: "Cập nhật số liệu hỏng, mất",
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                variant="outlined"
                onClick={handleCloseModal}
                color="secondary"
              >
                Đóng
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={
                  !!inventoryTransactionId && Number(inventoryTransactionId) > 0
                }
              >
                Ghi
              </Button>
            </>
          ),
        }}
        modalContentProps={{
          content: <EditContent />,
        }}
      />
    </FormProvider>
  );
};

export default EditLostDamageDeviceModal;

const INIT_VALUE: LostDamageDeviceFormData = {
  inventoryTransactionId: 0,
  inventoryTransactionName: "",
  inventoryTransactionNumber: "",
  deviceId: 0,
  deviceName: "",
  reportedDate: "",
  totalBroken: 0,
  totalLost: 0,
  totalFixed: 0,
  notes: "",
  deviceTotalAvailable: 0,
};
