import http from "@/api";
import { ApiConstant } from "@/constant";
import { IPaginationModel } from "@/models/response.model";
import { ISchoolConfig } from "./schoolConfig.model";

export const getMoetSchoolListService = (params: IPaginationModel) =>
  http.get(ApiConstant.GET_MOET_SCHOOLS, {
    params,
  });

export const postMultiMoetSchoolListService = (body: ISchoolConfig[]) =>
  http.post(ApiConstant.ASYNC_MULTI_SCHOOL, body);
