import http from "@/api";
import { ApiConstant } from "@/constant";
import { DataResponseModel } from "@/models/response.model";
import { CommonUtils } from "@/utils";
import { extractErrorMessage } from "@/utils/common.utils";
import { toast } from "sonner";

export const postDomainConfig = async ({
  body,
  onSuccess,
}: {
  body: any;
  onSuccess?: () => void;
}) => {
  try {
    CommonUtils.toggleAppProgress(true);

    const response: DataResponseModel<any> = await http.post(
      ApiConstant.DOMAIN_CONFIG,
      body
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      toast.success("Thành công!", {
        description: "Thêm mới domain thành công.",
      });
      onSuccess?.();
    } else {
      throw new Error(response?.message || "Đã có lỗi xảy ra");
    }
  } catch (error: any) {
    const description = extractErrorMessage(error);
    toast.error("Thất bại!", {
      description,
    });
  } finally {
    CommonUtils.toggleAppProgress(false);
  }
};
