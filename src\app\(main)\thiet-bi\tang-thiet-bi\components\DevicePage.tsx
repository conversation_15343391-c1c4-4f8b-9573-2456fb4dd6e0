"use client";

import React, { useEffect, useState } from "react";
import { TablePageLayout } from "@/components/common";
import { ColumnDef } from "@tanstack/react-table";
import {
  ActionType,
  FilterConfig,
  FormConfig,
} from "@/components/common/TablePageLayout/type";
import { FormatUtils } from "@/utils";
import { formatNumber, formatPrice } from "@/utils/format.utils";
import dynamic from "next/dynamic";
import { DOCUMENT_ENTRY } from "@/constant/api.const";
import { createInjectableSaga } from "@/saga/injectableSaga";
import { systemSaga } from "@/saga/system.saga";
import { useAppDispatch, useAppStore } from "@/redux/hook";
import { systemActions } from "@/redux/system.slice";
import { eduDeviceSaga } from "@/saga/device/eduDevice.saga";
import { eduDeviceActions } from "@/redux/device/eduDevice.slice";
import { equipmentDocumentEntrySaga } from "../equipmentDocument.saga";
import { equipmentDocumentActions } from "../equipmentDocument.slice";
import { IDeviceTransaction } from "../equipmentDocument.model";
const CreateModal = dynamic(() => import("./CreateModal"), {
  ssr: false,
});
const EditModal = dynamic(() => import("./EditModal"), {
  ssr: false,
});

export const metadata = {
  title: "Tăng thiết bị",
};

const DevicePage = () => {
  const dispatch = useAppDispatch();
  const store = useAppStore();

  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    createInjectableSaga("systemReducer", systemSaga).injectInto(store);
    createInjectableSaga(
      "equipmentDocumentReducer",
      equipmentDocumentEntrySaga
    ).injectInto(store);
    createInjectableSaga("eduDeviceReducer", eduDeviceSaga).injectInto(store);

    setIsClient(true);

    return () => {
      dispatch(systemActions.systemReset());
      dispatch(equipmentDocumentActions.reset());
      dispatch(eduDeviceActions.reset());
    };
  }, []);

  if (!isClient) return null;

  return (
    <TablePageLayout<IDeviceTransaction>
      tableProps={tableProps}
      actions={ACTIONS}
      CreateModalComponent={CreateModal}
      EditModalComponent={EditModal}
      apiUrl={DOCUMENT_ENTRY}
      formConfig={FORM_FIELD_CONFIG}
      filterConfig={FILTER_CONFIG}
    />
  );
};

export default DevicePage;
const ACTIONS: ActionType[] = ["create", "delete", "update"];

const FORM_FIELD_CONFIG: FormConfig<IDeviceTransaction> = {
  deleteUrl: DOCUMENT_ENTRY,
  detailUrl: DOCUMENT_ENTRY,
};

const FILTER_CONFIG: FilterConfig[] = [
  {
    key: "dateRange",
    type: "dateRange",
    label: "Ngày chứng từ",
    keyDateRange: ["fromDate", "toDate"],
    value: [null, null],
  },
  {
    key: "searchKey",
    type: "text",
    label: "Tìm kiếm",
  },
];

const COLUMN: ColumnDef<IDeviceTransaction>[] = [
  {
    id: "documentNumber",
    accessorKey: "documentNumber",
    header: "Số phiếu",
  },
  {
    id: "documentDate",
    header: "Ngày chứng từ",
    accessorFn: (row) => FormatUtils.formatDayjsWithType(row.documentDate),
    size: 100,
  },
  {
    id: "schoolBudgetCategoryName",
    header: "Nguồn cấp",
    accessorKey: "schoolBudgetCategoryName",
  },
  {
    id: "totalDevices",
    header: "Số lượng",
    accessorFn: (row) => formatNumber(row.totalDevices),
    size: 100,
    meta: {
      align: "right",
    },
  },
  {
    id: "totalPrices",
    header: "Tổng giá trị",
    size: 100,
    accessorFn: (row) => formatPrice(row.totalPrices),
    meta: {
      align: "right",
    },
  },
  {
    id: "notes",
    header: "Lý do",
    accessorKey: "notes",
  },
];

const tableProps = {
  columns: COLUMN,
};
