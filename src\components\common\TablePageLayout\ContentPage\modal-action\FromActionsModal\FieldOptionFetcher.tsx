import http from "@/api";
import { IOption } from "@/components/common/AppAutoComplete";
import {
  DataListResponseModel,
  DataResponseModel,
} from "@/models/response.model";
import { convertDataToOptions } from "@/utils/format.utils";
import { memo, useEffect } from "react";
import { FormFieldConfig } from "../../../type";

const FieldOptionFetcher = ({
  item,
  allValues,
  watchedFilterVals,
  setOptionsObj,
}: {
  item: FormFieldConfig<any>;
  allValues: Record<string, any>;
  watchedFilterVals: Record<string, any>;
  setOptionsObj: React.Dispatch<
    React.SetStateAction<Record<string, IOption[]>>
  >;
}) => {
  useEffect(() => {
    const fetchOptions = async () => {
      const { selectConfig = {} } = item;
      const requireKeys = selectConfig.requireKey || [];
      const keyValueParams = selectConfig.keyValueParams || [];

      const isAllRequiredPresent = requireKeys.every(
        (key) =>
          allValues[key as string] !== undefined &&
          allValues[key as string] !== null
      );
      if (!isAllRequiredPresent) return;

      let params: Record<string, any> = {};
      keyValueParams.forEach((key) => {
        const val = allValues[key as string];
        if (val !== undefined && val !== null) {
          params[key as string] = val;
        }
      });

      if (watchedFilterVals[item.key]) {
        params = {
          ...params,
          ...watchedFilterVals[item.key],
        };
      }

      try {
        const res = await http.get<
          DataListResponseModel<any> | DataResponseModel<any>
        >(item.apiListUrl!, { params });

        const data = Array.isArray(res.data) ? res.data : res.data?.data || [];

        setOptionsObj((prev) => ({
          ...prev,
          [item.key]: convertDataToOptions(data),
        }));
      } catch (err) {
        console.error(`Failed to fetch options for ${item.key}:`, err);
      }
    };

    fetchOptions();
  }, [
    item.apiListUrl,
    item.key,
    ...(item.selectConfig?.requireKey || []).map((k) => allValues[k as string]),
    ...(item.selectConfig?.keyValueParams || []).map(
      (k) => allValues[k as string]
    ),
    JSON.stringify(watchedFilterVals?.[item.key]),
  ]);

  return null;
};

export default memo(FieldOptionFetcher);
