"use client";

import AppModal, { AppModalProps } from "@/components/common/modal/AppModal";
import React, { memo, useCallback, useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { Button } from "@mui/material";
import FormPanel from "./FormPanel";
import { IDeviceTransactionAction } from "../equipmentDocument.model";
import { useAppDispatch } from "@/redux/hook";
import { systemActions } from "@/redux/system.slice";
import { equipmentDocumentActions } from "../equipmentDocument.slice";

const CreateModal = ({
  fetchCurrentData,
  onClose,
  ...otherProps
}: CreateModalProps) => {
  const dispatch = useAppDispatch();

  const methods = useForm({
    defaultValues: INIT_VALUE,
  });

  const { handleSubmit, reset } = methods;

  const handleClose = () => {
    onClose();
    reset(INIT_VALUE);
    dispatch(equipmentDocumentActions.clearActionData());
  };

  const handleSubmitData = useCallback(
    (data) => {
      dispatch(
        equipmentDocumentActions.addDocumentEntry({
          data,
          onSuccess: () => {
            fetchCurrentData?.();
            handleClose();
          },
        })
      );
    },
    [fetchCurrentData]
  );

  useEffect(() => {
    dispatch(systemActions.getSourceList());
    dispatch(systemActions.getUnitList());
    dispatch(systemActions.getDeviceTypeList());
    dispatch(systemActions.getSubjectList());
    dispatch(systemActions.getGradeList());
    dispatch(systemActions.getRoomList());
    dispatch(systemActions.getTeacherComboList());
    dispatch(systemActions.getCountryList());
  }, []);

  return (
    <FormProvider {...methods}>
      <AppModal
        component="form"
        onClose={handleClose}
        onSubmit={handleSubmit(handleSubmitData)}
        fullScreen
        modalTitleProps={{
          title: "Thêm mới chứng từ",
        }}
        modalContentProps={{
          sx: {
            display: "flex",
            flexDirection: "column",
            bgcolor: "background.grey",
            px: "12px",
            py: "12px",
          },
          content: (
            <>
              <FormPanel />
            </>
          ),
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                onClick={handleClose}
                variant="outlined"
                color="secondary"
              >
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
        {...otherProps}
      />
    </FormProvider>
  );
};

export default memo(CreateModal);

type CreateModalProps = AppModalProps & {
  fetchCurrentData?: () => void;
};

export const INIT_VALUE: IDeviceTransactionAction = {
  documentNumber: "",
  documentDate: null,
  schoolBudgetCategoryId: null as any,
  notes: "",
  deviceTransactionItems: [],
  // deleteDeviceDefinitionIds: [],
};
