:root {
  --background: #ffffff;
  --foreground: #171717;
  --font-sans: "Roboto", sans-serif;
  --font-mono: var(--font-geist-mono); /* Giữ font mono nguyên như cũ */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
  overflow: hidden;
  a {
    text-decoration: none;
  }
  textarea {
    resize: none;
  }
}

.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  @media screen and (max-width: 600px) {
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
      border-radius: 50%;
    }
  }

  /* Track */
  &::-webkit-scrollbar-track {
    border-radius: 4px;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }
}

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.toast {
  font-family: var(--font-sans), Arial, Helvetica, sans-serif !important;
  gap: 12px !important;
  padding: 12px 16px !important;

  &:hover {
    padding: 12px 16px !important;
  }
}
.title-toast {
  color: #202124 !important;
  font-weight: 700 !important;
}
.description-toast {
  color: #5f6368 !important;
}
.success-toast {
  background: #e6f4ea !important;
  box-shadow: unset !important;
  border: 1px solid #34a853 !important;

  &:hover {
    border: 1px solid #34a853 !important;
  }
}
.error-toast {
  background: #fce8e6 !important;
  box-shadow: unset !important;
  border: 1px solid #ea4335 !important;

  &:hover {
    border: 1px solid #ea4335 !important;
  }
}
.warning-toast {
  background: #fef7e0 !important;
  box-shadow: unset !important;
  border: 1px solid #fbbc04 !important;

  &:hover {
    border: 1px solid #fbbc04 !important;
  }
}
.info-toast {
  background: #e8f0fe !important;
  box-shadow: unset !important;
  border: 1px solid #4285f4 !important;

  &:hover {
    border: 1px solid #4285f4 !important;
  }
}
.content-toast {
  width: 85% !important;
}
.close-button-toast {
  top: 50% !important;
  right: 12px !important;
  left: unset !important;
  transform: translateY(-50%) !important;
  background: transparent !important;
  font-size: 20px !important;
  width: 20px !important;
  height: 20px !important;
}
