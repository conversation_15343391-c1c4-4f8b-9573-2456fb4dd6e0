import { AppTable } from "@/components/common";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import { BORROW_TYPE, IBorrowRequestDevice } from "@/models/eduDevice.model";
import { formatNumber } from "@/utils/format.utils";
import { ColumnDef } from "@tanstack/react-table";
import React, { memo, useEffect, useMemo } from "react";
import { BorrowStatusCell } from "@/components/sn-common/BorrowStatusCell";
import "dayjs/locale/vi";
import dayjs from "dayjs";
dayjs.locale("vi");
import { v4 as uuid } from "uuid";
import { Typography } from "@mui/material";

const DeviceRegister = ({ data, borrowType }) => {
  const dataView = useMemo(() => {
    return groupDevicesByDateRange(data, borrowType);
  }, [borrowType, data]);

  const columns = useMemo(() => getColumns(borrowType), [borrowType]);

  return (
    <AppTable
      columns={columns}
      data={dataView}
      totalData={data?.length ?? 0}
      options={{
        getSubRows: (row) =>
          borrowType === BORROW_TYPE.week && "children" in row
            ? (row.children as IBorrowRequestDevice[])
            : undefined,
      }}
      {...TABLE_MODAL_FULL_HEIGHT}
    />
  );
};

export default memo(DeviceRegister);
const getColumns = (
  borrowType
): ColumnDef<GroupedDevice | IBorrowRequestDevice>[] => [
  {
    id: "code",
    accessorKey: "deviceCode",
    header: "Mã",
    size: 50,
    meta: {
      colSpanOnParentRow: borrowType === BORROW_TYPE.week ? 5 : undefined,
    },
    cell: ({ row }) => <CodeCell borrowType={borrowType} row={row} />,
  },
  {
    id: "deviceName",
    accessorKey: "deviceName",
    size: 150,
    header: "Tên thiết bị",
  },
  {
    id: "quantity",
    header: "SL đăng ký",
    accessorFn: (row) => formatNumber(row.quantity),
    size: 50,
    meta: {
      align: "right",
    },
  },
  {
    id: "type",
    header: "ĐVT",
    accessorKey: "deviceUnitName",
    size: 60,
  },
  {
    header: "Trạng thái",
    meta: {
      align: "center",
    },
    id: "status",
    size: 50,
    cell: ({ row }) => <BorrowStatusCell status={row.original.status} />,
  },
];

interface GroupedDevice extends IBorrowRequestDevice {
  text: string;
  children: IBorrowRequestDevice[];
}

export const groupDevicesByDateRange = (
  devices: IBorrowRequestDevice[],
  borrowType?: number
): (IBorrowRequestDevice | GroupedDevice)[] => {
  if (typeof borrowType !== "number") return [];

  // Nếu borrowType là theo ngày → trả về như cũ
  if (borrowType === BORROW_TYPE.longTerm) return devices;

  // Nếu borrowType là theo tuần → nhóm theo fromDate
  const groups: Record<string, IBorrowRequestDevice[]> = {};

  devices.forEach((device) => {
    const dateKey = dayjs(device.fromDate).format("YYYY-MM-DD");
    if (!groups[dateKey]) groups[dateKey] = [];
    groups[dateKey].push(device);
  });

  return Object.entries(groups).map(([dateStr, items]) => {
    const ref = items[0];
    const d = dayjs(dateStr);
    return {
      ...ref,
      id: uuid(),
      text: `${capitalize(d.format("dddd"))} (${d.format("DD/MM")})`,
      children: items,
    };
  });
};

export const capitalize = (str: string) =>
  str.charAt(0).toUpperCase() + str.slice(1);

const CodeCell = memo(({ row, borrowType }: { row; borrowType }) => {
  useEffect(() => {
    row.toggleExpanded(true);
  }, []);

  if (borrowType === BORROW_TYPE.longTerm || row.depth !== 0)
    return row.original.deviceCode;

  return (
    <Typography color="primary" fontWeight={500}>
      {row.original.text}
    </Typography>
  );
});
