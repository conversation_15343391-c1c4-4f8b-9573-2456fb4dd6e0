"use client";

import React, { memo } from "react";
import { useFormContext, useFormState } from "react-hook-form";
import { IBorrowRequestAction } from "../../../borrowRequestModel";
import {
  AppFormAutocomplete,
  AppFormRadio,
  GridFormContainer,
} from "@/components/common";
import { Grid } from "@mui/material";
import DateForm from "./DateForm";
import { useAppSelector } from "@/redux/hook";
import {
  selectSchoolWeekList,
  selectTeacherComboList,
} from "@/redux/system.slice";
import dayjs from "dayjs";
import { BORROW_TYPE, BORROW_TYPE_LIST } from "@/models/eduDevice.model";
import ResetButton from "./ResetButton";

const FormInfo = ({ isEdit }) => {
  const { control, setValue } = useFormContext<IBorrowRequestAction>();
  const { errors } = useFormState<IBorrowRequestAction>({ control });
  const teacher = useAppSelector(selectTeacherComboList);
  const schoolWeek = useAppSelector(selectSchoolWeekList);

  return (
    <GridFormContainer columnSpacing={5}>
      <Grid>
        <AppFormRadio
          direction="row"
          formLabelProps={{
            sx: {
              minWidth: 80,
            },
          }}
          radioProps={{
            disabled: isEdit,
          }}
          radioList={BORROW_TYPE_LIST}
          control={control}
          name="borrowType"
          label="Kế hoạch:"
          onChangeValueForm={(value) => {
            const borrowType = Number(value);
            if (borrowType === BORROW_TYPE.week) {
              const currentWeek = findClosestWeek(schoolWeek);
              setValue("schoolWeekConfigId", currentWeek ?? null);
              setValue("schoolWeekConfigName", currentWeek?.label ?? "");
            }
          }}
        />
      </Grid>
      <Grid size={3}>
        <AppFormAutocomplete<IBorrowRequestAction>
          direction="row"
          labelProps={{
            sx: {
              minWidth: 60,
            },
          }}
          control={control}
          options={teacher}
          name="teacherId"
          label="Giáo viên"
          rules={{
            required: "Giáo viên không được bỏ trống",
          }}
          autocompleteProps={{
            disabled: isEdit,
            textFieldProps: {
              helperText: errors?.teacherId?.message as unknown as string,
              error: Boolean(errors?.teacherId),
            },
          }}
        />
      </Grid>
      <DateForm isEdit={isEdit} />
      <ResetButton />
    </GridFormContainer>
  );
};

export default memo(FormInfo);

export function findClosestWeek(weeks: any[]): any | null {
  const today = dayjs();

  // Nếu nằm trong khoảng fromDate - toDate của tuần nào => return luôn tuần đó
  const exactWeek = weeks.find((week) =>
    dayjs(today).isBetween(week.fromDate, week.toDate, "day", "[]")
  );

  if (exactWeek) return exactWeek;

  // Nếu không, tìm tuần có fromDate gần nhất với hiện tại
  const sortedWeeks = weeks
    .map((week) => ({
      ...week,
      diff: Math.abs(dayjs(week.fromDate).diff(today, "day")),
    }))
    .sort((a, b) => a.diff - b.diff);

  return sortedWeeks[0] || null;
}
