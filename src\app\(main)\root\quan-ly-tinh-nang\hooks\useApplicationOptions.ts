import http from "@/api";
import { IApplication } from "@/app/(main)/root/quan-ly-tinh-nang/type";
import { ApiConstant } from "@/constant";
import { APPLICATION } from "@/constant/api.const";
import { DataListResponseModel } from "@/models/response.model";
import { extractErrorMessage } from "@/utils/common.utils";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";

const useApplicationOptions = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [applicationOptions, setApplicationOptions] = useState<IApplication[]>(
    []
  );

  useEffect(() => {
    setLoading(true);
    const fetchApplicationOptions = async () => {
      try {
        const response: DataListResponseModel<IApplication> = await http.get(
          APPLICATION
        );
        if (response.code === ApiConstant.ERROR_CODE_OK) {
          setApplicationOptions(response.data.data);
        } else {
          throw new Error(response.message);
        }
      } catch (error) {
        const description = extractErrorMessage(error);
        toast.error("Thất bại!", {
          description,
        });
      } finally {
        setLoading(false);
      }
    };
    fetchApplicationOptions();
  }, []);

  return { applicationOptions, loading };
};

export default useApplicationOptions;
