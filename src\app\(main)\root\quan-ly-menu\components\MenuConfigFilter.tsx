import { AppAutoComplete, IOption } from "@/components/common";
import {
  DEFAULT_FILTER_SIZE,
  SPACING_COLUMN_FILTER,
  SPACING_ROW_FILTER,
} from "@/components/common/TablePageLayout/ContentPage/HeaderFilter";
import { FilterValueType } from "@/components/common/TablePageLayout/type";
import { Box, Button, Divider, Grid, Stack } from "@mui/material";
import { useEffect } from "react";
import { useMenuTypeOptions } from "../hooks/useMenuTypeOptions";
import { AppAutoCompleteProps } from "@/components/common/AppAutoComplete";
import { DataConstant } from "@/constant";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { IMenuConfigFilter } from "@/models/menu.model";
import {
  menuActions,
  menuSelectors,
} from "@/app/(main)/root/quan-ly-menu/store/menu.slice";

const MenuConfigFilter = ({ onOpenModal }: { onOpenModal: () => void }) => {
  const { options, loading } = useMenuTypeOptions();
  const filter = useAppSelector(menuSelectors.filter);
  const dispatch = useAppDispatch();

  const handleChangeFilterWithKey = (
    key: keyof IMenuConfigFilter,
    value: any
  ) => {
    dispatch(menuActions.setFilterWithKey({ key, value }));
  };

  useEffect(() => {
    dispatch(
      menuActions.setFilterWithKey({
        key: "menuTypeId",
        value: options[0]?.id ?? null,
      })
    );
  }, [options]);

  return (
    <Box>
      <Grid
        px={3}
        container
        columnSpacing={SPACING_COLUMN_FILTER}
        rowSpacing={SPACING_ROW_FILTER}
        py={1}
        sx={{
          borderTop: "1px solid",
          borderColor: "border.main",
        }}
      >
        <Grid size={DEFAULT_FILTER_SIZE}>
          <AutoListFilter
            options={options}
            label={"Loại menu"}
            loading={loading}
            disableClearable
            value={filter.menuTypeId}
            onChange={(_, data) =>
              handleChangeFilterWithKey(
                "menuTypeId",
                (data as IOption)?.["id"] ?? null
              )
            }
          />
        </Grid>
        <Grid size={DEFAULT_FILTER_SIZE}>
          <AutoListFilter
            options={DataConstant.DON_VI_LIST.filter(
              (item) => item.id !== DataConstant.DON_VI_TYPE.bo
            )}
            label={"Đối tượng sử dụng"}
            disableClearable
            value={filter.groupUnitCode}
            onChange={(_, data) =>
              handleChangeFilterWithKey(
                "groupUnitCode",
                (data as IOption)?.["id"]?.toString() ?? null
              )
            }
          />
        </Grid>
        <Grid flex={1}>
          <Stack
            direction="row"
            gap={1}
            justifyContent="flex-end"
            flex={1}
            sx={{ minWidth: 0 }}
          >
            <Button variant="contained" onClick={onOpenModal}>
              Thêm mới
            </Button>
          </Stack>
        </Grid>
      </Grid>
      <Divider />
    </Box>
  );
};

export default MenuConfigFilter;

export const AutoListFilter = ({
  options,
  label,
  value,
  selectedKey,
  loading,
  ...otherProps
}: {
  options: IOption[];
  label: string;
  value?: number | string | null;
  selectedKey?: string;
} & Omit<AppAutoCompleteProps, "options" | "value" | "label">) => {
  let selectedValue: FilterValueType = null;

  if (typeof value === "object" && value !== null && "id" in value) {
    selectedValue = value as IOption;
  } else {
    selectedValue =
      typeof value !== undefined
        ? options.find((opt) => opt?.[selectedKey || "id"] === value) ?? null
        : null;
  }

  return (
    <AppAutoComplete
      options={options}
      label={label}
      loading={loading}
      value={selectedValue}
      {...otherProps}
    />
  );
};
