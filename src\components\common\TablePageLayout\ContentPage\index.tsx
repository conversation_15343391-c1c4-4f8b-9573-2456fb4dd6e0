"use client";

import React, {
  memo,
  useRef,
  useMemo,
  useC<PERSON>back,
  JSX,
  useState,
  useImperativeHandle,
  forwardRef,
  ReactNode,
} from "react";
import { ColumnDef, Row, Table, VisibilityState } from "@tanstack/react-table";

import { getElementById } from "@/utils/common.utils";
import { AppCheckbox, AppTable } from "@/components/common";

import HeaderFilter, { HeaderFilterProps } from "./HeaderFilter";
import ModalContainer, {
  ModalContainerProps,
} from "./modal-action/ModalContainer";
import EditCell from "../../table/cell/EditCell";
import { useResizeObserver } from "./hooks/useResizeObserver";
import DeleteCell from "../../table/cell/DeleteCell";
import { AppTableProps } from "../../table/AppTable";
import { ITableRef } from "../type";
import { useTableStore } from "../table-store/TableContext";
import { useModalAction } from "../modal-store/useModalAction";

const trackedElementId = "table";

export type ContentPageProps<T> = Omit<
  HeaderFilterProps<T>,
  "setColumnVisibility"
> &
  ModalContainerProps<T> & {
    tableProps?: Omit<AppTableProps<T>, "data" | "totalData">;
    /* custom table*/
    pageContent?: ReactNode;
    /* Cấu hình cột index */
    hasBaseCol?: boolean;
  };

const ContentPage = forwardRef(
  <T,>(
    {
      tableProps,
      actions,
      CreateModalComponent,
      EditModalComponent,
      formConfig,
      customActions,
      hasReload,
      visibleCol,
      filterCustom,
      collapseFilterCustom,
      pageContent,
      createFormContent,
      hasBaseCol = true,
    }: ContentPageProps<T>,
    ref: React.Ref<ITableRef>
  ) => {
    const hasUpdate = actions?.includes("update");
    const hasDelete = actions?.includes("delete");
    const hasChecked = actions?.includes("check");

    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
      {}
    );

    const { columns, hasDefaultPagination, ...otherTableProps } =
      tableProps || {};

    const resizeElementRef = useRef<HTMLDivElement | null>(null);

    const store = useTableStore<T>();
    const data = store((state) => state.data);
    const isLoading = store((state) => state.isLoading);
    const totalCount = store((state) => state.totalCount);
    const fetchCurrentData = store((state) => state.fetchCurrentData);
    const pagination = store((state) => state.pagination);
    const setPagination = store((state) => state.setPagination);
    const filterOptions = store((state) => state.filterOptions);
    const handleChangeFilter = store((state) => state.handleChangeFilter);
    const isLoadingFilter = store((state) => state.isLoadingFilter);

    useImperativeHandle(ref, () => ({
      fetchCurrentData,
      filterOptions,
      handleChangeFilter,
      isLoadingFilter,
      isLoading,
    }));

    const columnsWithIndex: ColumnDef<T>[] = useMemo(() => {
      const baseColumns: ColumnDef<T>[] = [
        {
          id: "index",
          header: "STT",
          size: 50,
          meta: { align: "center" },
          cell: ({ row, table }) => (
            <IdxCell
              row={row}
              table={table}
              hasDefaultPagination={hasDefaultPagination}
            />
          ),
        },
      ];

      const editColumn: ColumnDef<T> = {
        id: "edit",
        header: "Sửa",
        size: 50,
        cell: ({ row }) => <RenderEditCell<T> row={row.original} />,
        meta: { align: "center" },
      };

      const deleteColumn: ColumnDef<T> = {
        id: "delete",
        header: "Xóa",
        size: 50,
        cell: ({ row }) => <RenderDeleteCell<T> row={row.original} />,
        meta: { align: "center" },
      };

      const checkColumn: ColumnDef<T> = {
        id: "select",
        accessorKey: "select",
        meta: {
          align: "center",
        },
        size: 50,
        header: ({ table }) => (
          <AppCheckbox
            {...{
              checked: table.getIsAllPageRowsSelected(),
              indeterminate: table.getIsSomePageRowsSelected(),
              onChange: table.getToggleAllRowsSelectedHandler(),
            }}
          />
        ),
        cell: ({ row }) => (
          <AppCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler(),
            }}
          />
        ),
      };

      return [
        ...(hasBaseCol ? baseColumns : []),
        ...(hasUpdate ? [editColumn] : []),
        ...(hasDelete ? [deleteColumn] : []),
        ...(hasChecked ? [checkColumn] : []),
        ...(columns ?? []),
      ];
    }, [
      hasDefaultPagination,
      hasUpdate,
      hasDelete,
      hasChecked,
      hasBaseCol,
      columns,
    ]);

    const handleResize = useCallback(() => {
      const heightWindow = window.innerHeight;
      const tableEl = getElementById(trackedElementId);
      const paginationEl = getElementById("pagination");

      if (tableEl) {
        const headerHeight = tableEl.getBoundingClientRect().top;
        const paginationHeight =
          paginationEl?.getBoundingClientRect().height || 0;

        tableEl.style.height =
          heightWindow - headerHeight - paginationHeight - 1 + "px";
      }
    }, []);

    useResizeObserver(resizeElementRef, handleResize);

    return (
      <>
        <HeaderFilter<T>
          actions={actions}
          ref={resizeElementRef}
          customActions={customActions}
          visibleCol={visibleCol}
          filterCustom={filterCustom}
          collapseFilterCustom={collapseFilterCustom}
          setColumnVisibility={setColumnVisibility}
          hasReload={hasReload}
        />
        {pageContent ?? (
          <AppTable
            data={data}
            columns={columnsWithIndex}
            totalData={totalCount}
            onPageChange={hasDefaultPagination ? undefined : setPagination}
            paginationData={hasDefaultPagination ? undefined : pagination}
            isFetching={isLoading}
            columnVisibility={columnVisibility}
            hasDefaultPagination={hasDefaultPagination}
            {...otherTableProps}
          />
        )}
        <ModalContainer<T>
          formConfig={formConfig}
          actions={actions}
          CreateModalComponent={CreateModalComponent}
          EditModalComponent={EditModalComponent}
          createFormContent={createFormContent}
        />
      </>
    );
  }
);

export default memo(ContentPage) as <T>(
  props: ContentPageProps<T> & { ref?: React.Ref<any> }
) => JSX.Element;

type RenderEditCellProps<T> = {
  row?: T;
};
const RawRenderEditCell = <T,>({ row }: RenderEditCellProps<T>) => {
  const { openEditModal } = useModalAction();

  return <EditCell onClick={() => openEditModal(row)} />;
};

const RenderEditCell = memo(RawRenderEditCell) as <T>(
  props: RenderEditCellProps<T>
) => JSX.Element;
(RenderEditCell as any).displayName = "RenderEditCell";

type RenderDeleteCellProps<T> = {
  row?: T;
};

const RenderDeleteCellBase = <T,>({ row }: RenderDeleteCellProps<T>) => {
  const { openDeleteModal } = useModalAction();

  return <DeleteCell onClick={() => openDeleteModal(row)} />;
};

const RenderDeleteCell = memo(RenderDeleteCellBase) as <T>(
  props: RenderDeleteCellProps<T>
) => JSX.Element;

(RenderDeleteCell as unknown as { displayName: string }).displayName =
  "RenderDeleteCell";

type RowLike = {
  id: string;
  parentId?: string;
  depth: number;
  index: number;
};

export function generateRowIndexMap(
  rows: RowLike[],
  paginationSkip = 0,
  hasDefaultPagination = false
): Record<string, string> {
  const indexMap: Record<string, string> = {};
  const parentCountMap: Record<string, number> = {}; // Track count of children

  for (const row of rows) {
    if (row.depth === 0) {
      // Root row
      const index =
        (hasDefaultPagination ? row.index : rows.indexOf(row)) +
        1 +
        (hasDefaultPagination ? 0 : paginationSkip);
      indexMap[row.id] = `${index}`;
      parentCountMap[row.id] = 0;
    } else {
      const parentId = row.parentId ?? "";
      const parentIndex = indexMap[parentId] || "?";
      parentCountMap[parentId] = (parentCountMap[parentId] ?? 0) + 1;
      const childIndex = parentCountMap[parentId];
      indexMap[row.id] = `${parentIndex}.${childIndex}`;
    }
  }

  return indexMap;
}

export const IdxCell = <T,>({
  row,
  table,
  hasDefaultPagination,
}: {
  row: Row<T>;
  table: Table<T>;
  hasDefaultPagination?: boolean;
}) => {
  const store = useTableStore();
  const pagination = store((state) => state.pagination);
  const allRows = hasDefaultPagination
    ? table.getSortedRowModel().rows
    : table.getRowModel().rows;

  const indexMap = generateRowIndexMap(
    allRows,
    pagination.skip,
    hasDefaultPagination
  );

  return indexMap[row.id] || "";
};
