import { AppTable, AppTextField } from "@/components/common";
import {
  inventoryTransactionActions,
  inventoryTransactionSelectors,
  selectorInventoryActionItems,
} from "@/app/(main)/thiet-bi/kiem-ke/inventoryTransaction.slice";
import { IInventoryTransactionItems } from "@/app/(main)/thiet-bi/kiem-ke/type";
import React, { memo, useEffect, useMemo, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { ColumnDef } from "@tanstack/react-table";
import { Typography } from "@mui/material";
import DeleteCell from "@/components/common/table/cell/DeleteCell";
import { CommonUtils, FormatUtils } from "@/utils";
import { AppConstant } from "@/constant";
import { toast } from "sonner";

const DevicePanel = () => {
  const dispatch = useAppDispatch();
  const inventoryItems = useAppSelector(
    inventoryTransactionSelectors.inventoryItems
  );

  const columns = getColumns(({ deviceId, id }) => {
    dispatch(
      inventoryTransactionActions.deleteInventoryTransactionItem({
        deviceId,
        id,
      })
    );
  });

  return (
    <AppTable
      tableContainerProps={{
        sx: {
          height: 400,
        },
      }}
      columns={columns}
      data={inventoryItems}
      totalData={inventoryItems.length}
      hasDefaultPagination
    />
  );
};

export default memo(DevicePanel);

const getColumns = (
  onDelete: (item: { deviceId: number; id: number }) => void
): ColumnDef<IInventoryTransactionItems>[] => [
  {
    id: "index",
    header: "STT",
    cell: ({ row }) => {
      return <Typography>{row.index + 1}</Typography>;
    },
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "delete",
    header: "Xóa",
    cell: ({ row }) => (
      <DeleteCell
        onClick={() =>
          onDelete({ deviceId: row.original.deviceId, id: row.original.id })
        }
      />
    ),
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "deviceCode",
    header: "Mã thiết bị",
    accessorKey: "deviceCode",
    size: 100,
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
    size: 150,
  },
  {
    id: "roomName",
    header: "Kho/phòng",
    accessorKey: "roomName",
    size: 100,
  },
  {
    id: "schoolSubjectName",
    header: "Môn học",
    accessorKey: "schoolSubjectName",
  },
  {
    id: "deviceUnitName",
    header: "Đơn vị tính",
    accessorKey: "deviceUnitName",
    size: 100,
  },
  {
    id: "quantity",
    header: "Số lượng",
    accessorKey: "quantity",
    accessorFn: (row) => FormatUtils.formatNumber(row.quantity),
    meta: {
      align: "right",
    },
    size: 100,
  },
  {
    id: "before",
    header: "Trước kiểm kê",
    columns: [
      {
        id: "beforeBrokenTotal",
        header: "Hỏng",
        accessorKey: "beforeBrokenTotal",
        accessorFn: (row) => FormatUtils.formatNumber(row.beforeBrokenTotal),
        size: 70,
        meta: {
          align: "right",
        },
      },
      {
        id: "beforeLostTotal",
        header: "Mất",
        accessorKey: "beforeLostTotal",
        accessorFn: (row) => FormatUtils.formatNumber(row.beforeLostTotal),
        size: 70,
        meta: {
          align: "right",
        },
      },
      {
        id: "beforeAvailableTotal",
        header: "Còn SD",
        accessorKey: "beforeAvailableTotal",
        accessorFn: (row) => FormatUtils.formatNumber(row.beforeAvailableTotal),
        size: 70,
        meta: {
          align: "right",
        },
      },
    ],
  },
  {
    id: "after",
    header: "Sau kiểm kê",
    columns: [
      {
        id: "afterBrokenTotal",
        header: "Hỏng",
        accessorKey: "afterBrokenTotal",
        cell: ({ row }) => (
          <DeviceInputField
            field="afterBrokenTotal"
            maxValue={row.original.quantity}
            minValue={row.original.beforeBrokenTotal}
            currentValue={row.original.afterBrokenTotal}
            isEdit={false}
            rowId={row.original.deviceId}
          />
        ),
      },
      {
        id: "afterLostTotal",
        header: "Mất",
        accessorKey: "afterLostTotal",
        cell: ({ row }) => (
          <DeviceInputField
            field="afterLostTotal"
            maxValue={row.original.quantity}
            minValue={row.original.beforeLostTotal}
            currentValue={row.original.afterLostTotal}
            isEdit={false}
            rowId={row.original.deviceId}
          />
        ),
      },
      {
        id: "afterAvailableTotal",
        header: "Còn SD",
        accessorKey: "afterAvailableTotal",
        cell: ({ row }) => (
          <AfterChangeTotal
            rowId={row.original.deviceId}
            quantity={row.original.quantity}
          />
        ),
        size: 70,
        meta: {
          align: "right",
        },
      },
      {
        id: "afterChangeTotal",
        header: "Thừa",
        accessorKey: "afterChangeTotal",
        cell: ({ row }) => (
          <DeviceInputField
            field="afterChangeTotal"
            maxValue={row.original.quantity}
            minValue={0}
            currentValue={row.original.afterChangeTotal}
            isEdit={false}
            rowId={row.original.deviceId}
          />
        ),
      },
    ],
  },
  {
    id: "change",
    header: "Chênh lệch",
    columns: [
      {
        id: "changeBrokenTotal",
        header: "Hỏng",
        cell: ({ row }) => <ChangeTotalBroken rowId={row.original.deviceId} />,
        size: 70,
        meta: {
          align: "right",
        },
      },
      {
        id: "changeLostTotal",
        header: "Mất",
        cell: ({ row }) => <ChangeTotalLost rowId={row.original.deviceId} />,
        size: 70,
        meta: {
          align: "right",
        },
      },
    ],
  },
  {
    id: "note",
    header: "Ghi chú",
    cell: ({ row }) => (
      <NoteCell rowId={row.original.deviceId} notes={row.original.notes} />
    ),
  },
];

type DeviceInputFieldProps = {
  rowId: number;
  field: "afterBrokenTotal" | "afterLostTotal" | "afterChangeTotal";
  maxValue: number;
  minValue: number;
  currentValue: number | null;
  isEdit: boolean;
  label?: string;
};

const DeviceInputField = memo(
  ({
    rowId,
    field,
    maxValue,
    minValue,
    currentValue,
    isEdit,
  }: DeviceInputFieldProps) => {
    const dispatch = useAppDispatch();
    const [value, setValue] = useState(
      isEdit ? currentValue : minValue.toString()
    );
    const deviceFind = useAppSelector(selectorInventoryActionItems(rowId));
    const [isError, setIsError] = useState(false);

    const changeValueDebounce = useMemo(
      () =>
        CommonUtils.debounce((newValue: number) => {
          dispatch(
            inventoryTransactionActions.changeInventoryActionItems({
              deviceId: rowId,
              [field]: newValue,
            })
          );
        }, AppConstant.DEBOUNCE_TIME_IN_MILLISECOND),
      [dispatch, rowId, field]
    );

    const handleChange = (val: string) => {
      setValue(val);
      changeValueDebounce(Number(val));
    };

    const handleBlur = () => {
      if (field === "afterChangeTotal") {
        return;
      }

      if (value === "") {
        setValue("0");
        setIsError(false);
        return;
      }

      const inputVal = Number(value);
      const maxVal = maxValue;

      if (inputVal < minValue) {
        setIsError(true);
        toast.warning(
          `Số lượng thiết bị không được nhỏ hơn số lượng trước kiểm kê, vui lòng kiểm tra lại. (Số lượng trước kiểm kê: ${FormatUtils.formatNumber(
            minValue
          )})`
        );
        return;
      }

      const isError =
        (field === "afterBrokenTotal" &&
          inputVal + (deviceFind?.afterLostTotal || 0) > maxVal) ||
        (field === "afterLostTotal" &&
          inputVal + (deviceFind?.afterBrokenTotal || 0) > maxVal);

      if (isError) {
        setIsError(true);
        toast.warning(
          `Tổng số lượng thiết bị hỏng và mất không được phép lớn hơn số lượng trong kho, vui lòng kiểm tra lại. (Số lượng còn trong kho: ${maxVal})`
        );
      } else {
        setIsError(false);
      }
    };

    useEffect(() => {
      if (isEdit) setValue(String(currentValue));
    }, [isEdit, currentValue]);

    useEffect(() => {
      if (deviceFind?.[field]) {
        if (field !== "afterChangeTotal" && deviceFind[field]) {
          setIsError(
            deviceFind[field] > maxValue || deviceFind[field] < minValue
          );
        }
        setValue(String(deviceFind[field]));
      }
    }, []);

    return (
      <AppTextField
        type="number"
        slotProps={{ htmlInput: { min: 0 } }}
        onBlur={handleBlur}
        value={value}
        error={isError}
        onChange={(e) => handleChange(e.currentTarget.value)}
      />
    );
  }
);

const NoteCell = ({ rowId, notes }: { rowId: number; notes: string }) => {
  const dispatch = useAppDispatch();
  const [note, setNote] = useState(notes);
  const deviceFind = useAppSelector(selectorInventoryActionItems(rowId));

  const changeValueDebounce = useMemo(
    () =>
      CommonUtils.debounce((newValue: string) => {
        dispatch(
          inventoryTransactionActions.changeInventoryActionItems({
            deviceId: rowId,
            notes: newValue,
          })
        );
      }, AppConstant.DEBOUNCE_TIME_IN_MILLISECOND),
    [dispatch, rowId]
  );

  const handleChange = (val: string) => {
    setNote(val);
    changeValueDebounce(val);
  };

  useEffect(() => {
    if (deviceFind?.notes) {
      setNote(deviceFind.notes);
    }
  }, []);

  return (
    <AppTextField
      value={note}
      onChange={(e) => handleChange(e.currentTarget.value)}
    />
  );
};

const AfterChangeTotal = ({
  rowId,
  quantity,
}: {
  rowId: number;
  quantity: number;
}) => {
  const deviceFind = useAppSelector(selectorInventoryActionItems(rowId));

  const value = useMemo(() => {
    return (
      Number(quantity) -
      (Number(deviceFind?.afterBrokenTotal) +
        Number(deviceFind?.afterLostTotal))
    );
  }, [deviceFind, quantity]);

  return (
    <Typography>{FormatUtils.formatNumber(value < 0 ? 0 : value)}</Typography>
  );
};

const ChangeTotalLost = ({ rowId }: { rowId: number }) => {
  const deviceFind = useAppSelector(selectorInventoryActionItems(rowId));

  const value = useMemo(() => {
    return (
      Number(deviceFind?.afterLostTotal) - Number(deviceFind?.beforeLostTotal)
    );
  }, [deviceFind]);

  return <Typography>{FormatUtils.formatNumber(value)}</Typography>;
};

const ChangeTotalBroken = ({ rowId }: { rowId: number }) => {
  const deviceFind = useAppSelector(selectorInventoryActionItems(rowId));

  const value = useMemo(() => {
    return (
      Number(deviceFind?.afterBrokenTotal) -
      Number(deviceFind?.beforeBrokenTotal)
    );
  }, [deviceFind]);

  return <Typography>{FormatUtils.formatNumber(value)}</Typography>;
};
