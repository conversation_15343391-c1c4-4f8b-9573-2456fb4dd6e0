import http from "@/api";
import { ApiConstant } from "@/constant";
import { DELETE_MULTIPLE_SCHOOL_WEEK } from "@/constant/api.const";
import { DataResponseModel } from "@/models/response.model";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import { toast } from "sonner";

const useActionSchoolWeek = () => {
  const handleDeleteMultiple = async (
    ids: number[],
    onSuccess?: () => void
  ) => {
    toggleAppProgress(true);
    try {
      const response: DataResponseModel<any> = await http.delete(
        DELETE_MULTIPLE_SCHOOL_WEEK,
        ids
      );

      if (response.code === ApiConstant.ERROR_CODE_OK) {
        toast.success("Thành công!", {
          description: "Xóa tuần học thành công!",
        });
        onSuccess?.();
      } else {
        throw response;
      }
    } catch (error: any) {
      const description = extractErrorMessage(error);
      toast.error("Thất bại!", {
        description,
      });
    } finally {
      toggleAppProgress(false);
    }
  };

  return {
    handleDeleteMultiple,
  };
};

export default useActionSchoolWeek;
