"use client";

import { AppFormTextField, AppModal } from "@/components/common";
import { AppModalProps } from "@/components/common/modal/AppModal";
import { But<PERSON>, Stack } from "@mui/material";
import { useCallback } from "react";
import { useForm } from "react-hook-form";
import { IAccount } from "@/app/(main)/he-thong/quan-ly-tai-khoan/account.model";
import useResetPassword from "../hooks/useResetPassword";

const ResetPasswordModal = ({
  data,
  onClose,
  ...otherProps
}: ResetPasswordModalProps) => {
  const {
    control,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm({
    values: { newPassword: "" },
  });

  const handleReset = useResetPassword();

  const handleCloseModal = useCallback(() => {
    onClose();
    reset();
  }, []);

  const handleSubmitData = useCallback(
    (values: { newPassword: string }) => {
      if (data?.id) {
        const payload = {
          newPassword: values.newPassword,
        };
        handleReset(payload, data.id, handleCloseModal);
      }
    },
    [data, handleCloseModal]
  );

  return (
    <AppModal
      component="form"
      onSubmit={handleSubmit(handleSubmitData)}
      onClose={handleCloseModal}
      modalTitleProps={{
        title: "Reset mật khẩu",
      }}
      modalContentProps={{
        content: (
          <>
            <AppFormTextField
              label="Mật khẩu mới"
              control={control}
              name="newPassword"
              rules={{
                required: "Đây là trường bắt buộc",
                maxLength: {
                  value: 50,
                  message: "Mật khẩu không vượt quá 50 ký tự!",
                },
              }}
              textfieldProps={{
                autoComplete: "off",
                error: !!errors?.newPassword,
                helperText: errors?.newPassword?.message,
              }}
            />
          </>
        ),
      }}
      modalActionsProps={{
        children: (
          <>
            <Button
              color="secondary"
              variant="outlined"
              onClick={handleCloseModal}
            >
              Đóng
            </Button>
            <Button type="submit" variant="contained">
              Ghi
            </Button>
          </>
        ),
      }}
      {...otherProps}
    />
  );
};

type ResetPasswordModalProps = AppModalProps & {
  data: IAccount | null;
};

export default ResetPasswordModal;
