import { Button, ButtonProps } from "@mui/material";
import React, { memo } from "react";

const IconButtonCustom = (props: ButtonProps) => {
  return (
    <Button
      sx={{
        width: 36,
        height: 36,
        minWidth: "unset",
        fontSize: 20,
        "&,&:hover": {
          bgcolor: "common.white",
        },
      }}
      color="secondary"
      variant="outlined"
      {...props}
    />
  );
};

export default memo(IconButtonCustom);
