export interface IAccount {
  id: number;
  libraryCard: string;
  cardTypeId: number;
  cardStatusId: number;
  userName: string;
  fullName: string;
  lastName: string;
  firstName: string;
  gender: number;
  avatar: string;
  dateOfBirth: string;
  status: number;
  accountCode: string;
  email: string;
  phone: string;
  createdAt: string;
  createdBy: number;
  updatedAt: string;
  updatedBy: number;
  oldId: number;
  schoolName: string;
  applicationFunctionName: string;
  applicationFunctionCode: string;
}

export interface IAccountParams {
  searchKey: string;
  status: number;
}
