import {
  deviceActions,
  selectorDeviceSelected,
  selectorIsGroupByDefinition,
} from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.slice";
import useRecordLoss from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/hooks/useRecordLoss";
import {
  AppFormTextField,
  AppModal,
  AppTextField,
  GridFormContainer,
} from "@/components/common";
import AppFormDatePicker from "@/components/common/form/AppFormDatePicker";
import { DataConstant } from "@/constant";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { Button, FormLabel, Grid, Stack } from "@mui/material";
import { memo, useCallback, useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";
import DeviceSelector from "@/components/sn-common/ChooseDevicePopup/DeviceSelector";
import { formatNumber } from "@/utils/format.utils";
import dayjs from "dayjs";

const RecordLoss = ({
  open,
  onClose,
  fetchCurrentData,
}: {
  open: boolean;
  onClose: () => void;
  fetchCurrentData?: () => void;
}) => {
  const dispatch = useAppDispatch();
  const deviceSelected = useAppSelector(selectorDeviceSelected);
  const isGroupByDefinition = useAppSelector(selectorIsGroupByDefinition);
  const { handleRecordLoss } = useRecordLoss();
  const methods = useForm({
    defaultValues: INITIAL_VALUES,
  });
  const {
    control,
    setValue,
    formState: { errors },
    reset,
    handleSubmit,
  } = methods;

  const handleNumberFieldChange = useCallback(
    (event: any) => {
      const value = event.target.value;
      if (value === "" || value === null || value === undefined) {
        setValue(event.target.name, 0);
      }
    },
    [setValue]
  );

  const handleClose = useCallback(() => {
    reset(INITIAL_VALUES);
    onClose();
  }, [onClose, reset]);

  const handleSubmitData = (data: any) => {
    if (Number(data.totalBroken) === 0 && Number(data.totalLost) === 0) {
      toast.warning("Cảnh báo!", {
        description: "Số lượng hỏng và mất không được để trống",
      });
      return;
    }

    const totalSum = Number(data.totalBroken) + Number(data.totalLost);
    const totalAvailable = Number(data.totalAvailable);

    if (totalSum > totalAvailable) {
      toast.warning("Cảnh báo!", {
        description: `Không thể ghi nhận hỏng mất cho thiết bị với số lượng lớn hơn số lượng còn trong kho, vui lòng kiểm tra lại. (Số lượng còn trong kho: ${totalAvailable}).`,
      });
      return;
    }

    handleRecordLoss(data, () => {
      fetchCurrentData?.();
      handleClose();
    });
  };

  useEffect(() => {
    if (open && deviceSelected.length > 0) {
      setValue("deviceId", deviceSelected[0].id);
      setValue("totalAvailable", deviceSelected[0].totalAvailable);
      if (isGroupByDefinition) {
        dispatch(
          deviceActions.changeDeviceFilterWithKey({
            key: "deviceDefinitionId",
            value: deviceSelected[0].id,
          })
        );
      }
    }
  }, [deviceSelected, open, isGroupByDefinition]);

  return (
    <FormProvider {...methods}>
      <AppModal
        isOpen={open}
        onClose={handleClose}
        component={"form"}
        onSubmit={handleSubmit(handleSubmitData)}
        modalTitleProps={{
          title: "Ghi nhận hỏng/mất",
        }}
        modalContentProps={{
          content: (
            <GridFormContainer>
              <Grid container spacing={2}>
                <Grid size={12}>
                  {isGroupByDefinition ? (
                    <DeviceSelector />
                  ) : (
                    <Stack direction={"column"} spacing={0.5}>
                      <FormLabel>Thiết bị</FormLabel>
                      <AppTextField
                        disabled
                        value={`${deviceSelected?.[0]?.deviceName} (${
                          deviceSelected?.[0]?.deviceCode
                        } - SL: ${formatNumber(
                          deviceSelected?.[0]?.quantity
                        )})`}
                      />
                    </Stack>
                  )}
                </Grid>
                <Grid size={12}>
                  <AppFormDatePicker
                    control={control}
                    name="reportedDate"
                    label="Thời gian"
                    rules={{
                      required: "Thời gian không được để trống",
                    }}
                    datePickerProps={{
                      maxDate: null,
                    }}
                  />
                </Grid>
                <Grid size={6}>
                  <AppFormTextField
                    control={control}
                    name="totalBroken"
                    label="Số lượng hỏng"
                    textfieldProps={{
                      type: "number",
                      inputProps: { min: 0 },
                      onBlur: handleNumberFieldChange,
                    }}
                  />
                </Grid>

                <Grid size={6}>
                  <AppFormTextField
                    control={control}
                    name="totalLost"
                    label="Số lượng mất"
                    textfieldProps={{
                      type: "number",
                      inputProps: { min: 0 },
                      onBlur: handleNumberFieldChange,
                    }}
                  />
                </Grid>
                <Grid size={12}>
                  <AppFormTextField
                    control={control}
                    name="notes"
                    label="Ghi chú"
                    textfieldProps={{
                      multiline: true,
                      minRows: 3,
                      error: !!errors.notes,
                      helperText: errors.notes?.message as string,
                      placeholder: "Nhập ghi chú",
                    }}
                    rules={{
                      maxLength: {
                        value: 500,
                        message: "Ghi chú không được dài quá 500 ký tự",
                      },
                    }}
                  />
                </Grid>
              </Grid>
            </GridFormContainer>
          ),
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                variant="outlined"
                onClick={handleClose}
                color="secondary"
              >
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
      />
    </FormProvider>
  );
};

export default memo(RecordLoss);

const INITIAL_VALUES = {
  deviceId: 0,
  totalAvailable: 0,
  deviceName: "",
  totalBroken: 0,
  totalLost: 0,
  notes: "",
  reportedDate: dayjs(),
};
