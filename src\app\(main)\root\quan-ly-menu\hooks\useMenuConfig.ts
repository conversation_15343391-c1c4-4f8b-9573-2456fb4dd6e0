import http from "@/api";
import {
  menuActions,
  menuSelectors,
} from "@/app/(main)/root/quan-ly-menu/store/menu.slice";
import { ApiConstant } from "@/constant";
import { MENU_CONFIG } from "@/constant/api.const";
import { IMenuTree } from "@/models/menu.model";
import { DataListResponseModel } from "@/models/response.model";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { extractErrorMessage } from "@/utils/common.utils";
import { toast } from "sonner";

const useMenuConfig = () => {
  const dispatch = useAppDispatch();
  const filter = useAppSelector(menuSelectors.filter);
  const fetchMenuConfig = async () => {
    try {
      const res = await http.get<DataListResponseModel<IMenuTree>>(
        MENU_CONFIG,
        {
          params: filter,
        }
      );
      if (res.code === ApiConstant.ERROR_CODE_OK) {
        dispatch(menuActions.setMenuConfig(res.data.data));
      } else {
        throw new Error(res.message || "Đã có lỗi xảy ra");
      }
    } catch (error: any) {
      toast.error("Thất bại!", {
        description: extractErrorMessage(error),
      });
    }
  };

  return {
    fetchMenuConfig,
  };
};

export default useMenuConfig;
