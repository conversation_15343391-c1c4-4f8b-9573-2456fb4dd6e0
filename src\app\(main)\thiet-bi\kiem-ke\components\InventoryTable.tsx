import ChooseDeviceModal from "@/app/(main)/thiet-bi/kiem-ke/components/ChooseDeviceModal/ChooseDeviceModal";
import ChooseTeacherModal from "@/app/(main)/thiet-bi/kiem-ke/components/ChooseTeacherModal";
import DevicePanel from "@/app/(main)/thiet-bi/kiem-ke/components/panel/DevicePanel";
import TeamPanel from "@/app/(main)/thiet-bi/kiem-ke/components/panel/TeamPanel";
import AppFormLayoutPanel from "@/components/common/AppFormLayoutPanel";
import { Stack, Tab, Tabs } from "@mui/material";
import React, { useState } from "react";

const InventoryTable = () => {
  const [tabValue, setTabValue] = useState(TAB.devices);

  return (
    <AppFormLayoutPanel
      isDoc
      childrenProps={{
        sx: {
          p: 0,
        },
      }}
    >
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        py={0.75}
      >
        <Tabs value={tabValue} onChange={(_, value) => setTabValue(value)}>
          <Tab label="Thông tin thiết bị" value={TAB.devices} />
          <Tab label="Ban kiểm kê" value={TAB.teams} />
        </Tabs>

        <Stack direction="row" spacing={1} pr={2}>
          {tabValue === TAB.devices && <ChooseDeviceModal />}
          {tabValue === TAB.teams && <ChooseTeacherModal />}
        </Stack>
      </Stack>
      {tabValue === TAB.devices && <DevicePanel />}
      {tabValue === TAB.teams && <TeamPanel />}
    </AppFormLayoutPanel>
  );
};

export default InventoryTable;

const enum TAB {
  devices,
  teams,
}
