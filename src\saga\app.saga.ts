import { IOption } from "@/components/common";
import { ApiConstant, AppConstant, EnvConstant } from "@/constant";
import { IDomainInfo, ISchoolYear, IUserInfo } from "@/models/app.model";
import { IMenuItemTree, MENU_TYPE } from "@/models/menu.model";
import {
  DataListResponseModel,
  DataResponseModel,
} from "@/models/response.model";
import { appActions } from "@/redux/app.slice";
import { AppServices } from "@/services";
import { CommonUtils } from "@/utils";
import { PayloadAction } from "@reduxjs/toolkit";
import { all, call, put, takeLatest } from "redux-saga/effects";
import { toast } from "sonner";

export function* getSchoolMenuConfigTree(action: PayloadAction<MENU_TYPE>) {
  try {
    const response: DataListResponseModel<IMenuItemTree> = yield call(
      AppServices.getMenuSideBar,
      action.payload
    );
    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(appActions.getMenuSideBarSuccess(response.data.data));
    }
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại!", {
      description: CommonUtils.extractErrorMessage(error),
    });
  }
}

export function* getDomainInfoSaga() {
  try {
    const response: DataResponseModel<IDomainInfo> = yield call(
      AppServices.getDomainInfo
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(appActions.getDomainInfoSuccess(response.data));
    } else {
      throw new Error(response.message as any);
    }
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại!", {
      description: CommonUtils.extractErrorMessage(error),
    });
    yield put(appActions.appFailure(error));
  }
}

export function* getUserInfoSaga(
  action: PayloadAction<{ onSuccess?: () => void }>
) {
  try {
    const response: DataResponseModel<IUserInfo> = yield call(
      AppServices.getUserInfoService
    );
    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(appActions.getUserInfoSuccess(response.data));
      if (action?.payload?.onSuccess) {
        yield call(action.payload.onSuccess);
      }
    } else {
      yield put(appActions.appFailure({}));
      yield put(appActions.clearToken());
    }
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại!", {
      description: CommonUtils.extractErrorMessage(error),
    });
  }
}

export function* changePasswordSaga(
  action: PayloadAction<{
    password: string;
    newPassword: string;
    confirmPassword: string;
    onSuccess?: () => void;
  }>
) {
  try {
    const response: DataResponseModel<any> = yield call(
      AppServices.changePasswordService,
      {
        password: action.payload.password,
        newPassword: action.payload.newPassword,
        confirmPassword: action.payload.confirmPassword,
      }
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(appActions.changePasswordSuccess());
      toast.success("Thành công!", {
        description: "Thay đổi mật khẩu thành công.",
      });

      if (action.payload.onSuccess) {
        yield call(action.payload.onSuccess);
      }
    } else {
      throw response;
    }
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    yield put(appActions.appFailure(error));
    toast.error("Thất bại!", {
      description: CommonUtils.extractErrorMessage(error),
    });
  }
}

export function* getSchoolYearListSaga() {
  try {
    const response: DataListResponseModel<ISchoolYear> = yield call(
      AppServices.getSchoolYearListService
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      const schoolYearData = Array.isArray(response.data)
        ? response.data
        : response.data?.data || [];
      yield put(appActions.getSchoolYearListSuccess(schoolYearData));
    } else {
      throw new Error(response.message as any);
    }
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    yield put(appActions.appFailure(error));
    toast.error("Thất bại!", {
      description: CommonUtils.extractErrorMessage(error),
    });
  }
}

export function* changeSchoolYearSaga(
  action: PayloadAction<{
    schoolYear: IOption;
    semester: IOption;
    onSuccess?: () => void;
  }>
) {
  try {
    const response: DataResponseModel<any> = yield call(
      AppServices.changeSchoolYearService,
      {
        schoolYear: action.payload.schoolYear.id as number,
        semester: action.payload.semester.id as number,
      }
    );
    if (response.code === ApiConstant.ERROR_CODE_OK) {
      localStorage.setItem(
        AppConstant.COOKIE_KEY.semester,
        JSON.stringify(action.payload.semester)
      );
      yield put(
        appActions.changeSchoolYearSuccess({
          schoolYear: action.payload.schoolYear,
          semester: action.payload.semester,
        })
      );
      toast.success("Thành công!", {
        description: "Thay đổi năm học thành công.",
      });

      if (action.payload.onSuccess) {
        yield call(action.payload.onSuccess);
      }
    } else {
      throw response;
    }
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    yield put(appActions.appFailure(error));
    toast.error("Thất bại!", {
      description: CommonUtils.extractErrorMessage(error),
    });
  }
}

export function* appSaga() {
  yield all([
    takeLatest(appActions.getMenuSideBar.type, getSchoolMenuConfigTree),
    takeLatest(appActions.getDomainInfo.type, getDomainInfoSaga),
    takeLatest(appActions.getUserInfo.type, getUserInfoSaga),
    takeLatest(appActions.changePassword.type, changePasswordSaga),
    takeLatest(appActions.getSchoolYearList.type, getSchoolYearListSaga),
    takeLatest(appActions.changeSchoolYear.type, changeSchoolYearSaga),
  ]);
}
