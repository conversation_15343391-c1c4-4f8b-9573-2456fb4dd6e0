"use client";

import { TablePageLayout } from "@/components/common";
import StatusCell from "@/components/common/table/cell/StatusCell";
import {
  FormFieldConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import { DEVICE_TYPE, STATUS_DEVICE_TYPE } from "@/constant/api.const";
import { ColumnDef } from "@tanstack/react-table";
import React, { useRef } from "react";
import { STATUS_TYPE_LIST } from "@/constant/data.const";
import { updateStatusService } from "@/services/app.service";
import { CheckIcon } from "@/components/icons";
import SyncModal from "./SyncModal";
import { IDeviceType } from "@/models/system.model";

const DeviceTypePage = () => {
  const tableRef = useRef<ITableRef>(null);
  const columns = getColumns(tableRef);

  return (
    <TablePageLayout<IDeviceType>
      ref={tableRef}
      fetchAll
      customActions={
        <SyncModal
          onFetchData={() => {
            tableRef.current?.fetchCurrentData?.();
          }}
        />
      }
      visibleCol={VISIBLE_COL}
      apiUrl={DEVICE_TYPE}
      tableProps={{
        columns,
        hasDefaultPagination: true,
      }}
      filterConfig={[
        {
          key: "status",
          type: "select",
          label: "Trạng thái hiển thị",
          size: 2.4,
          options: STATUS_TYPE_LIST,
        },
        {
          key: "searchKey",
          type: "text",
          label: "Tìm kiếm",
          size: 2.4,
        },
      ]}
      actions={["create", "update", "delete"]}
      formConfig={{
        deleteUrl: DEVICE_TYPE,
        detailUrl: DEVICE_TYPE,
        createUrl: DEVICE_TYPE,
        updateUrl: DEVICE_TYPE,
        createFields: CREATE_CONFIG,
        updateFields: CREATE_CONFIG,
      }}
    />
  );
};

export default DeviceTypePage;

const getColumns = (
  tableRef: React.RefObject<ITableRef | null>
): ColumnDef<IDeviceType>[] => [
  {
    id: "code",
    header: "Mã",
    accessorKey: "code",
    size: 60,
  },
  {
    id: "name",
    header: "Tên thiết bị",
    accessorKey: "name",
    size: 150,
  },
  {
    id: "note",
    accessorKey: "note",
    header: "Ghi chú",
    size: 200,
    meta: { align: "center" },
  },
  {
    id: "isSystem",
    header: "Loại hệ thống",
    size: 60,
    meta: { align: "center" },
    cell: ({ row }) =>
      Boolean(row.original.isSystem) && (
        <CheckIcon sx={{ fontSize: 24, color: "primary.main" }} />
      ),
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Hiển thị",
    size: 60,
    cell: ({ row }) => (
      <StatusCell
        status={row.original.status}
        onStatusChange={(_, value) => {
          updateStatusService({
            id: row.original.id,
            status: Number(value),
            onSuccess: tableRef?.current?.fetchCurrentData,
            url: STATUS_DEVICE_TYPE,
          });
        }}
      />
    ),
    meta: { align: "center" },
  },
];

const VISIBLE_COL = [
  { id: "code", name: "Mã" },
  { id: "name", name: "Tên thiết bị" },
  { id: "note", name: "Ghi chú" },
  { id: "status", name: "Hiển thị" },
];

const CREATE_CONFIG: FormFieldConfig<IDeviceType>[] = [
  {
    key: "code",
    type: "text",
    label: "Mã",
    size: 12,
    textFieldProps: {
      autoFocus: true,
    },
    rules: {
      required: "Mã thiết bị không được để trống",
      maxLength: {
        value: 50,
        message: "Mã không được dài quá 50 ký tự",
      },
    },
  },
  {
    key: "name",
    type: "text",
    label: "Tên thiết bị",
    size: 12,
    rules: {
      required: "Tên thiết bị không được để trống",
      maxLength: {
        value: 250,
        message: "Tên thiết bị không được dài quá 250 ký tự",
      },
    },
  },
  {
    key: "note",
    type: "text",
    label: "Ghi chú",
    textFieldProps: {
      multiline: true,
      minRows: 3,
    },
    size: 12,
    rules: {
      maxLength: {
        value: 500,
        message: "Ghi chú không được dài quá 500 ký tự",
      },
    },
  },
  {
    key: "status",
    type: "toggle",
    label: "Trạng thái hiển thị",
    size: 12,
  },
];
