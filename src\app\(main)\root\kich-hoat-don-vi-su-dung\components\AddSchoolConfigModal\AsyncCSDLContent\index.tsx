import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { Stack } from "@mui/material";
import { useEffect } from "react";
import {
  IMoetSchoolFilter,
  schoolConfigActions,
  selectFilterModal,
} from "../../../schoolConfig.slice";
import AsyncCSDLTable from "./AsyncCSDLTable";
import Filter from "./Filter";

const AsyncCSDLContent = () => {
  const dispatch = useAppDispatch();
  const filter = useAppSelector(selectFilterModal);

  useEffect(() => {
    if (filter.schoolLevelMoet && filter.doetCode) {
      const newFilter = convertFilter(filter);

      dispatch(
        schoolConfigActions.getMoetSchoolDataList({
          ...newFilter,
        })
      );
    }
  }, [filter]);

  return (
    <Stack>
      <Filter />
      <AsyncCSDLTable />
    </Stack>
  );
};

export default AsyncCSDLContent;

const convertFilter = (filter: IMoetSchoolFilter) => {
  const newFilter = { ...filter };
  if (Object.keys(newFilter).length) {
    for (const key in newFilter) {
      const newValue = newFilter[key];

      switch (key) {
        case "schoolLevelMoet":
          newFilter[key] =
            typeof newValue !== "string" ? (newValue?.id as string) : newValue;
          break;

        case "doetCode":
        case "divisionCode":
          newFilter[key] =
            typeof newValue !== "string"
              ? (newValue?.code as string)
              : newValue;
          break;

        default:
          break;
      }
    }
  }
  return newFilter;
};
