import React, { memo } from "react";
import { SvgIcon, SvgIconProps } from "@mui/material";

const OpenEyePasswordIcon = ({ sx, ...otherProps }: SvgIconProps) => {
  return (
    <SvgIcon
      viewBox="0 0 24 24"
      sx={{ fontSize: "inherit", width: "24px", height: "24px", ...sx }}
      {...otherProps}
    >
      <g clipPath="url(#clip0_416_1354)">
        <path
          d="M17.9981 8.82042C16.0565 7.05642 13.6829 6.00042 11.0621 6.00042C8.44129 6.00042 6.06768 7.05642 4.12608 8.82042C2.76528 10.0588 1.75248 11.6308 1.72848 11.9884C1.73088 11.938 1.75488 12.0052 1.81008 12.1228C1.91808 12.346 2.08608 12.6268 2.30448 12.9364C2.81808 13.6612 3.53328 14.446 4.35168 15.1588C6.39169 16.9324 8.73649 18.0004 11.0621 18.0004C13.3877 18.0004 15.7325 16.9324 17.7701 15.1612C18.5885 14.4484 19.3037 13.6636 19.8173 12.9388C20.0357 12.6292 20.2061 12.3484 20.3141 12.1252C20.3669 12.0172 20.3885 11.95 20.3957 11.9788C20.3501 11.5996 19.3445 10.0444 17.9981 8.82042ZM11.0621 19.3348C5.06209 19.3348 0.396484 13.3348 0.396484 12.0004C0.396484 10.666 4.39489 4.66602 11.0621 4.66602C17.7293 4.66602 21.7277 10.666 21.7277 12.0004C21.7277 13.3348 17.0621 19.3348 11.0621 19.3348ZM11.0621 16.0012C13.2701 16.0012 15.0629 14.2108 15.0629 12.0004C15.0629 9.79002 13.2701 7.99962 11.0621 7.99962C8.85408 7.99962 7.06129 9.79242 7.06129 12.0004C7.06129 14.2084 8.85408 16.0012 11.0621 16.0012ZM11.0621 14.6668C9.58849 14.6668 8.39569 13.474 8.39569 12.0004C8.39569 10.5268 9.58849 9.33402 11.0621 9.33402C12.5357 9.33402 13.7285 10.5268 13.7285 12.0004C13.7285 13.474 12.5357 14.6668 11.0621 14.6668Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_416_1354">
          <rect width="22" height="22" fill="white" />
        </clipPath>
      </defs>
    </SvgIcon>
  );
};

export default memo(OpenEyePasswordIcon);
