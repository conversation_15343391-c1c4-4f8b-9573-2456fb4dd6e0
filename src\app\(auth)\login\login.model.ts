import { IOption } from "@/components/common";
import { DataConstant } from "@/constant";

export interface ILogin {
  username: string;
  password: string;
  schoolId: IOption | null;
  unitLevel?: null | IOption;
  doetCode?: null | IOption;
  divisionCode?: null | IOption;
  donVi?: null | IOption;
}

export interface IDonviParams {
  doetCode?: string;
  divisionCode?: string | null;
  groupUnitCode?: string;
  schoolLevel?: number | null;
  searchKey?: string;
  status?: DataConstant.BOOLEAN_TYPE;
  isGetDivision?: number;
  skip?: number;
  take?: number;
}

export interface IUserLogin {
  username: string;
  password: string;
  schoolId: number | string;
}

export interface ITokenLoginResponseModel {
  access_token: string;
  expired_time: number;
}
