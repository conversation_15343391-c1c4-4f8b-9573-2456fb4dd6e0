import { IFunction } from "@/app/(main)/he-thong/phan-quyen/type";
import { rootReducer } from "@/redux/reducer";
import { createSlice, WithSlice } from "@reduxjs/toolkit";
import { PayloadAction } from "@reduxjs/toolkit";

/* ------------- Initial State ------------- */
export interface IInitialState {
  functions: IFunction[];
  selectedNodes: number[];
  applicationFeatureId: number | null;
}

const initialState: IInitialState = {
  functions: [],
  selectedNodes: [],
  applicationFeatureId: null,
};

/* ------------- Selector ------------- */
const selectors = {
  functions: (state: IInitialState) => state.functions,
  selectedNodes: (state: IInitialState) => state.selectedNodes,
  applicationFeatureId: (state: IInitialState) => state.applicationFeatureId,
};

/* ------------- Reducers ------------- */
const reducers = {
  setApplicationFeatureId: (
    state: IInitialState,
    action: PayloadAction<number>
  ) => {
    state.applicationFeatureId = action.payload;
  },
  setFunctions: (state: IInitialState, action: PayloadAction<IFunction[]>) => {
    state.functions = action.payload;
  },
  setSelectedNodes: (state: IInitialState, action: PayloadAction<number[]>) => {
    state.selectedNodes = action.payload;
  },
};

/* ------------- Slice ------------- */
const permissionSlice = createSlice({
  name: "permissionReducer",
  initialState,
  reducers,
  selectors,
});

export const permissionActions = permissionSlice.actions;

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices extends WithSlice<typeof permissionSlice> {}
}

const injectedPermissionSlice = permissionSlice.injectInto(rootReducer);

export const permissionSelectors = injectedPermissionSlice.selectors;
