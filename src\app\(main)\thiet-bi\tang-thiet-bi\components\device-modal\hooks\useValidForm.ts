import React from "react";
import { IDeviceTransactionItems } from "../../../equipmentDocument.model";
import { validEquipmentDocumentEntry } from "../../../equipmentDocument.service";
import { DataResponseModel } from "@/models/response.model";
import { ApiConstant } from "@/constant";
import { toast } from "sonner";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import { cleanIdString } from "../../../helper";
import { useAppSelector } from "@/redux/hook";
import { selectEquipmentDocumentEntry } from "../../../equipmentDocument.slice";

const useValidForm = () => {
  const deviceTransactionItems = useAppSelector(selectEquipmentDocumentEntry);

  const handleValid = async (
    data: IDeviceTransactionItems,
    onSuccess: () => void
  ) => {
    try {
      toggleAppProgress(true);

      const isSameCode = deviceTransactionItems.some(
        (item) => item.deviceCode === data.deviceCode && item.id !== data.id
      );
      if (isSameCode) {
        toast.error("Thất bại", {
          description: "Mã thiết bị đã tồn tại trong chứng từ!",
        });
        return;
      }

      const cleanData = cleanIdString(data);

      const newData = {
        ...cleanData,
        id: cleanData.itemId ?? cleanData.id,
        deviceCodes:
          cleanData.devices
            ?.filter((item) => item.id === 0)
            .map((item) => item.code) ?? [],
      };

      const res: DataResponseModel<unknown> = await validEquipmentDocumentEntry(
        newData
      );
      if (res.code === ApiConstant.ERROR_CODE_OK) {
        onSuccess();
        return;
      }
      throw res;
    } catch (error) {
      toast.error("Thất bại", {
        description: extractErrorMessage(error),
      });
    } finally {
      toggleAppProgress(false);
    }
  };
  return handleValid;
};

export default useValidForm;
