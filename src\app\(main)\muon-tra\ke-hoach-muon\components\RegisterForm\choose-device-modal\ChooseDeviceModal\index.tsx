import React, { memo, useEffect, useState } from "react";
import { AppModal } from "@/components/common";
import { <PERSON><PERSON>, <PERSON><PERSON>, Step, StepLabel, Stack } from "@mui/material";
import DeviceTable from "./DeviceTable";
import {
  FormProvider,
  useForm,
  useFormContext,
  useWatch,
} from "react-hook-form";
import {
  BORROW_TYPE,
  BorrowStatusEnum,
  IEduDevice,
} from "@/models/eduDevice.model";
import InfoTable from "./InfoTable";
import { IBorrowRequestAction } from "../../../../borrowRequestModel";
import dayjs from "dayjs";
import { v4 as uuid } from "uuid";
import { IOption, toOption } from "@/components/common/AppAutoComplete";
import { toast } from "sonner";

const ChooseDeviceModal = ({
  isOpen,
  onClose,
  onSuccess,
  initDate,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  initDate?;
}) => {
  const { setValue: setParentValue, getValues: getParrentValue } =
    useFormContext<IBorrowRequestAction>();

  const [activeStep, setActiveStep] = useState(0);

  const methods = useForm({ defaultValues: INIT });
  const { reset, getValues, setValue } = methods;

  const handleClose = () => {
    onClose();
    setActiveStep(0);
    reset(INIT);
  };

  const handleSubmitData = () => {
    const data = getValues();
    if (!data.fromDate || !data.toDate) {
      toast.warning("Cảnh báo", {
        description: "Bạn vui lòng chọn khoảng thời gian đăng ký mượn",
      });

      return;
    }
    const borrowRequestDevices = getParrentValue("borrowRequestDevices");
    const borrowType = getParrentValue("borrowType");

    const errorItem = data.deviceSelected.find(
      (item) => Number(item.totalBorrow) > Number(item.totalBorrowReady)
    );

    if (errorItem) {
      const max = errorItem.totalBorrowReady;
      const name = errorItem.deviceName;
      const code = errorItem.code;

      const description = `Số lượng "${name} (${code})" còn lại không đủ. Thầy cô có thể mượn tối đa (${max}) thiết bị.`;

      toast.warning("Cảnh báo", { description });

      return;
    }

    setParentValue("borrowRequestDevices", [
      ...borrowRequestDevices,
      ...data.deviceSelected.map((item) => ({
        id: uuid(),
        status: BorrowStatusEnum.Register,
        deviceId: item.id as number,
        deviceName: item.deviceName,
        deviceCode: item.code,
        subjectId: toOption(
          item.schoolSubjectId,
          item.schoolSubjectName
        ) as IOption,
        subjectName: item.schoolSubjectName,
        roomId: toOption(item.roomId, item.roomName) as IOption,
        roomName: item.roomName,
        fromDate: data.fromDate as Date,
        toDate: data.toDate as Date,
        quantity: Number(item.totalBorrow),
        deviceUnitName: item.deviceUnitName,
        deviceUnitId: item.deviceUnitId,
        gradeCode: item.gradeCode,
        borrowType,
        totalBorrowReady: item.totalBorrowReady,
      })),
    ]);

    handleClose();
    onSuccess?.();
  };

  const handleNext = () => {
    if (activeStep === 1) {
      handleSubmitData();
    } else {
      const deviceSelected = getValues("deviceSelected");
      if (deviceSelected.length === 0) {
        toast.warning("Cảnh báo", {
          description: "Bạn chưa chọn thiết bị cần đăng ký mượn",
        });
        return;
      }
      setActiveStep((prev) => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => prev - 1);
  };

  useEffect(() => {
    if (isOpen) {
      const borrowType = getParrentValue("borrowType");
      const borrowFromDate = getParrentValue("borrowFromDate");
      const borrowToDate = getParrentValue("borrowToDate");
      const schoolWeekConfigId = getParrentValue("schoolWeekConfigId");

      setValue("borrowType", borrowType);
      if (Number(borrowType) === BORROW_TYPE.longTerm) {
        setValue("toDate", borrowToDate);
        setValue("fromDate", borrowFromDate);
      } else {
        setValue(
          "toDate",
          schoolWeekConfigId?.id
            ? dayjs(schoolWeekConfigId?.toDate).toDate()
            : null
        );
        setValue(
          "fromDate",
          schoolWeekConfigId?.id
            ? dayjs(schoolWeekConfigId?.fromDate).toDate()
            : null
        );
      }
      if (initDate) {
        const date = dayjs(initDate).toDate();
        setValue("toDate", schoolWeekConfigId?.id ? date : null);
        setValue("fromDate", schoolWeekConfigId?.id ? date : null);
      }
    }
  }, [isOpen]);
  return (
    <>
      <FormProvider {...methods}>
        <AppModal
          onSubmit={(e) => {
            e.stopPropagation();
          }}
          component="form"
          fullScreen
          isOpen={isOpen}
          onClose={handleClose}
          modalTitleProps={{
            title: "Chọn thiết bị",
          }}
          modalContentProps={{
            sx: {
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              pb: 0,
            },
            content: (
              <>
                <Stepper activeStep={activeStep} sx={{ mb: 2, width: 500 }}>
                  <Step>
                    <StepLabel>Chọn thiết bị</StepLabel>
                  </Step>
                  <Step>
                    <StepLabel>Nhập thông tin bổ sung</StepLabel>
                  </Step>
                </Stepper>
                <Stack flex={1} width="100%" minHeight={0}>
                  {activeStep === 0 && <DeviceTable />}
                  {activeStep === 1 && <InfoTable />}
                </Stack>
              </>
            ),
          }}
          modalActionsProps={{
            children: (
              <>
                <Button variant="outlined" onClick={handleClose}>
                  Đóng
                </Button>
                <Button
                  variant="outlined"
                  disabled={activeStep <= 0}
                  onClick={handleBack}
                >
                  Quay lại
                </Button>
                <Button variant="contained" onClick={handleNext}>
                  {activeStep === 1 ? "Hoàn tất" : "Tiếp theo"}
                </Button>
              </>
            ),
          }}
        />
      </FormProvider>
    </>
  );
};

export default memo(ChooseDeviceModal);
export interface IChooseDeviceForm {
  deviceSelected: (IEduDevice & {
    totalBorrow: any;
    code: string;
    originalId?: number;
  })[];
  fromDate: Date | null;
  toDate: Date | null;
  borrowType?: BORROW_TYPE;
}
const INIT: IChooseDeviceForm = {
  deviceSelected: [],
  fromDate: null,
  toDate: null,
  borrowType: BORROW_TYPE.longTerm,
};
