import React, { memo } from "react";
import { SvgIcon, SvgIconProps } from "@mui/material";

const GridViewIcon = ({ sx, ...otherProps }: SvgIconProps) => {
  return (
    <SvgIcon
      viewBox="0 0 24 24"
      sx={{ fontSize: "inherit", ...sx }}
      {...otherProps}
    >
      <path d="M3 3h8v8H3V3zm10 0h8v8h-8V3zM3 13h8v8H3v-8zm10 0h8v8h-8v-8z" />
    </SvgIcon>
  );
};

export default memo(GridViewIcon);
