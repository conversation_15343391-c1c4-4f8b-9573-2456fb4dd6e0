import TreeView from "@/components/common/tree/TreeView";
import { AppConstant } from "@/constant";
import { ITreeData } from "@/models/types";
import { Box, Typography } from "@mui/material";
import { TreeItem } from "@mui/x-tree-view";
import { memo, useMemo } from "react";

const RenderTreeFeatureSelect = ({
  data,
  idNodeTreeSelected,
  onSelectTree,
  onClosePopover,
  nodeChildren,
  currentDataTree,
  onSetValue,
  value,
  expandAll = true,
}: RenderTreeFeatureSelect) => {
  const renderTree = (nodes: ITreeData[]) => {
    return nodes.map((node) => {
      const disableTree = node.id === idNodeTreeSelected;
      const notAllowSelectNode =
        idNodeTreeSelected === node?.id ||
        (idNodeTreeSelected === node?.parentId &&
          idNodeTreeSelected != AppConstant.DEFAULT_TREE);

      return (
        <TreeItem
          key={node.id}
          itemId={node.id.toString()}
          disabled={disableTree}
          sx={{
            "& .MuiTreeItem-content": {
              paddingLeft: "8px",
              borderRadius: 0,
            },
            "& .MuiCollapse-root": {
              marginLeft: "16px",
              paddingLeft: "10px",
              borderLeft: "1px dashed",
              borderColor: "primary.main",
            },
          }}
          label={
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-start",
                alignItems: "center",
                width: "100%",
                padding: "8px 8px 8px 0",
                cursor: notAllowSelectNode ? "not-allowed" : "pointer",
              }}
              onClick={
                notAllowSelectNode
                  ? () => {}
                  : () => {
                      onSetValue(node.name);
                      onClosePopover();
                      onSelectTree({
                        name: node.name,
                        id: Number(node.id),
                        parentId: node.parentId,
                        applicationId: node?.applicationId,
                      });
                    }
              }
            >
              <Typography
                sx={{
                  whiteSpace: "nowrap",
                  color: "text.primary",
                }}
              >
                {node.name}
              </Typography>
            </Box>
          }
        >
          {Array.isArray(node[nodeChildren]) && renderTree(node[nodeChildren])}
        </TreeItem>
      );
    });
  };

  const defaultExpanded = useMemo(() => {
    return expandAll ? findIdsToExpand(data) : [];
  }, [data, expandAll]);

  return (
    <TreeView defaultExpandedItems={defaultExpanded}>
      <Box sx={{ width: "402px" }}>
        {value === AppConstant.ROOT_NODE_TREE ? (
          <></>
        ) : (
          <TreeItem
            itemId={AppConstant.ROOT_NODE_TREE}
            label={
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-start",
                  alignItems: "center",
                  width: "100%",
                  padding: "8px 8px 8px 0",
                }}
                onClick={() => {
                  onSetValue("Chọn làm thư mục gốc");
                  onClosePopover();
                  onSelectTree({
                    name: "Chọn làm thư mục gốc",
                    id: AppConstant.NONE_ID,
                    parentId: AppConstant.NONE_ID,
                    applicationId: currentDataTree?.applicationId,
                  });
                }}
              >
                <Typography
                  sx={{
                    whiteSpace: "nowrap",
                    color: "text.primary",
                  }}
                >
                  Chọn làm thư mục gốc
                </Typography>
              </Box>
            }
          />
        )}

        {renderTree(data)}
      </Box>
    </TreeView>
  );
};

export interface RenderTreeFeatureSelect {
  data: ITreeData[];
  value: string;
  label?: string;
  currentDataTree?: ITreeData | null;
  onSelectTree: (dataTreeItems: NodeData) => void;
  defaultExpanded?: string[];
  idNodeTreeSelected?: number;
  onClosePopover: () => void;
  nodeChildren: string;
  onSetValue: (value: string) => void;
  expandAll?: boolean;
}

export interface NodeData {
  name: string;
  id: number;
  parentId: number | null;
  applicationId?: number | null;
}

export default memo(RenderTreeFeatureSelect);

const findChildrenIds = (children: ITreeData[], nodeChildren: string) => {
  let childIds: number[] = [];
  if (children) {
    for (let i = 0; i < children.length; i++) {
      childIds.push(Number(children[i].id));
      const grandchildrenIds = findChildrenIds(
        children[i][nodeChildren],
        nodeChildren
      );
      childIds = [...childIds, ...grandchildrenIds];
    }
  }
  return childIds;
};

const findIdsToExpand = (array: ITreeData[]) => {
  const ids: string[] = [];

  const recursiveSearch = (subArray: ITreeData[]) => {
    subArray.forEach((item) => {
      if (item.children && item.children.length > 0) {
        ids.push(item.id.toString());
        recursiveSearch(item.children);
      }
    });
  };
  recursiveSearch(array);

  return ids;
};
