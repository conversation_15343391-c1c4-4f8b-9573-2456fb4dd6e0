import React, { memo } from "react";
import { Box, Typography, Grid } from "@mui/material";
import { IMenuGroup } from "@/models/menu.model";
import MenuCard from "./MenuCard";
import { CardVariant } from "./enums";

type MenuGroupProps = {
  groups: IMenuGroup[];
};

const MenuGroup: React.FC<MenuGroupProps> = ({ groups }) => {
  return groups?.map((group) => (
    <Grid size={12} key={group.id}>
      <Box>
        <Typography
          variant="h6"
          sx={{
            fontWeight: 700,
            color: "text.primary",
            mb: 1,
          }}
        >
          {group.name}
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {group?.items.map((item) => (
          <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={item.id}>
            <MenuCard item={item} variant={CardVariant.CARD} />
          </Grid>
        ))}
      </Grid>
    </Grid>
  ));
};

export default memo(MenuGroup);
