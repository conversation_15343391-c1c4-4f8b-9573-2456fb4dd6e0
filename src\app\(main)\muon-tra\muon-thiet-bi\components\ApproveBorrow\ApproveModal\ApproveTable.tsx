import { TablePageLayout } from "@/components/common";
import { BORROW_DEVICE } from "@/constant/api.const";
import { ColumnDef } from "@tanstack/react-table";
import { memo, useMemo, useRef, useState } from "react";
import { IBorrowDeviceModal } from "../../../borrowDevice.modal";
import { BORROW_TYPE_LIST } from "@/models/eduDevice.model";
import {
  FilterConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import { AppConstant } from "@/constant";
import { formatDayjsWithType, formatNumber } from "@/utils/format.utils";
import { useAppSelector } from "@/redux/hook";
import { selectTeacherComboList } from "@/redux/system.slice";
import { Typography } from "@mui/material";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import { BorrowStatusEnum } from "@/models/eduDevice.model";

const ApproveTable = ({
  selectedRows = [],
  borrowType,
  onSelectedRowsChange,
  approveTableSelectedRows,
}: {
  selectedRows?: IBorrowDeviceModal[];
  borrowType?: number;
  onSelectedRowsChange?: (rows: IBorrowDeviceModal[]) => void;
  approveTableSelectedRows?: IBorrowDeviceModal[];
}) => {
  const tableRef = useRef<ITableRef>(null);
  const [totalDataCount, setTotalDataCount] = useState(0);

  const teacherOptions = useAppSelector(selectTeacherComboList);

  const autoFilter = useMemo((): {
    teacherIds?: number[];
    fromDate?: string;
    toDate?: string;
  } => {
    if (!selectedRows || selectedRows.length === 0) {
      return {};
    }

    const teacherIds = Array.from(
      new Set(selectedRows.map((row) => row.teacherId).filter(Boolean))
    );

    const dates = selectedRows
      .map((row) => new Date(row.fromDate))
      .filter((date) => !isNaN(date.getTime()));

    if (dates.length === 0) {
      return { teacherIds };
    }

    const minDate = new Date(Math.min(...dates.map((d) => d.getTime())));
    const maxDate = new Date(Math.max(...dates.map((d) => d.getTime())));

    return {
      teacherIds,
      fromDate: formatDayjsWithType(minDate, AppConstant.DATE_TIME_YYYYescape),
      toDate: formatDayjsWithType(maxDate, AppConstant.DATE_TIME_YYYYescape),
    };
  }, [selectedRows]);

  const filterConfig: FilterConfig[] = useMemo(() => {
    return [
      {
        key: "teacherIds",
        type: "select" as const,
        label: "Giáo viên",
        size: 3,
        options: teacherOptions,
        isMulti: true,
        value: autoFilter.teacherIds?.map((id) =>
          teacherOptions.find((option) => option.id === id)
        ),
      },
      {
        key: "borrowType",
        type: "select" as const,
        label: "Loại mượn",
        size: 2,
        options: BORROW_TYPE_LIST,
        value: borrowType,
        required: true,
      },
      {
        key: "fromDate",
        type: "dateRange" as const,
        label: "Khoảng thời gian",
        keyDateRange: ["fromDate", "toDate"],
        size: 2.5,
        value:
          autoFilter?.fromDate && autoFilter?.toDate
            ? [new Date(autoFilter.fromDate), new Date(autoFilter.toDate)]
            : undefined,
        required: true,
      },
      {
        key: "searchKey",
        type: "text" as const,
        label: "Tìm kiếm",
        size: 2,
      },
      {
        key: "status",
        value: BorrowStatusEnum.Register,
        required: true,
      },
    ];
  }, [autoFilter, borrowType, teacherOptions]);

  const tableProps = useMemo(() => {
    return {
      ...TABLE_MODAL_FULL_HEIGHT,
      columns: columns,
      rowSelected: selectedRows,
      onRowSelectionChange: (selectedRowsData: IBorrowDeviceModal[]) => {
        onSelectedRowsChange?.(selectedRowsData);
      },
    };
  }, [selectedRows, onSelectedRowsChange]);

  const handleFormatData = (data: IBorrowDeviceModal[]) => {
    setTotalDataCount(data.length);
    return data;
  };

  return (
    <TablePageLayout
      ref={tableRef}
      key={`approve-${selectedRows.length}-${JSON.stringify(autoFilter)}`}
      apiUrl={BORROW_DEVICE}
      methodFetch="POST"
      tableProps={tableProps}
      filterConfig={filterConfig}
      formatData={handleFormatData}
      actions={["check"]}
      collapseFilterCustom={() => (
        <>
          <Typography
            variant="body1"
            color="primary"
            sx={{ width: "100%", textAlign: "right" }}
          >
            {approveTableSelectedRows?.length} / {totalDataCount} thiết bị đã
            chọn
          </Typography>
        </>
      )}
    />
  );
};

const columns: ColumnDef<IBorrowDeviceModal>[] = [
  {
    header: "Ngày mượn",
    accessorKey: "fromDate",
    cell: ({ row }) => {
      return formatDayjsWithType(row.original.fromDate);
    },
    size: 6,
  },
  {
    header: "Giáo viên",
    accessorKey: "teacherName",
  },
  {
    header: "Mã thiết bị",
    accessorKey: "deviceCode",
  },
  {
    header: "Tên thiết bị",
    accessorKey: "deviceName",
  },
  {
    header: "Kho/phòng",
    accessorKey: "roomName",
  },
  {
    header: "SL đăng ký",
    accessorKey: "quantity",
    size: 1,
    cell: ({ row }) => {
      return formatNumber(row.original.quantity);
    },
    meta: {
      align: "right",
    },
  },
  {
    header: "SL cho mượn",
    accessorKey: "quantityApprove",
    cell: ({ row }) => {
      return formatNumber(row.original.quantity);
    },
    size: 1,
    meta: {
      align: "right",
    },
  },
];

export default memo(ApproveTable);
