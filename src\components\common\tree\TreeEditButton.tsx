"use client";

import { IconButton, IconButtonProps } from "@mui/material";
import { EditIcon } from "@/components/icons";
import { memo } from "react";

const TreeEditButton = ({
  onClickButton,
  ...otherProps
}: ActionIconButtonProps) => {
  return (
    <IconButton
      color="primary"
      size="small"
      sx={{ fontSize: "22px", width: 24, height: 24, color: "primary.main" }}
      onClick={(e) => {
        e.stopPropagation();
        onClickButton();
      }}
      {...otherProps}
    >
      <EditIcon />
    </IconButton>
  );
};

export default memo(TreeEditButton);

type ActionIconButtonProps = IconButtonProps & {
  onClickButton: () => void;
};
