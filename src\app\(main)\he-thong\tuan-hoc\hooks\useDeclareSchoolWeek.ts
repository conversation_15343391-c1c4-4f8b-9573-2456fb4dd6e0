import http from "@/api";
import { ApiConstant, AppConstant } from "@/constant";
import { CONFIG_SCHOOL_WEEK } from "@/constant/api.const";
import { DataResponseModel } from "@/models/response.model";
import { useAppSelector } from "@/redux/hook";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import { formatDayjsWithType } from "@/utils/format.utils";
import { toast } from "sonner";

const useDeclareSchoolWeek = () => {
  const schoolYear = useAppSelector(
    (state) => state.appReducer.schoolYearSelected?.id
  );
  const handleConfigDeclare = async (data, onSuccess?: () => void) => {
    toggleAppProgress(true);
    try {
      const response: DataResponseModel<any> = await http.post(
        CONFIG_SCHOOL_WEEK,
        {
          ...data,
          semester: data.semester?.id,
          schoolYear,
          fromDate: formatDayjsWithType(
            data.fromDate,
            AppConstant.DATE_TIME_YYYYescape
          ),
        }
      );
      if (response.code === ApiConstant.ERROR_CODE_OK) {
        toast.success("Thành công!", {
          description: "Khai báo tuần học thành công!",
        });
        onSuccess?.();
      } else {
        throw response;
      }
    } catch (error: any) {
      const description = extractErrorMessage(error);
      toast.error("Thất bại!", {
        description,
      });
    } finally {
      toggleAppProgress(false);
    }
  };

  return {
    handleConfigDeclare,
  };
};

export default useDeclareSchoolWeek;
