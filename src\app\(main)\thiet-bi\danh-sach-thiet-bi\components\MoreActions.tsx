import CancelDeviceTransfer from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/components/CancelDeviceTransfer";
import RecordLoss from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/components/RecordLoss";
import TransferDevice from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/components/TransferDevice";
import {
  deviceActions,
  selectorDeviceSelected,
  selectorIsGroupByDefinition,
} from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.slice";
import IconButtonCustom from "@/components/common/TablePageLayout/ContentPage/HeaderFilter/IconButtonCustom";
import { DataConstant } from "@/constant";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { MenuItem, MenuList, Popover } from "@mui/material";
import React, { useCallback, useState } from "react";
import { toast } from "sonner";

const MoreActions = ({
  fetchCurrentData,
}: {
  fetchCurrentData?: () => void;
}) => {
  const dispatch = useAppDispatch();
  const [anchor, setAnchor] = useState<HTMLButtonElement | null>(null);
  const deviceSelected = useAppSelector(selectorDeviceSelected);
  const [openTransfer, setOpenTransfer] = useState(false);
  const [openRecordLoss, setOpenRecordLoss] = useState(false);
  const [openCancelTransfer, setOpenCancelTransfer] = useState(false);
  const isGroupByDefinition = useAppSelector(selectorIsGroupByDefinition);

  const handleOpenTransfer = useCallback(() => {
    setAnchor(null);
    if (deviceSelected.length === 0) {
      toast.warning("Thông báo", {
        description: "Bạn chưa chọn thiết bị cần điều chuyển.",
        position: "bottom-right",
      });
      return;
    }
    if (isGroupByDefinition) {
      dispatch(
        deviceActions.getDeviceCombo({
          deviceDefinitionIds: deviceSelected?.map((item) => item.id),
        })
      );
    } else {
      dispatch(deviceActions.transferDevice());
    }
    setOpenTransfer(true);
  }, [deviceSelected, isGroupByDefinition]);

  const handleOpenRecordLoss = useCallback(() => {
    setAnchor(null);
    if (deviceSelected.length === 0) {
      toast.warning("Thông báo", {
        description: "Bạn chưa chọn thiết bị cần ghi nhận hỏng, mất.",
        position: "bottom-right",
      });
      return;
    }

    if (deviceSelected.length !== 1) {
      toast.warning("Thông báo", {
        description:
          "Bạn chỉ có thể ghi nhận hỏng/mất nhanh cho từng thiết bị.",
        position: "bottom-right",
      });
      return;
    }
    setOpenRecordLoss(true);
  }, [deviceSelected]);

  const handleOpenCancelTransfer = useCallback(() => {
    if (deviceSelected.length === 0) {
      toast.warning("Thông báo", {
        description: "Bạn chưa chọn thiết bị cần hủy điều chuyển.",
        position: "bottom-right",
      });
      return;
    }

    const ids = deviceSelected?.map((item) => item.id);

    const filter = isGroupByDefinition
      ? {
          deviceDefinitionIds: ids,
        }
      : { deviceIds: ids };

    dispatch(deviceActions.setDeviceTransferParams(filter));

    setAnchor(null);
    setOpenCancelTransfer(true);
  }, [deviceSelected, isGroupByDefinition]);

  return (
    <>
      <IconButtonCustom
        aria-label="Thao tác thêm"
        onClick={(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) =>
          setAnchor(e.currentTarget)
        }
      >
        ◦◦◦
      </IconButtonCustom>
      <Popover
        open={Boolean(anchor)}
        anchorEl={anchor}
        onClose={() => setAnchor(null)}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        slotProps={{
          paper: {
            sx: {
              mt: 0.5,
            },
          },
        }}
      >
        <MenuList>
          <MenuItem onClick={handleOpenTransfer}>Điều chuyển TB</MenuItem>
          <MenuItem onClick={handleOpenCancelTransfer}>
            Hủy điều chuyển TB
          </MenuItem>
          <MenuItem onClick={handleOpenRecordLoss}>Ghi nhận hỏng/mất</MenuItem>
        </MenuList>
      </Popover>
      <TransferDevice
        open={openTransfer}
        onClose={() => setOpenTransfer(false)}
        fetchCurrentData={fetchCurrentData}
      />
      <RecordLoss
        open={openRecordLoss}
        onClose={() => setOpenRecordLoss(false)}
        fetchCurrentData={fetchCurrentData}
      />
      <CancelDeviceTransfer
        open={openCancelTransfer}
        onClose={() => setOpenCancelTransfer(false)}
        fetchCurrentData={fetchCurrentData}
      />
    </>
  );
};

export default MoreActions;
