import AppFormLayoutPanel from "@/components/common/AppFormLayoutPanel";
import {
  FullScreenIcon,
  FullScreenIconExit,
  PlusIcon,
} from "@/components/icons";
import {
  Button,
  IconButton,
  Skeleton,
  Stack,
  Tab,
  Tabs,
  Tooltip,
} from "@mui/material";
import { memo, useCallback, useMemo, useState } from "react";
import { useAppDispatch } from "@/redux/hook";
import InfoTableTab from "./InfoTableTab";
import TeamleadTable from "./TeamleadTable";
import ChooseDeviceModal from "../ChooseDeviceModal";
import ChooseTeamLeadModal from "../ChooseTeamLeadModal";

const TabTable = () => {
  const dispatch = useAppDispatch();
  const [tabValue, setTabValue] = useState<TAB>(TAB.info);
  const [delayedTab, setDelayedTab] = useState<TAB>(TAB.info);
  const [isPending, setIsPending] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const handleChangeTab = useCallback((_: React.SyntheticEvent, value: TAB) => {
    setTabValue(value);
    setIsPending(true);
    setTimeout(() => {
      setDelayedTab(value);
      setIsPending(false);
    }, 350);
  }, []);

  const handleToggleFullscreen = useCallback(() => {
    setIsFullscreen((prev) => !prev);
  }, []);

  const tableProps = useMemo(() => {
    return {
      tableContainerProps: {
        sx: {
          height: isFullscreen ? "100%" : 400,
        },
      },
      sx: {
        height: isFullscreen ? "calc(100% - 40px)" : "unset",
      },
      boxProps: {
        sx: {
          flex: 1,
          minHeight: 0,
        },
      },
    };
  }, [isFullscreen]);

  const fullscreenSx = useMemo(
    () =>
      isFullscreen
        ? {
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            zIndex: 500,
          }
        : {},
    [isFullscreen]
  );

  return (
    <>
      <AppFormLayoutPanel
        sx={fullscreenSx}
        alignItems="flex-start"
        flex={1}
        mt={isFullscreen ? 0 : 2}
        title="Thông tin thiết bị"
        childrenProps={{
          p: 0,
          flex: 1,
          minHeight: 0,
        }}
        titleProps={{ justifyContent: "space-between", pr: 1.5 }}
        actions={
          <Tooltip title={isFullscreen ? "Thu nhỏ" : "Phóng to"}>
            <IconButton
              sx={{ width: 24, height: 24 }}
              onClick={handleToggleFullscreen}
            >
              {isFullscreen ? <FullScreenIconExit /> : <FullScreenIcon />}
            </IconButton>
          </Tooltip>
        }
      >
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          py={0.75}
        >
          <Tabs value={tabValue} onChange={handleChangeTab} slotProps={{}}>
            <Tab label="Danh sách thiết bị" value={TAB.info} />
            <Tab label="Hội đồng thanh lý" value={TAB.teamlead} />
          </Tabs>

          <Stack direction="row" spacing={1} pr={2}>
            {tabValue === TAB.info && <ChooseDeviceModal />}
            {tabValue === TAB.teamlead && <ChooseTeamLeadModal />}
          </Stack>
        </Stack>

        <Stack flex={1} minHeight={0} position="relative">
          {isPending && <LoadingSkeleton isFullscreen={isFullscreen} />}
          {delayedTab === TAB.info && <InfoTableTab tableProps={tableProps} />}
          {delayedTab === TAB.teamlead && (
            <TeamleadTable tableProps={tableProps} />
          )}
        </Stack>
      </AppFormLayoutPanel>
    </>
  );
};

const enum TAB {
  info,
  teamlead,
}

export default memo(TabTable);

const LoadingSkeleton = memo(({ isFullscreen }: { isFullscreen: boolean }) => (
  <Stack
    spacing={2}
    position="absolute"
    top={0}
    left={0}
    right={0}
    bottom={0}
    zIndex={1000}
    bgcolor="white"
    flex={1}
    minHeight={0}
    overflow="hidden"
  >
    <Skeleton variant="rectangular" sx={{ minHeight: 40 }} />
    {Array.from({ length: isFullscreen ? 40 : 15 }).map((_, i) => (
      <Skeleton key={i} variant="text" />
    ))}
  </Stack>
));
