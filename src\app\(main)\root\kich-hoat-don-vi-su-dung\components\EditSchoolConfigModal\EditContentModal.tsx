import {
  AppF<PERSON><PERSON>oggle,
  App<PERSON>orm<PERSON><PERSON>t<PERSON><PERSON>,
  GridFormContainer,
} from "@/components/common";
import { Box, Grid, Tooltip } from "@mui/material";

const EditContentModal = ({ control }) => {
  return (
    <GridFormContainer>
      <Grid size={6}>
        <DisableTextField label="Tên đơn vị" name="name" control={control} />
      </Grid>
      <Grid size={6}>
        <DisableTextField
          label="Mã đơn vị"
          name="schoolCode"
          control={control}
        />
      </Grid>
      <Grid size={6}>
        <DisableTextField
          name="doetName"
          label="Sở giáo dục"
          control={control}
        />
      </Grid>
      <Grid size={6}>
        <DisableTextField
          name="divisionName"
          label="Phòng giáo dục"
          control={control}
        />
      </Grid>
      <Grid size={6}>
        <DisableTextField
          name="schoolLevelName"
          label="Cấp học"
          control={control}
        />
      </Grid>

      <Grid size={6}>
        <AppFormTextField name="domailUrl" label="Domain" control={control} />
      </Grid>

      <Grid size={6}>
        <AppFormToggle
          label="Kích hoạt sử dụng dịch vụ"
          name="isActived"
          control={control}
        />
      </Grid>

      <Grid size={6}>
        <AppFormToggle
          label="Khởi tạo dữ liệu hệ thống"
          name="isInitedData"
          control={control}
        />
      </Grid>
      <Grid size={6}>
        <AppFormToggle
          label="Chuyển đổi dữ liệu từ hệ thống cũ"
          name="isConvertData"
          control={control}
        />
      </Grid>
    </GridFormContainer>
  );
};

export default EditContentModal;

const DisableTextField = ({ label, name, control }) => {
  return (
    <Tooltip followCursor placement="top" title="Không được chỉnh sửa" arrow>
      <Box>
        <AppFormTextField
          label={label}
          name={name}
          control={control}
          textfieldProps={{
            autoComplete: "off",
            disabled: true,
          }}
        />
      </Box>
    </Tooltip>
  );
};
