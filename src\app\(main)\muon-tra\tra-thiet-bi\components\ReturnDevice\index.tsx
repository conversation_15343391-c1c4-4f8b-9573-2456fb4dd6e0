"use client";

import { Button } from "@mui/material";
import React, { memo, useState } from "react";
import ReturnDeviceModal from "./ReturnDeviceModal";
import { IReturnDeviceList } from "../../returnDevice.model";

const ReturnDevice = ({
  fetchCurrentData,
  selectedRows = [],
}: {
  fetchCurrentData?: () => void;
  selectedRows?: IReturnDeviceList[];
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <Button
        variant="contained"
        color="primary"
        onClick={() => setIsOpen(true)}
        disabled={selectedRows.length === 0}
      >
        Ghi trả ({selectedRows.length})
      </Button>
      <ReturnDeviceModal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        selectedRows={selectedRows}
        fetchCurrentData={fetchCurrentData}
      />
    </>
  );
};

export default memo(ReturnDevice);
