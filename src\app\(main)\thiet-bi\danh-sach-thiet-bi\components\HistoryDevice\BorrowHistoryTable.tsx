import { selectorHistoryDevice } from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.slice";
import { IBorrowHistory } from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/type";
import { AppTable } from "@/components/common";
import { BorrowStatusCell } from "@/components/sn-common/BorrowStatusCell";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import { useAppSelector } from "@/redux/hook";
import { formatDayjsWithType, formatNumber } from "@/utils/format.utils";
import { Chip, Stack, Typography } from "@mui/material";
import { ColumnDef } from "@tanstack/react-table";

const BorrowHistoryTable = () => {
  const historyDevice = useAppSelector(selectorHistoryDevice);

  return (
    <>
      <Stack direction="row" alignItems="center" mb={1} px={1} spacing={4}>
        <Stack direction="row" alignItems="center" spacing={1.5}>
          <Typography>Sẵn sàng cho mượn</Typography>
          <Chip
            label={formatNumber(historyDevice?.totalBorrowReady ?? 0)}
            color="primary"
          />
        </Stack>
        <Stack direction="row" alignItems="center" spacing={1.5}>
          <Typography>Đã đăng ký</Typography>
          <Chip
            label={formatNumber(historyDevice?.totalRegister ?? 0)}
            color="primary"
          />
        </Stack>
        <Stack direction="row" alignItems="center" spacing={1.5}>
          <Typography>Đang mượn</Typography>
          <Chip
            label={formatNumber(historyDevice?.totalBorrowing ?? 0)}
            color="primary"
          />
        </Stack>
      </Stack>
      <AppTable
        columns={columns}
        data={historyDevice?.borrowHistories ?? []}
        totalData={historyDevice?.borrowHistories?.length ?? 0}
        hasDefaultPagination
        {...TABLE_MODAL_FULL_HEIGHT}
      />
    </>
  );
};

export default BorrowHistoryTable;

const columns: ColumnDef<IBorrowHistory>[] = [
  {
    id: "index",
    header: "STT",
    size: 50,
    cell: ({ row }) => {
      return row.index + 1;
    },
    meta: {
      align: "center",
    },
  },
  {
    id: "teacherName",
    header: "Tên người mượn",
    accessorKey: "teacherName",
    size: 150,
  },
  {
    id: "quantity",
    header: "Số lượng",
    accessorFn: (row) => formatNumber(row.quantity),
    size: 120,
    meta: {
      align: "right",
    },
  },
  {
    id: "borrowToDate",
    header: "Thời gian mượn",
    accessorFn: (row) => formatDayjsWithType(row.borrowToDate),
    size: 120,
  },
  {
    id: "borrowStatus",
    header: "Trạng thái",
    cell: ({ row }) => <BorrowStatusCell status={row.original.borrowStatus} />,
    size: 120,
    meta: {
      align: "center",
    },
  },
];
