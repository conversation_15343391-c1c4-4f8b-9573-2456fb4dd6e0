import useFeatureStore from "@/app/(main)/root/quan-ly-tinh-nang/hooks/useFeature";
import { featureSelectors } from "@/app/(main)/root/quan-ly-tinh-nang/store/feature.slice";
import { useModalAction } from "@/components/common/TablePageLayout/modal-store/useModalAction";
import TreeDeleteButton from "@/components/common/tree/TreeDeleteButton";
import TreeEditButton from "@/components/common/tree/TreeEditButton";
import TreeItem from "@/components/common/tree/TreeItem";
import TreeView from "@/components/common/tree/TreeView";
import { ITreeData } from "@/models/types";
import { useAppSelector } from "@/redux/hook";
import { convertDataTreeItem } from "@/utils/tree.utils";
import { Box, Stack } from "@mui/material";
import React, { memo, useEffect } from "react";

const TreeContent = () => {
  const features = useAppSelector(featureSelectors.features);
  const { openEditModal, openDeleteModal } = useModalAction();

  const renderIcon = (data: ITreeData) => {
    return (
      <Stack direction="row" spacing={1}>
        <TreeEditButton
          onClickButton={() => {
            openEditModal(data);
          }}
        />
        <TreeDeleteButton
          onClickButton={() => {
            openDeleteModal(data);
          }}
        />
      </Stack>
    );
  };

  return (
    <Box>
      <TreeView>
        <TreeItem
          data={convertDataTreeItem(features)}
          hasMoreICon={false}
          labelItemProps={{ justifyContent: "space-between" }}
          onCustomIcon={renderIcon}
        />
      </TreeView>
    </Box>
  );
};

export default TreeContent;
