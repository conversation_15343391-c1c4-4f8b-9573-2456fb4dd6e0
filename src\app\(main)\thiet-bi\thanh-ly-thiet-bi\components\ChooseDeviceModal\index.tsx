import { AppModal } from "@/components/common";
import { PlusIcon } from "@/components/icons";
import { useAppDispatch } from "@/redux/hook";
import { Button } from "@mui/material";
import { useCallback, useState } from "react";
import { deviceLiquidationActions } from "../../deviceLiquidation.slice";
import ChooseDeviceTable from "./ChooseDeviceTable";

const ChooseDeviceModal = () => {
  const dispatch = useAppDispatch();
  const [isOpen, setIsOpen] = useState(false);

  const handleClose = useCallback(() => {
    dispatch(deviceLiquidationActions.resetModalSelected());
    setIsOpen(false);
  }, []);

  const handleChooseDevice = useCallback(() => {
    dispatch(deviceLiquidationActions.addDevices());
    setIsOpen(false);
  }, []);

  return (
    <>
      <Button
        onClick={() => setIsOpen(true)}
        startIcon={<PlusIcon />}
        variant="contained"
        size="small"
      >
        Thêm thiết bị
      </Button>
      <AppModal
        fullWidth
        maxWidth="lg"
        slotProps={{
          paper: {
            sx: {
              height: "100%",
            },
          },
        }}
        onClose={handleClose}
        isOpen={isOpen}
        modalTitleProps={{ title: "Chọn thiết bị" }}
        modalContentProps={{
          sx: { py: 1 },
          content: <ChooseDeviceTable />,
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleClose}
              >
                Đóng
              </Button>
              <Button variant="contained" onClick={handleChooseDevice}>
                Chọn
              </Button>
            </>
          ),
        }}
      />
    </>
  );
};

export default ChooseDeviceModal;
