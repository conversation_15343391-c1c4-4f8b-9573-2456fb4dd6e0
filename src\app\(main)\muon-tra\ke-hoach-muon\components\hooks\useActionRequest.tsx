import {
  posRequestBorrow,
  updateRequestBorrow,
} from "../../requestBorrow.service";
import { DataResponseModel } from "@/models/response.model";
import { ApiConstant, AppConstant, EnvConstant } from "@/constant";
import { toast } from "sonner";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import { IBorrowRequestAction } from "../../borrowRequestModel";
import { getOptionId } from "@/components/common/AppAutoComplete";
import dayjs from "dayjs";
import { BORROW_TYPE, IBorrowRequest } from "@/models/eduDevice.model";
import { formatDayjsWithType } from "@/utils/format.utils";

const formatDate = (date?: string | Date | null) =>
  date ? dayjs(date).format(AppConstant.DATE_TIME_YYYYescape) : null;

const useActionRequest = () => {
  const handleAction = async (
    mode: "post" | "update",
    input:
      | {
          data: IBorrowRequestAction;
          dataOrigin?: IBorrowRequest;
        }
      | {
          data: IBorrowRequestAction;
        },
    onSuccess: () => void
  ) => {
    try {
      toggleAppProgress(true);
      const { data, dataOrigin } = input as any;

      if (
        data.borrowRequestDevices.length === 0 &&
        data.borrowRequestRooms.length === 0
      ) {
        toast.warning("Thiếu thông tin mượn", {
          description:
            "Bạn cần thêm ít nhất một thiết bị hoặc phòng để đăng ký mượn.",
        });
        return;
      }

      for (const element of data.borrowRequestDevices ?? []) {
        if (!element.deviceName) {
          toast.warning("Thiếu thông tin mượn", {
            description: `Bạn vui lòng nhập đủ thông tin của thiết bị trong ngày ${formatDayjsWithType(
              element.fromDate
            )} để đăng ký mượn.`,
          });
          return;
        }
      }

      if (
        Array.isArray(data.borrowRequestRooms) &&
        data.borrowRequestRooms.length > 0 &&
        !validateRooms(data.borrowRequestRooms)
      ) {
        return;
      }

      const borrowType = Number(data.borrowType);
      const payload = buildPayload(data, borrowType, mode, dataOrigin);

      const res: DataResponseModel<unknown> = await (mode === "post"
        ? posRequestBorrow(payload)
        : updateRequestBorrow(dataOrigin.id, payload));

      if (res.code === ApiConstant.ERROR_CODE_OK) {
        toast.success("Thành công", {
          description: `Bạn đã ${
            mode === "update" ? "cập nhật" : ""
          } đăng ký mượn thành công`,
        });
        onSuccess();
      } else {
        throw res;
      }
    } catch (error) {
      EnvConstant.IS_DEV && console.error(error);
      toast.error("Thất bại", {
        description: extractErrorMessage(error, "Đăng ký mượn thất bại"),
      });
    } finally {
      toggleAppProgress(false);
    }
  };

  return {
    handleRequestPost: (data: IBorrowRequestAction, onSuccess: () => void) =>
      handleAction("post", { data }, onSuccess),
    handleRequestUpdate: (
      dataObj: {
        data: IBorrowRequestAction;
        dataOrigin: IBorrowRequest;
      },
      onSuccess: () => void
    ) => handleAction("update", dataObj, onSuccess),
  };
};

export default useActionRequest;

const validateRooms = (rooms: any[]) => {
  const missing = rooms.find(
    (r) => !r.roomId || getOptionId(r.roomId) === null
  );
  if (missing) {
    toast.warning("Thiếu thông tin phòng học", {
      description: `Bạn cần chọn phòng học cho tất cả yêu cầu mượn phòng (${formatDayjsWithType(
        missing.fromDate
      )})`,
    });
    return false;
  }
  return true;
};
const isDeviceChanged = (newItem: any, oldItem: any) => {
  const result =
    newItem.deviceId !== oldItem.deviceId ||
    newItem.subjectId !== oldItem.subjectId ||
    newItem.roomId !== oldItem.roomId ||
    formatDate(newItem.fromDate) !== formatDate(oldItem.fromDate) ||
    formatDate(newItem.toDate) !== formatDate(oldItem.toDate) ||
    newItem.quantity !== oldItem.quantity ||
    newItem.roomDeviceGuid !== oldItem.roomDeviceGuid;

  if (result) {
    console.log("❗ Device changed:", {
      newItem,
      oldItem,
      compare: {
        deviceId: newItem.deviceId !== oldItem.deviceId,
        subjectId: newItem.subjectId !== oldItem.subjectId,
        roomId: newItem.roomId !== oldItem.roomId,
        fromDate: formatDate(newItem.fromDate) !== formatDate(oldItem.fromDate),
        toDate: formatDate(newItem.toDate) !== formatDate(oldItem.toDate),
        quantity: newItem.quantity !== oldItem.quantity,
      },
    });
  }

  return result;
};

const isRoomChanged = (newItem: any, oldItem: any) => {
  return (
    newItem.roomId !== oldItem.roomId ||
    newItem.subjectId !== oldItem.subjectId ||
    newItem.periodIds?.map((p: any) => p.id || p).join(",") !==
      (oldItem.periodIds?.map((p: any) => p.id || p).join(",") || "") ||
    newItem.schoolClassIds?.map((c: any) => c.id || c).join(",") !==
      (oldItem.schoolClassIds?.map((c: any) => c.id || c).join(",") || "") ||
    formatDate(newItem.fromDate) !== formatDate(oldItem.fromDate) ||
    formatDate(newItem.toDate) !== formatDate(oldItem.toDate) ||
    newItem.purpose !== oldItem.purpose ||
    newItem.roomDeviceGuid !== oldItem.roomDeviceGuid
  );
};

const buildPayload = (
  data: IBorrowRequestAction,
  borrowType: number,
  mode: "post" | "update",
  dataOrigin?: IBorrowRequestAction
) => {
  const isLongTerm = borrowType === BORROW_TYPE.longTerm;

  const startDate = isLongTerm
    ? formatDate(data.borrowFromDate)
    : data.schoolWeekConfigId?.fromDate ?? null;
  const endDate = isLongTerm
    ? formatDate(data.borrowToDate)
    : data.schoolWeekConfigId?.toDate ?? null;

  const rangeStart = dayjs(startDate);
  const rangeEnd = dayjs(endDate);

  const originDevicesMap = new Map(
    (dataOrigin?.borrowRequestDevices ?? []).map((d) => [d.id, d])
  );

  const originRoomsMap = new Map(
    (dataOrigin?.borrowRequestRooms ?? []).map((r) => [r.id, r])
  );

  const allDevices = (data.borrowRequestDevices ?? [])
    .filter((d) => d.borrowType === borrowType)
    .map((d) => {
      // const from = dayjs(isLongTerm ? startDate : d.fromDate);
      // const to = dayjs(isLongTerm ? endDate : d.toDate);
      // if (from.isBefore(rangeStart) || to.isAfter(rangeEnd)) return null;

      return {
        id: typeof d.id === "number" ? d.id : 0,
        deviceId: d.deviceId,
        subjectId: getOptionId(d.subjectId),
        roomId: getOptionId(d.roomId),
        fromDate: isLongTerm ? startDate : formatDate(d.fromDate),
        toDate: isLongTerm ? endDate : formatDate(d.toDate),
        quantity: d.quantity,
        roomDeviceGuid: d.roomDeviceGuid,
      };
    })
    .filter(Boolean);

  const allRooms = (data.borrowRequestRooms ?? [])
    .filter((r) => r.borrowType === borrowType)
    .map((r) => {
      // const from = dayjs(r.fromDate);
      // const to = dayjs(r.toDate);
      // if (from.isBefore(rangeStart) || to.isAfter(rangeEnd)) return null;

      return {
        id: typeof r.id === "number" ? r.id : 0,
        roomId: getOptionId(r.roomId),
        periodIds: r.periodIds?.map((p: any) => p.id) || [],
        subjectId: getOptionId(r.subjectId),
        schoolClassIds: r.schoolClassIds?.map((c: any) => c.id) || [],
        fromDate: formatDate(r.fromDate),
        toDate: formatDate(r.toDate),
        roomDeviceGuid: r.roomDeviceGuid,
        purpose: getOptionId(r.purpose),
      };
    })
    .filter(Boolean);

  const changedDevices = allDevices.filter((d) => {
    if (!d) return false;
    const origin = originDevicesMap.get(d.id);
    return d.id === 0 || (origin && isDeviceChanged(d, origin));
  });

  const changedRooms = allRooms.filter((r) => {
    if (!r) return false;
    const origin = originRoomsMap.get(r.id);
    return r.id === 0 || (origin && isRoomChanged(r, origin));
  });

  const roomDeviceGuidList = changedDevices
    .filter((item) => item?.roomDeviceGuid)
    .map((item) => item?.roomDeviceGuid);
  const attachRoom = allRooms.filter((item) =>
    roomDeviceGuidList.includes(item?.roomDeviceGuid)
  );

  const uniqueRooms = new Map<string, any>();
  [...attachRoom, ...changedRooms].forEach((r) => {
    if (r?.roomDeviceGuid) uniqueRooms.set(r.roomDeviceGuid, r);
  });

  const payload: any = {
    borrowType,
    borrowFromDate: startDate,
    borrowToDate: endDate,
    schoolWeekConfigId: getOptionId(data.schoolWeekConfigId),
    teacherId: getOptionId(data.teacherId),
    borrowRequestDevices: changedDevices,
    borrowRequestRooms: Array.from(uniqueRooms.values()),
    deleteBorrowRequestDeviceIds: [],
    deleteBorrowRequestRoomIds: [],
  };

  if (mode === "update" && dataOrigin) {
    const originDeviceIds = new Set(
      dataOrigin.borrowRequestDevices?.map((d) => d.id) ?? []
    );
    const currentDeviceIds = new Set(
      allDevices.map((d: any) => d.id).filter((id: number) => id !== 0)
    );

    const originRoomIds = new Set(
      dataOrigin.borrowRequestRooms?.map((r) => r.id) ?? []
    );
    const currentRoomIds = new Set(
      allRooms.map((r: any) => r.id).filter((id: number) => id !== 0)
    );

    payload.deleteBorrowRequestDeviceIds = [...originDeviceIds].filter(
      (id) => !currentDeviceIds.has(id as number)
    );
    payload.deleteBorrowRequestRoomIds = [...originRoomIds].filter(
      (id) => !currentRoomIds.has(id as number)
    );
  }

  return payload;
};
