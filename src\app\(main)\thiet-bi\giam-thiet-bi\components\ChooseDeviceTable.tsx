import { useAppDispatch, useAppSelector } from "@/redux/hook";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import { Box, Grid, Stack, Typography } from "@mui/material";
import {
  AppAutoComplete,
  AppCheckbox,
  AppSearchDebounceTextFiled,
  AppTable,
  IOption,
} from "@/components/common";
import { ColumnDef } from "@tanstack/react-table";
import { AppConstant } from "@/constant";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import {
  reduceDeviceActions,
  reduceDeviceSelectors,
} from "@/app/(main)/thiet-bi/giam-thiet-bi/reduceDevice.slice";
import { FormatUtils } from "@/utils";
import {
  selectGradeList,
  selectRoomList,
  selectSubjectList,
  systemActions,
} from "@/redux/system.slice";
import { IDevice, IDeviceParams } from "@/models/eduDevice.model";

const ChooseDeviceTable = () => {
  const dispatch = useAppDispatch();
  const devices = useAppSelector(reduceDeviceSelectors.deviceChooseList);
  const totalData = useAppSelector(reduceDeviceSelectors.totalDeviceChoose);
  const deviceSelected = useAppSelector(reduceDeviceSelectors.deviceSelected);
  const roomList = useAppSelector(selectRoomList);
  const gradeList = useAppSelector(selectGradeList);
  const subjectList = useAppSelector(selectSubjectList);

  const [filter, setFilter] = useState<IDeviceParams>({});
  const [pagination, setPagination] = useState(
    AppConstant.DEFAULT_PAGINATION_SKIP_TAKE
  );

  const columns = useMemo(
    () => getColumns({ skip: pagination.skip }),
    [pagination.skip]
  );

  const handleChangeFilterWithKey = useCallback(
    (key: keyof IDeviceParams) => (value) => {
      setFilter((pre) => ({
        ...pre,
        [key]: value,
      }));
    },
    []
  );

  useEffect(() => {
    dispatch(
      reduceDeviceActions.getDeviceChoose({
        ...convertFilter(filter),
        ...pagination,
      })
    );
  }, [filter, pagination]);

  useEffect(() => {
    dispatch(systemActions.getRoomList());
    dispatch(systemActions.getGradeList());
    dispatch(systemActions.getSubjectList());
  }, []);

  return (
    <Stack spacing={1} height="100%">
      <Grid container spacing={2}>
        <Grid size={6}>
          <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
            <Typography>Hiển thị thiết bị</Typography>
            <Stack direction="row" alignItems="center">
              <AppCheckbox
                onChange={(_, value) =>
                  handleChangeFilterWithKey("isBroken")(value)
                }
              />
              <Typography>Hỏng</Typography>
            </Stack>
            <Stack direction="row" alignItems="center">
              <AppCheckbox
                onChange={(_, value) =>
                  handleChangeFilterWithKey("isLost")(value)
                }
              />
              <Typography>Mất</Typography>
            </Stack>
            <Stack direction="row" alignItems="center">
              <AppCheckbox
                onChange={(_, value) =>
                  handleChangeFilterWithKey("isAvailable")(value)
                }
              />
              <Typography>Còn sử dụng</Typography>
            </Stack>
          </Box>
        </Grid>
        <Grid size={6}>
          <Stack
            height={"100%"}
            direction="row"
            alignItems="flex-end"
            justifyContent="flex-end"
          >
            <Typography color="primary">
              {deviceSelected.length}/{totalData} thiết bị đã được chọn
            </Typography>
          </Stack>
        </Grid>
        <Grid size={3}>
          <AppAutoComplete
            label="Kho/Phòng"
            options={roomList}
            onChange={(_, value) => handleChangeFilterWithKey("roomId")(value)}
          />
        </Grid>
        <Grid size={3}>
          <AppAutoComplete
            label="Khối/Lớp"
            options={gradeList}
            onChange={(_, value) =>
              handleChangeFilterWithKey("gradeCode")(value)
            }
          />
        </Grid>
        <Grid size={3}>
          <AppAutoComplete
            label="Môn học"
            options={subjectList}
            onChange={(_, value) =>
              handleChangeFilterWithKey("schoolSubjectId")(value)
            }
          />
        </Grid>
        <Grid size={3}>
          <AppSearchDebounceTextFiled
            label="Tìm kiếm"
            valueInput={filter.searchKey ?? ""}
            onChangeValue={handleChangeFilterWithKey("searchKey")}
          />
        </Grid>
      </Grid>
      <AppTable
        columns={columns}
        totalData={totalData}
        data={devices}
        paginationData={pagination}
        onPageChange={setPagination}
        {...TABLE_MODAL_FULL_HEIGHT}
      />
    </Stack>
  );
};

export default memo(ChooseDeviceTable);

const convertFilter = (filter) => {
  const { roomId, gradeCode, schoolSubjectId } = filter;
  return {
    ...filter,
    roomId: roomId ? (roomId as IOption).id : undefined,
    gradeCode: gradeCode ? (gradeCode as IOption).code : undefined,
    schoolSubjectId: schoolSubjectId
      ? (schoolSubjectId as IOption).id
      : undefined,
  };
};
const getColumns = ({ skip }: { skip: number }): ColumnDef<IDevice>[] => [
  {
    id: "index",
    header: "STT",
    cell: ({ row }) => row.index + skip + 1,
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "select",
    accessorKey: "select",
    meta: {
      align: "center",
    },
    size: 50,
    header: ({ table }) => <HeaderCheckbox table={table} />,
    cell: ({ row }) => <RowCheckbox row={row} />,
  },
  {
    id: "deviceCode",
    header: "Mã",
    accessorKey: "deviceCode",
    size: 60,
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
  },
  {
    id: "schoolDeviceTypeName",
    header: "Loại thiết bị",
    accessorKey: "schoolDeviceTypeName",
    size: 100,
  },
  {
    id: "roomName",
    header: "Kho/Phòng",
    accessorKey: "roomName",
    size: 100,
  },
  {
    id: "gradeCode",
    header: "Khối/Lớp",
    accessorKey: "gradeCode",
    size: 100,
  },
  {
    id: "schoolSubjectName",
    header: "Môn học",
    accessorKey: "schoolSubjectName",
    size: 100,
  },
  {
    id: "totalBroken",
    header: "Hỏng",
    accessorKey: "totalBroken",
    accessorFn: (row) => FormatUtils.formatNumber(row.totalBroken),
    meta: {
      align: "right",
    },
    size: 100,
  },
  {
    id: "totalLost",
    header: "Mất",
    accessorKey: "totalLost",
    accessorFn: (row) => FormatUtils.formatNumber(row.totalLost),
    meta: {
      align: "right",
    },
    size: 100,
  },
  {
    id: "totalAvailable",
    header: "Còn SD",
    accessorFn: (row) => FormatUtils.formatNumber(row.totalAvailable),
    meta: {
      align: "right",
    },
    accessorKey: "totalAvailable",
    size: 100,
  },
];

const HeaderCheckbox = ({ table }) => {
  const dispatch = useAppDispatch();
  const deviceSelected = useAppSelector(reduceDeviceSelectors.deviceSelected);
  const deviceChooseList = useAppSelector(
    reduceDeviceSelectors.deviceChooseList
  );

  const isCheckedAll = useMemo(() => {
    return (
      deviceChooseList.every((item) => deviceSelected.includes(item.id)) &&
      deviceChooseList.length !== 0
    );
  }, [deviceChooseList, deviceSelected]);

  const handleCheckedAll = (_, checked: boolean) => {
    table.toggleAllPageRowsSelected(!!checked);
    dispatch(
      reduceDeviceActions.setDeviceSelectedAll({
        checked,
        deviceChooseList,
      })
    );
  };

  return <AppCheckbox checked={isCheckedAll} onChange={handleCheckedAll} />;
};

const RowCheckbox = ({ row }) => {
  const dispatch = useAppDispatch();
  const deviceSelected = useAppSelector(reduceDeviceSelectors.deviceSelected);
  const devices = useAppSelector(reduceDeviceSelectors.devices);

  const idsExists = useMemo(() => {
    return devices.map((item) => item.id);
  }, [devices]);

  const handleCheckedRow = (id) => {
    dispatch(reduceDeviceActions.setDeviceSelected(id));
  };

  const isChecked = useMemo(() => {
    return deviceSelected.includes(row.original.id);
  }, [deviceSelected, row.original.id]);

  return (
    <AppCheckbox
      onChange={(_, value) => handleCheckedRow(row.original.id)}
      checked={isChecked || idsExists.includes(row.original.id)}
      disabled={idsExists.includes(row.original.id)}
    />
  );
};
