import { IOption } from "@/components/common";
import { IAddMenuConfig } from "@/models/menu.model";

export const convertDataToAddMenuConfig = (data: any) => {
  const result: IAddMenuConfig = {
    name: data.name,
    icon: data.icon,
    status: Number(data.status),
    isSystem: Number(data.isSystem),
    isRoot: Number(data.isRoot),
    isShowSubMenu: Number(data.isShowSubMenu),
    isBetaMenu: Number(data.isBetaMenu),
    menuTypeId: data.menuTypeId,
    groupUnitCodes: data.groupUnitCodes.map((item: IOption) => item.schoolCode),
    href: data.href,
    order: data.order,
    parentId: data.parentId,
  };

  return result;
};
