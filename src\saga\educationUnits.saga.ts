import { ApiConstant, EnvConstant } from "@/constant";
import { IDonVi, ISchoolLevel } from "@/models/app.model";
import { IDivision, IDoet, IUnitParams } from "@/models/educationUnits.model";
import { DataListResponseModel } from "@/models/response.model";
import { educationUnitsActions } from "@/redux/educationUnits.slice";
import { AppServices, EducationUnitsServices } from "@/services";
import { PayloadAction } from "@reduxjs/toolkit";
import { call, put, takeLatest } from "redux-saga/effects";

function* getTruongListSaga(
  action: PayloadAction<
    IUnitParams & {
      isScroll?: boolean;
    }
  >
) {
  try {
    const response: DataListResponseModel<IDonVi> = yield call(
      AppServices.getDonViListService,
      {
        ...action.payload,
      }
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        educationUnitsActions.getTruongListSuccess({
          data: response.data.data || [],
          totalCount: response.data.totalCount,
          isScroll: action.payload.isScroll,
        })
      );
    } else {
      yield put(
        educationUnitsActions.getTruongListSuccess({
          data: [],
          totalCount: 0,
        })
      );
    }
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    yield put(
      educationUnitsActions.getTruongListSuccess({
        data: [],
        totalCount: 0,
      })
    );
  }
}

function* getPhongListSaga(action: PayloadAction<IUnitParams>) {
  try {
    const response: DataListResponseModel<IDivision> = yield call(
      EducationUnitsServices.getDivisionListService,
      { ...action.payload }
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        educationUnitsActions.getPhongListSuccess({
          data: response.data.data || [],
          totalCount: response.data.totalCount,
        })
      );
    } else {
      yield put(
        educationUnitsActions.getPhongListSuccess({
          data: [],
          totalCount: 0,
        })
      );
    }
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    yield put(
      educationUnitsActions.getPhongListSuccess({
        data: [],
        totalCount: 0,
      })
    );
  }
}

function* getSoListSaga() {
  try {
    const response: DataListResponseModel<IDoet> = yield call(
      EducationUnitsServices.getDoetListService
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        educationUnitsActions.getSoListSuccess({
          data: response.data.data || [],
          totalCount: response.data.totalCount,
        })
      );
    }
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
  }
}

function* getSchoolLevelListSaga() {
  try {
    const response: DataListResponseModel<ISchoolLevel> = yield call(
      AppServices.getSchoolLevelListService
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        educationUnitsActions.getSchoolLevelListSuccess({
          data: response.data.data || [],
        })
      );
    } else {
      yield put(
        educationUnitsActions.getSchoolLevelListSuccess({
          data: [],
        })
      );
    }
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    yield put(
      educationUnitsActions.getSchoolLevelListSuccess({
        data: [],
      })
    );
  }
}

export function* educationUnitsSaga() {
  yield takeLatest(educationUnitsActions.getSoList, getSoListSaga);
  yield takeLatest(educationUnitsActions.getTruongList, getTruongListSaga);
  yield takeLatest(educationUnitsActions.getPhongList, getPhongListSaga);
  yield takeLatest(
    educationUnitsActions.getSchoolLevelList,
    getSchoolLevelListSaga
  );
}
