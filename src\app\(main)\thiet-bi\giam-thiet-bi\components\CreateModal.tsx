import DeviceTable from "@/app/(main)/thiet-bi/giam-thiet-bi/components/DeviceTable";
import InformationForm from "@/app/(main)/thiet-bi/giam-thiet-bi/components/InformationForm";
import useActionReduce from "@/app/(main)/thiet-bi/giam-thiet-bi/hooks/useActionReduce";
import { reduceDeviceActions } from "@/app/(main)/thiet-bi/giam-thiet-bi/reduceDevice.slice";
import { AppModal } from "@/components/common";
import { AppModalProps } from "@/components/common/modal/AppModal";
import { useAppDispatch } from "@/redux/hook";
import { Box, Button } from "@mui/material";
import { useCallback } from "react";
import { FormProvider, useForm } from "react-hook-form";
import dayjs, { Dayjs } from "dayjs";

const CreateModal = ({
  fetchCurrentData,
  onClose,
  ...otherProps
}: CreateModalProps) => {
  const dispatch = useAppDispatch();
  const methods = useForm({
    defaultValues: INIT_VALUE,
  });
  const { control, handleSubmit, reset } = methods;
  const { handleCreateReduce } = useActionReduce();

  const handleClose = useCallback(() => {
    dispatch(reduceDeviceActions.resetModalDetails());
    reset(INIT_VALUE);
    onClose();
  }, [onClose, reset]);

  const onSubmit = useCallback(
    (data: any) => {
      handleCreateReduce(data, () => {
        handleClose();
        fetchCurrentData?.();
      });
    },
    [handleCreateReduce, fetchCurrentData]
  );

  return (
    <FormProvider {...methods}>
      <AppModal
        onClose={handleClose}
        modalTitleProps={{
          title: "Thêm phiếu ghi giảm thiết bị",
        }}
        fullScreen
        component={"form"}
        onSubmit={handleSubmit(onSubmit)}
        modalContentProps={{
          content: (
            <Box sx={{ display: "flex", flexDirection: "column", gap: 1.5 }}>
              <InformationForm control={control} />
              <DeviceTable />
            </Box>
          ),
          sx: {
            bgcolor: "background.grey",
            padding: "12px !important",
          },
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                onClick={handleClose}
                variant="outlined"
                color="secondary"
              >
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
        {...otherProps}
      />
    </FormProvider>
  );
};

export default CreateModal;

type CreateModalProps = AppModalProps & {
  fetchCurrentData?: () => void;
};

type InitValue = {
  documentNumber: string;
  documentDate: Dayjs | null | string;
  notes: string;
};

const INIT_VALUE: InitValue = {
  documentNumber: "",
  documentDate: dayjs(),
  notes: "",
};
