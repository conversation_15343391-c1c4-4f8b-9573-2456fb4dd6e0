"use client";

import { ComponentType, JSX, memo, ReactNode } from "react";
import { ActionType, FormConfig, FormFieldConfig } from "../../type";
import dynamic from "next/dynamic";
import { AppModalProps } from "@/components/common/modal/AppModal";
import DeleteModal from "./DeleteModal";
import { useTableStore } from "../../table-store/TableContext";
import { useModalAction } from "../../modal-store/useModalAction";
import { useModalData, useModalType } from "../../modal-store/useModalSelector";

const CreateModal = dynamic(() => import("../modal-action/CreateModal"), {
  ssr: false,
});
const EditModal = dynamic(() => import("../modal-action/EditModal"), {
  ssr: false,
});

const ModalContainer = <T,>({
  actions,
  formConfig,
  CreateModalComponent,
  EditModalComponent,
  createFormContent,
}: ModalContainerProps<T>) => {
  const modalType = useModalType();
  const modalData = useModalData();
  const { closeModal } = useModalAction();

  const isOpenCreate = modalType === "create";
  const isOpenEdit = modalType === "edit";
  const isOpenDelete = modalType === "delete";

  const store = useTableStore<T>();

  const fetchCurrentData = store((state: any) => state.fetchCurrentData);

  return (
    <>
      {actions?.includes("create") &&
        isOpenCreate &&
        (CreateModalComponent ? (
          <CreateModalComponent
            isOpen={isOpenCreate}
            onClose={closeModal}
            fetchCurrentData={fetchCurrentData}
          />
        ) : (
          <CreateModal
            isOpen={isOpenCreate}
            onClose={closeModal}
            formConfig={formConfig as any}
            createFormContent={
              createFormContent as
                | ((formField?: FormFieldConfig<unknown>[]) => ReactNode)
                | undefined
            }
          />
        ))}
      {actions?.includes("update") &&
        isOpenEdit &&
        (EditModalComponent ? (
          <EditModalComponent
            isOpen={isOpenEdit}
            onClose={closeModal}
            fetchCurrentData={fetchCurrentData}
            modalData={modalData}
          />
        ) : (
          <EditModal
            isOpen={isOpenEdit}
            formConfig={formConfig as any}
            onClose={closeModal}
            createFormContent={
              createFormContent as
                | ((formField?: FormFieldConfig<unknown>[]) => ReactNode)
                | undefined
            }
          />
        ))}
      {actions?.includes("delete") && isOpenDelete && (
        <DeleteModal
          url={formConfig?.deleteUrl}
          onSuccess={formConfig?.onSuccess}
        />
      )}
    </>
  );
};

export type ModalContainerProps<T> = {
  actions?: ActionType[];
  formConfig?: FormConfig<T>;
  createFormContent?: (formField?: FormFieldConfig<T>[]) => ReactNode;

  CreateModalComponent?: ComponentType<
    AppModalProps & {
      /** Hàm get lại dữ liệu bảng */
      fetchCurrentData?: () => void;
    }
  >;
  EditModalComponent?: ComponentType<
    AppModalProps & {
      modalData: any;
      fetchCurrentData?: () => void;
    }
  >;
};
export default memo(ModalContainer) as <T>(
  props: ModalContainerProps<T>
) => JSX.Element;
