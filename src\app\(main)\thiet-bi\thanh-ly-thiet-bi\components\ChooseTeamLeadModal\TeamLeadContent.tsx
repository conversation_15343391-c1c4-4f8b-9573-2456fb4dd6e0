import {
  AppCheckbox,
  AppSearchDebounceTextFiled,
  AppTable,
} from "@/components/common";
import { AppConstant } from "@/constant";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import { ISearchParams, ITeacher } from "@/models/system.model";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { Grid, Stack } from "@mui/material";
import { ColumnDef } from "@tanstack/react-table";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import {
  deviceLiquidationActions,
  selectTeacherChooseList,
  selectTeacherChooseTotal,
  selectTeacherSelected,
  selectTeamlead,
} from "../../deviceLiquidation.slice";

const TeamLeadContent = () => {
  const dispatch = useAppDispatch();
  const data = useAppSelector(selectTeacherChooseList);
  const totalData = useAppSelector(selectTeacherChooseTotal);

  const [filter, setFilter] = useState<ISearchParams>({});
  const [pagination, setPagination] = useState(
    AppConstant.DEFAULT_PAGINATION_SKIP_TAKE
  );

  const columns = useMemo(
    () => getColumns({ skip: pagination.skip }),
    [pagination.skip]
  );

  const handleChangeFilterWithKey = useCallback(
    (key: keyof ISearchParams) => (value) => {
      setFilter((pre) => ({
        ...pre,
        [key]: value,
      }));
    },
    []
  );

  useEffect(() => {
    dispatch(
      deviceLiquidationActions.getTeacherChooseList({
        ...filter,
        ...pagination,
      })
    );
  }, [filter, pagination]);

  return (
    <Stack spacing={1} height="100%">
      <Grid container spacing={2}>
        <Grid size={3}>
          <AppSearchDebounceTextFiled
            label="Tìm kiếm"
            valueInput={filter.searchKey ?? ""}
            onChangeValue={handleChangeFilterWithKey("searchKey")}
          />
        </Grid>
      </Grid>
      <AppTable
        columns={columns}
        totalData={totalData || 0}
        data={data}
        paginationData={pagination}
        onPageChange={setPagination}
        {...TABLE_MODAL_FULL_HEIGHT}
      />
    </Stack>
  );
};

export default memo(TeamLeadContent);

const getColumns = ({ skip }: { skip: number }): ColumnDef<ITeacher>[] => [
  {
    id: "index",
    header: "STT",
    cell: ({ row }) => row.index + skip + 1,
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "select",
    header: ({ table }) => <HeaderCheckbox table={table} />,
    cell: ({ row }) => <RowCheckbox row={row} />,
    size: 70,
    meta: {
      align: "center",
    },
  },
  {
    id: "fullName",
    header: "Tên thành viên",
    accessorKey: "fullName",
    size: 60,
  },
  {
    id: "code",
    header: "Mã giáo viên",
    accessorKey: "code",
    size: 60,
  },
  {
    id: "gender",
    accessorKey: "gender",
    header: "Giới tính",
    size: 50,
    meta: { align: "center" },
    cell: ({ row }) => (row.original.gender === 1 ? "Nam" : "Nữ"),
  },
  {
    id: "phone",
    header: "Số điện thoại",
    accessorKey: "phone",
    size: 60,
  },
  {
    id: "email",
    header: "Email",
    accessorKey: "email",
  },
  {
    id: "teacherGroupSubjectName",
    header: "Bộ môn",
    accessorKey: "teacherGroupSubjectName",
    size: 80,
  },
];

const HeaderCheckbox = ({ table }) => {
  const dispatch = useAppDispatch();
  const teacherSelected = useAppSelector(selectTeacherSelected);
  const teacherChooseList = useAppSelector(selectTeacherChooseList);

  const isCheckedAll = useMemo(() => {
    return (
      teacherChooseList.every((item) => teacherSelected?.includes(item.id)) &&
      teacherChooseList.length !== 0
    );
  }, [teacherChooseList, teacherSelected]);

  const handleCheckedAll = (_, checked: boolean) => {
    table.toggleAllPageRowsSelected(!!checked);
    dispatch(
      deviceLiquidationActions.setTeacherSelectedAll({
        checked,
        teacherChooseList,
      })
    );
  };

  return <AppCheckbox checked={isCheckedAll} onChange={handleCheckedAll} />;
};

const RowCheckbox = ({ row }) => {
  const dispatch = useAppDispatch();
  const teacherSelected = useAppSelector(selectTeacherSelected);
  const teamlead = useAppSelector(selectTeamlead);

  const idsExists = useMemo(() => {
    return teamlead?.map((item) => item.id);
  }, [teamlead]);

  const handleCheckedRow = (id) => {
    dispatch(deviceLiquidationActions.setTeacherSelected(id));
  };

  const isChecked = useMemo(() => {
    return teacherSelected?.includes(row.original.id);
  }, [teacherSelected, row.original.id]);

  return (
    <AppCheckbox
      onChange={(_, value) => handleCheckedRow(row.original.id)}
      checked={isChecked || idsExists?.includes(row.original.id)}
      disabled={idsExists?.includes(row.original.id)}
    />
  );
};
