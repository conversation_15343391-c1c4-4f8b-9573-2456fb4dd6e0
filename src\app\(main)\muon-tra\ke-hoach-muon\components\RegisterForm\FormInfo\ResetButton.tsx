import { ReloadIcon } from "@/components/icons";
import {
  borrowRequestActions,
  selectIsShowReset,
} from "@/redux/device/borrowRequest.slice";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { Grid, IconButton, Tooltip } from "@mui/material";
import React, { memo } from "react";
import { useFormContext } from "react-hook-form";
import { IBorrowRequestAction } from "../../../borrowRequestModel";
import { defaultBorrowRequestAction } from "../../CreateModal";

const ResetButton = () => {
  const dispatch = useAppDispatch();
  const isShowReset = useAppSelector(selectIsShowReset);

  const { reset } = useFormContext<IBorrowRequestAction>();
  const handleReset = () => {
    dispatch(borrowRequestActions.getBorrowRequestByTeacherWeekSuccess(null));
    reset(defaultBorrowRequestAction);
  };
  return (
    isShowReset && (
      <Grid flex={1} container justifyContent="flex-end">
        <Tooltip title="Đặt lại thông tin phiếu">
          <IconButton
            aria-label="Đặt lại thông tin phiếu"
            onClick={handleReset}
            sx={{
              border: "1px solid",
              borderRadius: "4px",
              borderColor: "border.main",
            }}
          >
            <ReloadIcon />
          </IconButton>
        </Tooltip>
      </Grid>
    )
  );
};

export default memo(ResetButton);
