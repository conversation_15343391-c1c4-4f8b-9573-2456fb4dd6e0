// app/root/quan-ly-menu/hooks/useMenuTypeOptions.ts
import { useEffect, useState } from "react";
import { IOption } from "@/components/common";
import http from "@/api";
import { MENU_TYPE } from "@/constant/api.const";
import { DataListResponseModel } from "@/models/response.model";
import { ApiConstant } from "@/constant";
import { IMenuType } from "@/models/menu.model";
import { toast } from "sonner";
import { extractErrorMessage } from "@/utils/common.utils";

export const useMenuTypeOptions = () => {
  const [options, setOptions] = useState<IOption[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMenuTypes = async () => {
      try {
        const res = await http.get<DataListResponseModel<IMenuType>>(MENU_TYPE);
        if (res.code === ApiConstant.ERROR_CODE_OK) {
          setOptions(
            res.data.data.map((item) => ({
              id: item.id,
              label: item.name,
            }))
          );
        } else {
          throw new Error(res.message || "Đã có lỗi xảy ra");
        }
      } catch (error: any) {
        const description = extractErrorMessage(error);
        toast.error("Thất bại!", {
          description,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchMenuTypes();
  }, []);

  return { options, loading };
};
