"use client";

import http from "@/api";
import {
  permissionActions,
  permissionSelectors,
} from "@/app/(main)/he-thong/phan-quyen/permission.slice";
import {
  IFunction,
  IFunctionChecked,
} from "@/app/(main)/he-thong/phan-quyen/type";
import { AppCheckbox } from "@/components/common";
import AppFormLayoutPanel from "@/components/common/AppFormLayoutPanel";
import TreeItem from "@/components/common/tree/TreeItem";
import TreeView from "@/components/common/tree/TreeView";
import { ApiConstant } from "@/constant";
import {
  APPLICATION_FEATURE,
  APPLICATION_FEATURE_FUNCTION,
  UPDATE_APPLICATION_FEATURE,
} from "@/constant/api.const";
import {
  DataListResponseModel,
  DataResponseModel,
} from "@/models/response.model";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { CommonUtils } from "@/utils";
import { extractErrorMessage } from "@/utils/common.utils";
import {
  checkIndeterminate,
  checkSiblingsId,
  convertData,
  convertDataExpand,
  convertDataTreeItem,
  findParentNodeById,
  getAllFathers,
  handelGetAllChild,
} from "@/utils/tree.utils";
import { Button, Stack } from "@mui/material";
import React, { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import stringFormat from "string-format";

const FunctionPanel = () => {
  const dispatch = useAppDispatch();
  const [applicationFeature, setApplicationFeature] = useState<
    IFunctionChecked[]
  >([]);
  const [expandedNodes, setExpandedNodes] = useState<string[]>([]);
  const selectedNodes = useAppSelector(permissionSelectors.selectedNodes);
  const functions = useAppSelector(permissionSelectors.functions);
  const applicationFeatureId = useAppSelector(
    permissionSelectors.applicationFeatureId
  );

  const handleNodeToggle = useCallback((_, nodeIds) => {
    setExpandedNodes(nodeIds);
  }, []);

  const handleNodeSelect = useCallback(
    (event, nodeId) => {
      event.stopPropagation();
      const fathersIds = getAllFathers(nodeId, functions);

      if (selectedNodes.includes(nodeId)) {
        if (expandedNodes.some((nodeId) => nodeId === nodeId.toString())) {
          setExpandedNodes((prevExpandedNodes) =>
            prevExpandedNodes.filter(
              (expandedNodeId) => expandedNodeId !== nodeId.toString()
            )
          );
        }

        const backTrackCheckedNode = [nodeId, ...fathersIds];
        while (backTrackCheckedNode.length > 0) {
          const checkNode = backTrackCheckedNode.shift();
          const allChildCheckNode = handelGetAllChild(checkNode, functions);

          dispatch(
            permissionActions.setSelectedNodes(
              selectedNodes.filter((id) => !allChildCheckNode.includes(id))
            )
          );

          if (backTrackCheckedNode.length === 0) break;

          const parentNode = findParentNodeById(
            functions,
            backTrackCheckedNode[0]
          );
          const siblingsId = parentNode.applicationFeatures
            .filter((item) => item.id !== checkNode)
            .map((item) => item.id);

          if (checkSiblingsId(siblingsId, selectedNodes)) break;
          setExpandedNodes((prevExpandedNodes) =>
            prevExpandedNodes.filter(
              (expandedNodeId) => expandedNodeId !== parentNode.id.toString()
            )
          );
        }
      } else {
        const toBeChecked = handelGetAllChild(nodeId, functions);
        fathersIds.forEach((item) => {
          if (!selectedNodes.some((nodeId) => nodeId === item)) {
            toBeChecked.push(item);
          }
        });

        dispatch(
          permissionActions.setSelectedNodes([...selectedNodes, ...toBeChecked])
        );

        const newExpandedNodes = convertDataExpand(toBeChecked, functions);
        setExpandedNodes((prev) => [
          ...new Set([...prev, ...newExpandedNodes]),
        ]);
      }
    },
    [selectedNodes, functions]
  );

  const handleIndeterminate = useCallback(
    (nodeId) => {
      return checkIndeterminate(nodeId, functions, selectedNodes);
    },
    [functions, selectedNodes]
  );

  const handleSave = async () => {
    const formData = {
      applicationFunctionId: applicationFeatureId,
      applicationFeatureIds: selectedNodes,
    };
    CommonUtils.toggleAppProgress(true);
    try {
      const response: DataResponseModel<any> = await http.post(
        UPDATE_APPLICATION_FEATURE,
        formData
      );
      if (response.code === ApiConstant.ERROR_CODE_OK) {
        toast.success("Cập nhật thành công!");
      } else {
        throw new Error(response.message || "Đã có lỗi xảy ra");
      }
    } catch (error) {
      const description = extractErrorMessage(error);
      toast.error("Thất bại!", {
        description,
      });
    } finally {
      CommonUtils.toggleAppProgress(false);
    }
  };

  useEffect(() => {
    if (!applicationFeatureId) return;
    const fetchFunctions = async () => {
      CommonUtils.toggleAppProgress(true);
      try {
        const response: DataResponseModel<IFunctionChecked[]> = await http.get(
          stringFormat(APPLICATION_FEATURE, {
            functionId: applicationFeatureId,
          })
        );
        if (response.code === ApiConstant.ERROR_CODE_OK) {
          setApplicationFeature(response.data);
        } else {
          throw new Error(response.message || "Đã có lỗi xảy ra");
        }
      } catch (error) {
        const description = extractErrorMessage(error);
        toast.error("Thất bại!", {
          description,
        });
      } finally {
        CommonUtils.toggleAppProgress(false);
      }
    };
    fetchFunctions();
  }, [applicationFeatureId]);

  useEffect(() => {
    if (!functions.length) return;
    const visited = convertData(applicationFeature, functions);
    setExpandedNodes(visited.expandCheckbox);
    dispatch(permissionActions.setSelectedNodes(visited.checkedCheckBox));
  }, [applicationFeature, functions]);

  useEffect(() => {
    const fetchFunctions = async () => {
      CommonUtils.toggleAppProgress(true);
      try {
        const response: DataListResponseModel<IFunction> = await http.get(
          APPLICATION_FEATURE_FUNCTION
        );
        if (response.code === ApiConstant.ERROR_CODE_OK) {
          dispatch(permissionActions.setFunctions(response.data.data));
        } else {
          throw new Error(response.message || "Đã có lỗi xảy ra");
        }
      } catch (error) {
        const description = extractErrorMessage(error);
        toast.error("Thất bại!", {
          description,
        });
      } finally {
        CommonUtils.toggleAppProgress(false);
      }
    };
    fetchFunctions();
  }, []);

  return (
    <AppFormLayoutPanel
      isDoc
      title="Danh sách các chức năng"
      className="content "
      childrenProps={{
        height: "100%",
        overflow: "auto",
      }}
      height={"100%"}
      titleProps={{
        justifyContent: "space-between",
        alignItems: "center",
      }}
      actions={
        <Button variant="contained" onClick={handleSave}>
          Ghi
        </Button>
      }
    >
      <Stack height={"100%"} width={"100%"}>
        <TreeView
          multiSelect
          expandedItems={expandedNodes}
          onExpandedItemsChange={handleNodeToggle}
        >
          <TreeItem
            data={convertDataTreeItem(functions || [])}
            hasMoreICon={false}
            onCheckBox={(node) => (
              <AppCheckbox
                checked={selectedNodes.indexOf(node.id as number) !== -1}
                onClick={(event) => handleNodeSelect(event, node.id)}
                indeterminate={handleIndeterminate(node.id)}
                sx={{
                  height: 28,
                  width: 28,
                  fontSize: 20,
                  padding: "8px",
                }}
              />
            )}
          />
        </TreeView>
      </Stack>
    </AppFormLayoutPanel>
  );
};

export default FunctionPanel;
