"use client";
import { TablePageLayout } from "@/components/common";
import StatusCell from "@/components/common/table/cell/StatusCell";
import {
  FormFieldConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import { APPLICATION, STATUS_APPLICATION } from "@/constant/api.const";
import { IApplication } from "@/app/(main)/root/quan-ly-ung-dung/application.model";
import { ColumnDef } from "@tanstack/react-table";
import React, { useRef } from "react";
import { updateStatusService } from "@/services/app.service";

const ApplicationPage = () => {
  const tableRef = useRef<ITableRef>(null);
  const columns = getColumns(tableRef);

  return (
    <TablePageLayout<IApplication>
      ref={tableRef}
      fetchAll
      visibleCol={VISIBLE_COL}
      apiUrl={APPLICATION}
      tableProps={{
        columns,
        hasDefaultPagination: true,
      }}
      filterConfig={[
        {
          key: "searchKey",
          type: "text",
          label: "T<PERSON><PERSON> kiếm",
          size: 2.4,
        },
      ]}
      actions={["create", "update", "delete"]}
      formConfig={{
        deleteUrl: APPLICATION,
        detailUrl: APPLICATION,
        createUrl: APPLICATION,
        updateUrl: APPLICATION,
        createFields: CREATE_CONFIG,
        updateFields: UPDATE_CONFIG,
      }}
    />
  );
};

export default ApplicationPage;

const getColumns = (
  tableRef: React.RefObject<ITableRef | null>
): ColumnDef<IApplication>[] => [
  {
    id: "code",
    header: "Mã",
    accessorKey: "code",
    size: 60,
  },
  {
    id: "name",
    header: "Tên",
    accessorKey: "name",
    size: 150,
  },
  {
    id: "order",
    accessorKey: "order",
    header: "Thứ tự",
    size: 60,
    meta: { align: "center" },
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Hiển thị",
    size: 60,
    cell: ({ row }) => (
      <StatusCell
        status={row.original.status}
        onStatusChange={(_, value) => {
          updateStatusService({
            id: row.original.id,
            status: Number(value),
            onSuccess: tableRef?.current?.fetchCurrentData,
            url: STATUS_APPLICATION,
          });
        }}
      />
    ),
    meta: { align: "center" },
  },
];

const VISIBLE_COL = [
  { id: "code", name: "Mã" },
  { id: "name", name: "Tên" },
  { id: "order", name: "Thứ tự" },
  { id: "status", name: "Hiển thị" },
];

const CREATE_CONFIG: FormFieldConfig<IApplication>[] = [
  {
    key: "code",
    type: "text",
    label: "Mã",
    size: 12,
    rules: {
      required: "Mã ứng dụng không được để trống",
      maxLength: {
        value: 50,
        message: "Mã không được dài quá 50 ký tự",
      },
    },
  },
  {
    key: "name",
    type: "text",
    label: "Tên",
    size: 12,
    rules: {
      required: "Tên ứng dụng không được để trống",
      maxLength: {
        value: 500,
        message: "Tên không được dài quá 500 ký tự",
      },
    },
  },
  {
    key: "order",
    type: "number",
    label: "Thứ tự",
  },
  {
    key: "status",
    type: "toggle",
    label: "Trạng thái hiển thị",
    size: 12,
  },
];

const UPDATE_CONFIG: FormFieldConfig<IApplication>[] = [
  {
    key: "code",
    type: "text",
    label: "Mã ",
    size: 12,
    rules: { required: "Mã ứng dụng không được để trống" },
    disabled: true,
  },
  {
    key: "name",
    type: "text",
    label: "Tên",
    size: 12,
    rules: { required: "Tên ứng dụng không được để trống" },
  },
  {
    key: "order",
    type: "number",
    label: "Thứ tự",
    size: 12,
  },
  {
    key: "status",
    type: "toggle",
    label: "Trạng thái hiển thị",
    size: 12,
  },
];
