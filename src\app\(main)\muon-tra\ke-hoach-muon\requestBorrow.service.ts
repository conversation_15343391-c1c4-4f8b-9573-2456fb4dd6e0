import http from "@/api";
import { ApiConstant } from "@/constant";
import { DataListResponseModel } from "@/models/response.model";
import { IBorrowRequestAction } from "./borrowRequestModel";
import stringFormat from "string-format";

export const posRequestBorrow = (body: IBorrowRequestAction) => {
  return http.post<DataListResponseModel<unknown>>(
    ApiConstant.BORROW_REQUEST,
    body
  );
};

export const updateRequestBorrow = (id: number, body: IBorrowRequestAction) => {
  return http.put<DataListResponseModel<unknown>>(
    stringFormat(ApiConstant.BORROW_REQUEST_UPDATE, { id }),
    body
  );
};
