import { IDevice } from "@/models/eduDevice.model";

export interface IReduceDevicePayload {
  id: number;
  code: string;
  deviceDefinitionId: number;
  roomId: number;
  teacherId: number;
  quantity: number;
  price: number;
  countryId: number;
  entryDate: string;
  serial: string;
  expireDate: string;
  totalBroken: number;
  totalLost: number;
  totalAvailable: number;
  deviceTransactionItemId: number;
}

export interface IReduceDevice {
  id: number;
  createdBy: number;
  updatedBy: number;
  createdAt: string;
  updatedAt: string;
  documentNumber: string;
  documentDate: string;
  schoolBudgetCategoryId: number;
  schoolBudgetCategoryName: string;
  totalDevices: number;
  totalPrices: number;
  notes: string;
  devices: IDevice[];
}

export interface IReduceParams {
  fromDate: string | null;
  toDate: string | null;
  searchKey?: string;
}

export interface IDeviceInput {
  id: number;
  totalBroken?: number;
  maxBroken?: number;
  totalLost?: number;
  maxLost?: number;
  totalAvailable?: number;
  maxAvailable?: number;
  isError?: boolean;
  isChange?: boolean;
}
