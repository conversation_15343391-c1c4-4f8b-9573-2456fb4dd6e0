import {
  AppFormAutocomplete,
  AppFormTextField,
  GridFormContainer,
} from "@/components/common";
import AppFormDatePicker from "@/components/common/form/AppFormDatePicker";
import { Grid } from "@mui/material";
import React, { memo } from "react";
import { useFormContext } from "react-hook-form";
import { IDeviceTransactionAction } from "../../equipmentDocument.model";
import { useAppSelector } from "@/redux/hook";
import { selectSystemSourceList } from "@/redux/system.slice";
import AppFormLayoutPanel from "@/components/common/AppFormLayoutPanel";

const InfoDocument = () => {
  const {
    control,
    formState: { errors },
  } = useFormContext<IDeviceTransactionAction>();
  const sourceList = useAppSelector(selectSystemSourceList);

  return (
    <AppFormLayoutPanel alignItems="flex-start" title="Thông tin chứng từ">
      <GridFormContainer>
        <Grid size={2.4}>
          <AppFormTextField
            name="documentNumber"
            label="Số chứng từ"
            control={control}
            rules={{
              required: "Số chứng từ không được bỏ trống",
            }}
            textfieldProps={{
              error: Boolean(errors.documentNumber),
              helperText: errors.documentNumber?.message,
            }}
          />
        </Grid>
        <Grid size={2.4}>
          <AppFormDatePicker
            name="documentDate"
            label="Ngày chứng từ"
            control={control}
            rules={{
              required: "Ngày chứng từ không được bỏ trống",
            }}
            datePickerProps={{
              slotProps: {
                textField: {
                  error: Boolean(errors.documentDate),
                  helperText: errors.documentDate?.message,
                },
              },
            }}
          />
        </Grid>
        <Grid size={2.4}>
          <AppFormAutocomplete
            name="schoolBudgetCategoryId"
            label="Nguồn cấp"
            control={control}
            options={sourceList}
            rules={{
              required: "Nguồn cấp không được bỏ trống",
            }}
            autocompleteProps={{
              textFieldProps: {
                error: Boolean(errors?.schoolBudgetCategoryId),
                helperText: errors?.schoolBudgetCategoryId?.message as string,
              },
            }}
          />
        </Grid>
        <Grid size={7.2}>
          <AppFormTextField
            name="notes"
            label="Lý do nhập"
            control={control}
            textfieldProps={{
              multiline: true,
              rows: 3,
            }}
          />
        </Grid>
      </GridFormContainer>
    </AppFormLayoutPanel>
  );
};

export default memo(InfoDocument);
