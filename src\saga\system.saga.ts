import { ApiConstant, EnvConstant } from "@/constant";
import {
  DataListResponseModel,
  DataResponseModel,
} from "@/models/response.model";
import {
  IClass,
  ICountry,
  IDeviceType,
  IGrade,
  IPeriod,
  IRoom,
  ISchoolWeek,
  ISource,
  ISubject,
  ITeacher,
  IUnit,
} from "@/models/system.model";
import { systemActions } from "@/redux/system.slice";
import {
  getClassListService,
  getCountryListService,
  getDeviceTypeListService,
  getGradeListService,
  getPeriodListService,
  getRoomListService,
  getSchoolWeekListService,
  getSourceListService,
  getSubjectListService,
  getTeacherComboListService,
  getUnitListService,
} from "@/services/system.service";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import { PayloadAction } from "@reduxjs/toolkit";
import { call, put, takeLatest } from "redux-saga/effects";
import { toast } from "sonner";

function* getSourceListSaga() {
  try {
    const response: DataListResponseModel<ISource> = yield call(
      getSourceListService
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        systemActions.getSourceListSuccess({
          data: response.data.data || [],
          totalCount: response.data.totalCount,
        })
      );
      return;
    }
    throw new Error(response.message);
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  }
}

function* getSchoolWeekListSaga(
  action: PayloadAction<{ onSuccess?: () => void }>
) {
  toggleAppProgress(true);
  try {
    const response: DataListResponseModel<ISchoolWeek> = yield call(
      getSchoolWeekListService
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        systemActions.getSchoolWeekListSuccess({
          data: response.data.data || [],
          totalCount: response.data.totalCount,
        })
      );
      action.payload?.onSuccess?.();
    } else {
      throw response;
    }
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

function* getUnitListSaga() {
  try {
    const response: DataResponseModel<IUnit[]> = yield call(getUnitListService);

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        systemActions.getUnitListSuccess({
          data: response.data || [],
          totalCount: response.data.length,
        })
      );
      return;
    }
    throw new Error(response.message);
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  }
}

function* getDeviceTypeListSaga() {
  try {
    const response: DataListResponseModel<IDeviceType> = yield call(
      getDeviceTypeListService
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        systemActions.getDeviceTypeListSuccess({
          data: response.data.data || [],
          totalCount: response.data.totalCount,
        })
      );
      return;
    }
    throw new Error(response.message);
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  }
}

function* getSubjectListSaga() {
  try {
    const response: DataListResponseModel<ISubject> = yield call(
      getSubjectListService
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        systemActions.getSubjectListSuccess({
          data: response.data.data || [],
          totalCount: response.data.totalCount,
        })
      );
      return;
    }
    throw new Error(response.message);
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  }
}

function* getClassListSaga() {
  try {
    const response: DataListResponseModel<IClass> = yield call(
      getClassListService
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        systemActions.getClassListSuccess({
          data: response.data.data || [],
          totalCount: response.data.totalCount,
        })
      );
      return;
    }
    throw new Error(response.message);
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  }
}

function* getRoomListSaga() {
  try {
    const response: DataListResponseModel<IRoom> = yield call(
      getRoomListService
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        systemActions.getRoomListSuccess({
          data: response.data.data || [],
          totalCount: response.data.totalCount,
        })
      );
      return;
    }
    throw new Error(response.message);
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  }
}

function* getTeacherComboListSaga() {
  try {
    const response: DataListResponseModel<ITeacher> = yield call(
      getTeacherComboListService
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        systemActions.getTeacherComboListSuccess({
          data: response.data.data || [],
          totalCount: response.data.totalCount,
        })
      );
      return;
    }
    throw new Error(response.message);
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  }
}

function* getCountryListSaga() {
  try {
    const response: DataResponseModel<ICountry[]> = yield call(
      getCountryListService
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        systemActions.getCountryListSuccess({
          data: response.data || [],
          totalCount: response.data.length,
        })
      );
      return;
    }
    throw new Error(response.message);
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  }
}

function* getGradeListSaga() {
  try {
    const response: DataResponseModel<IGrade[]> = yield call(
      getGradeListService
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        systemActions.getGradeListSuccess({
          data: response.data || [],
          totalCount: response.data.length,
        })
      );
      return;
    }
    throw new Error(response.message);
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  }
}

function* getPeriodListSaga() {
  try {
    const response: DataResponseModel<IPeriod[]> = yield call(
      getPeriodListService
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        systemActions.getPeriodListSuccess({
          data: response.data || [],
          totalCount: response.data.length,
        })
      );
      return;
    }
    throw new Error(response.message);
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  }
}

export function* systemSaga() {
  yield takeLatest(systemActions.getSourceList.type, getSourceListSaga);
  yield takeLatest(systemActions.getUnitList.type, getUnitListSaga);
  yield takeLatest(systemActions.getDeviceTypeList.type, getDeviceTypeListSaga);
  yield takeLatest(systemActions.getSubjectList.type, getSubjectListSaga);
  yield takeLatest(systemActions.getClassList.type, getClassListSaga);
  yield takeLatest(systemActions.getRoomList.type, getRoomListSaga);
  yield takeLatest(systemActions.getGradeList.type, getGradeListSaga);
  yield takeLatest(
    systemActions.getTeacherComboList.type,
    getTeacherComboListSaga
  );
  yield takeLatest(systemActions.getCountryList.type, getCountryListSaga);
  yield takeLatest(systemActions.getPeriodList.type, getPeriodListSaga);
  yield takeLatest(systemActions.getSchoolWeekList.type, getSchoolWeekListSaga);
}
