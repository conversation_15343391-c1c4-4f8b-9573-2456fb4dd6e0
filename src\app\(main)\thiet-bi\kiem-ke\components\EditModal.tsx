import InformationForm from "@/app/(main)/thiet-bi/kiem-ke/components/InformationForm";
import InventoryTable from "@/app/(main)/thiet-bi/kiem-ke/components/InventoryTable";
import useInventoryActions from "@/app/(main)/thiet-bi/kiem-ke/hooks/useInventoryActions";
import { inventoryTransactionActions } from "@/app/(main)/thiet-bi/kiem-ke/inventoryTransaction.slice";
import {
  IInventoryTransaction,
  IInventoryTransactionAction,
  ManageTypeEnum,
} from "@/app/(main)/thiet-bi/kiem-ke/type";
import AppModal, { AppModalProps } from "@/components/common/modal/AppModal";
import { AppConstant } from "@/constant";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { selectRoomList, selectSubjectList } from "@/redux/system.slice";
import { Button } from "@mui/material";
import dayjs from "dayjs";
import React, { useCallback, useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";

const EditModal = ({
  fetchCurrentData,
  onClose,
  modalData,
  ...otherProps
}: EditModalProps) => {
  const dispatch = useAppDispatch();
  const methods = useForm({
    defaultValues: INIT_VALUE,
  });
  const { handleSubmit, reset, setValue } = methods;
  const rooms = useAppSelector(selectRoomList);
  const subjects = useAppSelector(selectSubjectList);

  const { handleUpdateInventory } = useInventoryActions();

  const handleClose = useCallback(() => {
    dispatch(inventoryTransactionActions.reset());
    reset(INIT_VALUE);
    onClose();
  }, [onClose, reset]);

  const handleSubmitData = (data) => {
    handleUpdateInventory({ ...data, id: modalData?.id }, () => {
      fetchCurrentData?.();
      onClose();
    });
  };

  useEffect(() => {
    if (modalData) {
      setValue("documentNumber", modalData.documentNumber);
      setValue("inventoryName", modalData.inventoryName);
      setValue(
        "fromDate",
        modalData.fromDate ? dayjs(modalData.fromDate) : null
      );
      setValue("toDate", modalData.toDate ? dayjs(modalData.toDate) : null);
      setValue("notes", modalData.notes);
      setValue("isManageType", modalData.isManageType);
      dispatch(
        inventoryTransactionActions.setInventoryItems(
          modalData.inventoryTransactionItems
        )
      );
      dispatch(
        inventoryTransactionActions.setTransactionTeams(
          modalData.transactionTeams
        )
      );
    }
  }, [modalData]);

  useEffect(() => {
    if (modalData) {
      const options =
        modalData.isManageType === ManageTypeEnum.Room
          ? rooms.map((item) => ({
              id: item.id,
              label: item.name,
            }))
          : subjects.map((item) => ({
              id: item.id,
              label: item.name,
            }));

      const scopeIds = options.filter((item) =>
        modalData.scopeIds.includes(item.id)
      );

      setValue("scopeIds", scopeIds);

      dispatch(
        inventoryTransactionActions.changeFilterChooseDevices({
          key:
            modalData.isManageType === ManageTypeEnum.Room
              ? "roomIds"
              : "schoolSubjectIds",
          value: scopeIds.map((item) => item.id),
        })
      );
    }
  }, [modalData, rooms, subjects]);

  return (
    <FormProvider {...methods}>
      <AppModal
        component="form"
        onClose={handleClose}
        onSubmit={handleSubmit(handleSubmitData)}
        fullScreen
        modalTitleProps={{
          title: "Cập nhật chứng từ",
        }}
        modalContentProps={{
          sx: {
            display: "flex",
            flexDirection: "column",
            bgcolor: "background.grey",
            padding: "12px !important",
            gap: "12px",
          },
          content: (
            <>
              <InformationForm />
              <InventoryTable />
            </>
          ),
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                onClick={handleClose}
                variant="outlined"
                color="secondary"
              >
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
        {...otherProps}
      />
    </FormProvider>
  );
};

export default EditModal;

type EditModalProps = AppModalProps & {
  fetchCurrentData?: () => void;
  modalData: IInventoryTransaction | null;
};

const INIT_VALUE: IInventoryTransactionAction = {
  documentNumber: "",
  inventoryName: "",
  fromDate: dayjs(),
  toDate: dayjs(),
  isManageType: ManageTypeEnum.Room,
  scopeIds: [],
  notes: "",
  inventoryTransactionItems: [],
  transactionTeams: [],
  deleteInventoryTransactionItemIds: [],
};
