import { IOption } from "@/components/common";
import { AppConstant, DataConstant } from "@/constant";
import { ReactNode } from "react";

export const getElementById = (id: string) => {
  if (typeof window !== "undefined") {
    return document.getElementById(id);
  }
};

export const takeLastSlug = (str: string) => {
  if (str) {
    const parts = str.split("-");
    return parts[parts.length - 1];
  }
};

export const getElementByClass = (className: string) => {
  if (typeof window !== "undefined") {
    return document.getElementsByClassName(className);
  }
};

export const getElementByQuerySelector = (className: string) => {
  if (typeof window !== "undefined") {
    return document.querySelector(className);
  }
};

export function debounce<Params extends any[]>(
  func: (...args: Params) => any,
  timeout: number
): (...args: Params) => void {
  let timer: NodeJS.Timeout;
  return (...args: Params) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func(...args);
    }, timeout);
  };
}

export const toggleAppProgress = (isShow: boolean) => {
  const progressEl = getElementById(AppConstant.APP_PROGRESS_ID);
  if (progressEl instanceof HTMLElement) {
    progressEl.style.display = isShow ? "block" : "none";
  }
};

export const getLabelCellFromList = (
  id: number | string | null,
  list: IOption[]
) => {
  return list?.find((item) => item.id === id)?.label ?? "";
};

/**
 * Extracts error message from API response error object
 * @param error - The error object from API response
 * @param defaultMessage - Optional default message if no error message is found
 * @returns Formatted error description ReactNode
 */
export const extractErrorMessage = (
  error: any,
  defaultMessage: string = "Đã xảy ra lỗi trong quá trình xử lý."
): ReactNode => {
  let message: string | null = null;

  // Case 1: If error has an errors object (e.g. { field1: [...], field2: [...] })
  if (error?.errors && typeof error.errors === "object") {
    const errorValues = Object.values(error.errors);
    message = errorValues.flat().join("\n");
  }

  // Case 2: payload string from backend
  else if (!message && typeof error?.payload === "string") {
    message = error.payload;
  }

  // Case 3: Standard message string
  else if (!message && typeof error?.message === "string") {
    message = error.message;
  }

  // Case 4: Raw string
  else if (!message && typeof error === "string") {
    message = error;
  }

  // Final fallback to default
  else if (!message) {
    message = defaultMessage;
  }

  // Render with pre-line formatting for better line breaks
  return <div style={{ whiteSpace: "pre-line" }}>{message}</div>;
};

export const convertDonViToOptions = (arr?: any[]) => {
  if (Array.isArray(arr)) {
    return arr.map((item) => {
      const code = item.schoolCode || item.code;

      return {
        id: item.id,
        code,
        label: `${item.name ?? item.label}${code ? ` (${code})` : ""}`,
      };
    });
  } else {
    return [];
  }
};
