import { AppFormAutocomplete, AppFormTextField } from "@/components/common";
import React, { memo } from "react";
import { IDevices } from "../../../../equipmentDocument.model";
import {
  selectCountries,
  selectRoomList,
  selectTeacher<PERSON>omboList,
} from "@/redux/system.slice";
import { useFormContext } from "react-hook-form";
import { AppFormTextFieldProps } from "@/components/common/form/AppFormTextField";
import AppFormDatePicker, {
  AppFormDatePickerProps,
} from "@/components/common/form/AppFormDatePicker";
import { useAppSelector } from "@/redux/hook";

// AppTextField input
export const InputEditForm = memo(
  ({
    keyChange,
    defaultValue,
    rowIndex,
    ...otherProps
  }: {
    keyChange: keyof IDevices;
    rowIndex: number;
  } & Omit<AppFormTextFieldProps<any>, "control" | "name">) => {
    const { control } = useFormContext();

    return (
      <AppFormTextField
        controlProps={{
          defaultValue,
        }}
        control={control}
        name={`devices.${rowIndex}.${keyChange}`}
        {...otherProps}
      />
    );
  }
);

// Room select
export const RoomSelectEditForm = memo(
  ({ defaultValue, rowIndex }: { defaultValue: number | null; rowIndex }) => {
    const roomList = useAppSelector(selectRoomList);

    const {
      control,
      formState: { errors },
    } = useFormContext();

    const fieldError = errors?.devices?.[rowIndex]?.roomId;

    return (
      <AppFormAutocomplete
        control={control}
        name={`devices.${rowIndex}.roomId`}
        autocompleteProps={{
          textFieldProps: {
            error: Boolean(fieldError),
            helperText: fieldError?.message,
          },
        }}
        controlProps={{
          defaultValue:
            roomList.find((item) => item.id === defaultValue) ?? null,
        }}
        options={roomList}
      />
    );
  }
);

// Country select
export const CountrySelectEditForm = memo(
  ({ defaultValue, rowIndex }: { rowIndex; defaultValue: number | null }) => {
    const countries = useAppSelector(selectCountries);
    const { control } = useFormContext();

    return (
      <AppFormAutocomplete
        control={control}
        name={`devices.${rowIndex}.countryId`}
        controlProps={{
          defaultValue: countries.find((item) => item.id === defaultValue),
        }}
        options={countries}
      />
    );
  }
);

// Date picker
export const DateEditForm = memo(
  ({
    keyChange,
    rowIndex,
    defaultValue,
    ...otherProps
  }: {
    keyChange: keyof IDevices;
    rowIndex;
    defaultValue?: any;
  } & Omit<
    AppFormDatePickerProps<any>,
    "control" | "name" | "controlProps"
  >) => {
    const { control } = useFormContext();

    return (
      <AppFormDatePicker
        control={control}
        name={`devices.${rowIndex}.${keyChange}`}
        controlProps={{
          defaultValue,
        }}
        {...otherProps}
      />
    );
  }
);
