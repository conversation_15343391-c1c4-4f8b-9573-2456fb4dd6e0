import { AppFormTextField, AppTable } from "@/components/common";
import DeleteCell from "@/components/common/table/cell/DeleteCell";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import { IEduDevice } from "@/models/eduDevice.model";
import { Stack, Typography } from "@mui/material";
import { ColumnDef } from "@tanstack/react-table";
import React, { memo, useMemo } from "react";
import { useFormContext } from "react-hook-form";
import { ModalSelectedType } from "../ChooseDeviceOfRoomModal";

const DeviceSelectedTable = ({ fields, onRemove, onRemoveAll }) => {
  const columns = useMemo(
    () => getColumn(onRemove, onRemoveAll),
    [onRemove, onRemoveAll]
  );

  return (
    <Stack height="100%">
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        mb={1}
        height={36}
      >
        <Typography
          whiteSpace="nowrap"
          color="primary"
          fontWeight={500}
        >{`Danh sách thiết bị đã chọn (${fields.length ?? 0})`}</Typography>
      </Stack>
      <AppTable
        columns={columns}
        data={fields}
        totalData={fields.length}
        columnPinning={COLUMN_PIN}
        {...TABLE_MODAL_FULL_HEIGHT}
      />
    </Stack>
  );
};

export default memo(DeviceSelectedTable);
const COLUMN_PIN = {
  right: ["sl"],
  left: ["index", "delete"],
};

const getColumn = (onRemove, onRemoveAll): ColumnDef<IEduDevice>[] => [
  {
    id: "index",
    header: "STT",
    cell: ({ row }) => row.index + 1,
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "delete",
    cell: ({ row }) => <DeleteCell onClick={() => onRemove(row.index)} />,
    header: () => (
      <DeleteCell
        sx={{
          color: "common.white",
        }}
        onClick={() => onRemoveAll([])}
      />
    ),
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    header: "Mã thiết bị",
    accessorKey: "code",
    size: 50,
    meta: {
      cellSx: {
        whiteSpace: "nowrap",
      },
    },
  },
  {
    id: "deviceName",
    accessorKey: "deviceName",
    header: "Tên thiết bị",
    size: 150,
  },
  {
    header: "Môn học",
    accessorKey: "schoolSubjectName",
    size: 80,
  },
  {
    header: "Khối lớp",
    accessorKey: "gradeName",
    size: 80,
  },
  {
    header: "SL",
    size: 100,
    maxSize: 100,
    id: "sl",
    meta: {
      cellSx: {
        maxWidth: 100,
      },
      align: "right",
    },
    cell: ({ row }) => <QuantityInputCell index={row.index} />,
  },
  {
    header: "ĐVT",
    accessorKey: "deviceUnitName",
    size: 50,
  },
];

const QuantityInputCell = memo(({ index }: { index: number }) => {
  const { control } = useFormContext<ModalSelectedType>();

  return (
    <AppFormTextField
      name={`deviceSelected.${index}.totalBorrow`}
      control={control}
      textfieldProps={{
        type: "number",
        inputProps: { min: 1 },
        autoFocus: false,
      }}
    />
  );
});
