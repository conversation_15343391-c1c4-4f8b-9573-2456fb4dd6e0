import http from "@/api";
import { ApiConstant, EnvConstant } from "@/constant";
import { DataResponseModel } from "@/models/response.model";
import { CommonUtils } from "@/utils";
import { toast } from "sonner";

const useRecordLoss = () => {
  const handleRecordLoss = async (data: any, onSuccess: () => void) => {
    try {
      const response: DataResponseModel<any> = await http.post(
        ApiConstant.DEVICE_ISSUE,
        data
      );

      if (response.code === ApiConstant.ERROR_CODE_OK) {
        toast.success("Thành công", {
          description: "<PERSON><PERSON> nhận hỏng/mất thành công",
        });
        onSuccess();
      } else {
        throw response;
      }
    } catch (error) {
      EnvConstant.IS_DEV && console.log(error);
      toast.error("Thất bại!", {
        description: CommonUtils.extractErrorMessage(error),
      });
    }
  };

  return {
    handleRecordLoss,
  };
};

export default useRecordLoss;
