"use client";

import { inventoryTransactionSaga } from "@/app/(main)/thiet-bi/kiem-ke/inventoryTransaction.saga";
import { inventoryTransactionActions } from "@/app/(main)/thiet-bi/kiem-ke/inventoryTransaction.slice";
import { IInventoryTransaction } from "@/app/(main)/thiet-bi/kiem-ke/type";
import { TablePageLayout } from "@/components/common";
import { INVENTORY_TRANSACTION } from "@/constant/api.const";
import { useAppDispatch, useAppStore } from "@/redux/hook";
import { systemActions } from "@/redux/system.slice";
import { createInjectableSaga } from "@/saga/injectableSaga";
import { systemSaga } from "@/saga/system.saga";
import { FormatUtils } from "@/utils";
import { ColumnDef } from "@tanstack/react-table";
import dynamic from "next/dynamic";
import { useEffect, useState } from "react";
const CreateModal = dynamic(() => import("./components/CreateModal"), {
  ssr: false,
});
const EditModal = dynamic(() => import("./components/EditModal"), {
  ssr: false,
});

const InventoryTransactionPage = () => {
  const dispatch = useAppDispatch();
  const store = useAppStore();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    createInjectableSaga("systemReducer", systemSaga).injectInto(store);
    createInjectableSaga(
      "inventoryTransactionReducer",
      inventoryTransactionSaga
    ).injectInto(store);

    setIsClient(true);

    return () => {
      dispatch(systemActions.systemReset());
      dispatch(inventoryTransactionActions.reset());
    };
  }, []);

  if (!isClient) return null;

  return (
    <TablePageLayout<IInventoryTransaction>
      apiUrl={INVENTORY_TRANSACTION}
      actions={["create", "delete", "update"]}
      formConfig={{
        deleteUrl: INVENTORY_TRANSACTION,
        detailUrl: INVENTORY_TRANSACTION,
      }}
      tableProps={{
        columns: COLUMN,
      }}
      filterConfig={[
        {
          key: "searchKey",
          type: "text",
          label: "Số phiếu",
          size: 2.4,
        },
        {
          key: "dateRange",
          type: "dateRange",
          label: "Ngày chứng từ",
          keyDateRange: ["fromDate", "toDate"],
          value: [null, null],
        },
      ]}
      CreateModalComponent={CreateModal}
      EditModalComponent={EditModal}
    />
  );
};

export default InventoryTransactionPage;

const COLUMN: ColumnDef<IInventoryTransaction>[] = [
  {
    id: "documentNumber",
    accessorKey: "documentNumber",
    header: "Số phiếu",
  },
  {
    id: "fromDate",
    header: "Từ ngày",
    accessorFn: (row) => FormatUtils.formatDayjsWithType(row.fromDate),
    size: 100,
  },
  {
    id: "toDate",
    header: "Đến ngày",
    accessorFn: (row) => FormatUtils.formatDayjsWithType(row.toDate),
    size: 100,
  },
  {
    id: "notes",
    header: "Nội dung",
    accessorKey: "notes",
  },
];
