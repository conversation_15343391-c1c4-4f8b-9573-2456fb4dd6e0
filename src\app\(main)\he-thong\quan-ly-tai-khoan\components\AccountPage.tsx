"use client";

import { TablePageLayout } from "@/components/common";
import StatusCell from "@/components/common/table/cell/StatusCell";
import {
  FormFieldConfig,
  FilterConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import { ACCOUNT, STATUS_ACCOUNT, UPDATE_ACCOUNT } from "@/constant/api.const";
import { IAccount } from "@/app/(main)/he-thong/quan-ly-tai-khoan/account.model";
import { ColumnDef } from "@tanstack/react-table";
import React, { useMemo, useRef, useState } from "react";
import { GENDER_TYPE_LIST, STATUS_ACTIVE_LIST } from "@/constant/data.const";
import { updateStatusService } from "@/services/app.service";
import { IconButton } from "@mui/material";
import dynamic from "next/dynamic";
import { ReloadIcon } from "@/components/icons";
import { formatDayjsWithType } from "@/utils/format.utils";
const ResetPasswordModal = dynamic(() => import("./ResetPasswordModal"), {
  ssr: false,
});

const AccountPage = () => {
  const tableRef = useRef<ITableRef>(null);
  const [data, setData] = useState<IAccount | null>(null);

  const tableProps = useMemo(() => {
    const columns = getColumns(tableRef, setData);
    return {
      columns,
      columnPinning: {
        right: ["restart"],
        left: ["index"],
      },
    };
  }, []);

  return (
    <>
      <TablePageLayout<IAccount>
        ref={tableRef}
        visibleCol={VISIBLE_COL}
        apiUrl={ACCOUNT}
        tableProps={tableProps}
        filterConfig={FILTER_CONFIG}
        actions={["update" /*, "delete" */]}
        formConfig={{
          detailUrl: ACCOUNT,
          updateUrl: UPDATE_ACCOUNT,
          updateFields: UPDATE_CONFIG,
          // deleteUrl: ACCOUNT,
        }}
      />
      {Boolean(data) && (
        <ResetPasswordModal
          data={data}
          isOpen={Boolean(data)}
          onClose={() => {
            setData(null);
            tableRef?.current?.fetchCurrentData?.();
          }}
        />
      )}
    </>
  );
};

export default AccountPage;

const FILTER_CONFIG: FilterConfig[] = [
  {
    key: "searchKey",
    type: "text",
    label: "Tìm kiếm",
    size: 2.4,
  },
  {
    key: "status",
    type: "select",
    label: "Trạng thái",
    size: 2.4,
    options: STATUS_ACTIVE_LIST,
  },
];

const getColumns = (
  tableRef: React.RefObject<ITableRef | null>,
  setData: (data: IAccount | null) => void
): ColumnDef<IAccount>[] => [
  {
    id: "fullName",
    header: "Thông tin tài khoản",
    accessorKey: "fullName",
    size: 200,
  },
  {
    id: "cardTypeId",
    header: "Loại tài khoản",
    accessorKey: "cardTypeId",
    size: 100,
    cell: ({ row }) =>
      row.original.cardTypeId === 1 ? "Giáo viên" : "Học sinh",
  },
  {
    id: "userName",
    accessorKey: "userName",
    header: "Tên đăng nhập",
    size: 200,
  },
  {
    id: "dateOfBirth",
    accessorKey: "dateOfBirth",
    header: "Ngày sinh",
    size: 100,
    meta: { align: "center" },
    cell: ({ row }) =>
      formatDayjsWithType(row.original.dateOfBirth, "DD/MM/YYYY"),
  },
  {
    id: "applicationFunctionName",
    accessorKey: "applicationFunctionName",
    header: "Nhóm quyền",
    size: 100,
    meta: { align: "center" },
  },
  {
    id: "gender",
    accessorKey: "gender",
    header: "Giới tính",
    size: 100,
    meta: { align: "center" },
    cell: ({ row }) => (row.original.gender === 1 ? "Nam" : "Nữ"),
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Kích hoạt",
    size: 60,
    meta: { align: "center" },
    cell: ({ row }) => (
      <StatusCell
        status={row.original.status}
        onStatusChange={(_, value) => {
          updateStatusService({
            id: row.original.id,
            status: Number(value),
            onSuccess: tableRef?.current?.fetchCurrentData,
            url: STATUS_ACCOUNT,
          });
        }}
      />
    ),
  },
  {
    id: "restart",
    header: "Khởi tạo",
    size: 60,
    meta: { align: "center" },
    cell: ({ row }) => (
      <IconButton aria-label="Khởi tạo" onClick={() => setData(row.original)}>
        <ReloadIcon sx={{ fontSize: 24 }} />
      </IconButton>
    ),
  },
];

const VISIBLE_COL = [
  { id: "fullName", name: "Thông tin tài khoản" },
  { id: "cardTypeId", name: "Loại tài khoản" },
  { id: "userName", name: "Tên đăng nhập" },
  { id: "dateOfBirth", name: "Ngày sinh" },
  { id: "applicationFunctionName", name: "Nhóm quyền" },
  { id: "gender", name: "Giới tính" },
  { id: "status", name: "Kích hoạt" },
];

const UPDATE_CONFIG: FormFieldConfig<IAccount>[] = [
  {
    key: "fullName",
    type: "text",
    label: "Họ tên",
    size: 12,
    rules: { required: "Họ tên không được để trống" },
  },
  {
    key: "applicationFunctionCode",
    type: "select",
    label: "Nhóm quyền",
    size: 12,
    selectConfig: {
      valueKey: "code",
    },
    apiListUrl: "/v1/application-function",
  },
  {
    key: "gender",
    type: "select",
    label: "Giới tính",
    size: 12,
    selectConfig: {
      options: GENDER_TYPE_LIST,
    },
  },
  {
    key: "dateOfBirth",
    type: "date",
    label: "Ngày sinh",
    size: 12,
  },
  {
    key: "status",
    type: "toggle",
    label: "Trạng thái kích hoạt",
    size: 12,
  },
];
