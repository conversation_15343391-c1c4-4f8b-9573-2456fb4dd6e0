"use client";

import { TablePageLayout } from "@/components/common";
import StatusCell from "@/components/common/table/cell/StatusCell";
import {
  FormFieldConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import {
  APPLICATION_FUNCTION,
  STATUS_APPLICATION_FUNCTION,
} from "@/constant/api.const";
import { IApplicationFunction } from "@/app/(main)/he-thong/quan-ly-nhom-nguoi-dung/applicationFunction.model";
import { ColumnDef } from "@tanstack/react-table";
import React, { useRef } from "react";
import { updateStatusService } from "@/services/app.service";

const UserGroupManagePage = () => {
  const tableRef = useRef<ITableRef>(null);
  const columns = getColumns(tableRef);

  return (
    <TablePageLayout<IApplicationFunction>
      ref={tableRef}
      fetchAll
      visibleCol={VISIBLE_COL}
      apiUrl={APPLICATION_FUNCTION}
      tableProps={{
        columns,
        hasDefaultPagination: true,
      }}
      filterConfig={[
        {
          key: "searchKey",
          type: "text",
          label: "Tìm kiếm",
          size: 2.4,
        },
      ]}
      actions={["create", "update", "delete"]}
      formConfig={{
        deleteUrl: APPLICATION_FUNCTION,
        detailUrl: APPLICATION_FUNCTION,
        createUrl: APPLICATION_FUNCTION,
        updateUrl: APPLICATION_FUNCTION,
        createFields: CREATE_CONFIG,
        updateFields: UPDATE_CONFIG,
      }}
    />
  );
};

export default UserGroupManagePage;

const getColumns = (
  tableRef: React.RefObject<ITableRef | null>
): ColumnDef<IApplicationFunction>[] => [
  {
    id: "code",
    header: "Mã nhóm",
    accessorKey: "code",
    size: 60,
  },
  {
    id: "name",
    header: "Tên nhóm",
    accessorKey: "name",
    size: 150,
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Hiển thị",
    size: 60,
    cell: ({ row }) => (
      <StatusCell
        status={row.original.status}
        onStatusChange={(_, value) => {
          updateStatusService({
            id: row.original.id,
            status: Number(value),
            onSuccess: tableRef?.current?.fetchCurrentData,
            url: STATUS_APPLICATION_FUNCTION,
          });
        }}
      />
    ),
    meta: { align: "center" },
  },
];

const VISIBLE_COL = [
  { id: "code", name: "Mã" },
  { id: "name", name: "Tên nhóm" },
  { id: "status", name: "Hiển thị" },
];

const CREATE_CONFIG: FormFieldConfig<IApplicationFunction>[] = [
  {
    key: "code",
    type: "text",
    label: "Mã nhóm",
    size: 12,
    rules: {
      required: "Mã nhóm không được để trống",
      maxLength: {
        value: 50,
        message: "Mã nhóm không được dài quá 50 ký tự",
      },
    },
  },
  {
    key: "name",
    type: "text",
    label: "Tên nhóm",
    size: 12,
    rules: {
      required: "Tên nhóm không được để trống",
      maxLength: {
        value: 500,
        message: "Tên nhóm không được dài quá 500 ký tự",
      },
    },
  },
  {
    key: "status",
    type: "toggle",
    label: "Trạng thái hiển thị",
    size: 12,
  },
];

const UPDATE_CONFIG: FormFieldConfig<IApplicationFunction>[] = [
  {
    key: "code",
    type: "text",
    label: "Mã nhóm",
    size: 12,
    rules: { required: "Mã nhóm không được để trống" },
  },
  {
    key: "name",
    type: "text",
    label: "Tên nhóm",
    size: 12,
    rules: { required: "Tên nhóm không được để trống" },
  },
  {
    key: "status",
    type: "toggle",
    label: "Trạng thái hiển thị",
    size: 12,
  },
];
