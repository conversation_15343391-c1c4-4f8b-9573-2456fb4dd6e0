import { MANAGE_BY } from "@/models/system.model";
import {
  IDeviceRoomGroup,
  IDevices,
  IDeviceTransactionItems,
  IDeviceTransactionUI,
} from "./equipmentDocument.model";
import { AppConstant, DataConstant } from "@/constant";
import { formatDayjsWithType } from "@/utils/format.utils";
import {
  getOptionCode,
  getOptionId,
  getOptionLabel,
} from "@/components/common/AppAutoComplete";
import { v4 as uuid } from "uuid";
import { Dayjs } from "dayjs";

interface MapEquipmentItemParams {
  item: IDevices;
  totalDevices: number;
  isQuantityMode: boolean;
  deviceName?: string;
  deviceCode?: string;
  maxIndexItem?: number;
  index?: number; // index của item trong danh sách
  deviceUnitName?: string;
  deviceId?: string | number;
  totalAvailable?: number;
}

function safeNumber(value: any): number {
  const num = Number(value);
  return isNaN(num) ? 0 : num;
}

export const getMaxCodeIndex = (devices: { code: string }[]) => {
  return devices.reduce((max, device) => {
    const match = device.code.match(/(\d+)$/);
    const number = match ? parseInt(match[1], 10) : 0;
    return Math.max(max, number);
  }, 0);
};

export function renderDeviceCode(
  deviceCode: string | undefined,
  startRegister: number | null | undefined,
  index: number | null | undefined
): string {
  if (!deviceCode || startRegister == null || index == null) return "";
  const codeNumber = (Number(startRegister) + index)
    .toString()
    .padStart(4, "0");
  return `${deviceCode}-${codeNumber}`;
}

export const mapEquipmentItem = ({
  item,
  totalDevices,
  isQuantityMode,
  deviceName,
  deviceCode,
  maxIndexItem,
  index,
  deviceUnitName,
  deviceId,
  totalAvailable,
}: MapEquipmentItemParams): IDevices => {
  const code = renderDeviceCode(deviceCode, maxIndexItem, index);

  return {
    ...item,
    id: item.itemId || item.id, // Lấy lại api gốc DB nếu có
    totalAvailable: totalAvailable ?? item.totalAvailable,
    code: isQuantityMode ? code : item.code || code,
    deviceUnitName,
    roomId: getOptionId(item.roomId),
    roomName: getOptionLabel(item.roomId),
    countryId: getOptionId(item.countryId),
    countryName: getOptionLabel(item.countryId),
    expireDate: item.expireDate
      ? (formatDayjsWithType(
          item.expireDate,
          AppConstant.DATE_TIME_YYYYescape
        ) as any)
      : null,
    entryDate: item.entryDate
      ? (formatDayjsWithType(
          item.entryDate,
          AppConstant.DATE_TIME_YYYYescape
        ) as any)
      : null,
    codeIndex: index ?? 0,
    deviceName,
    deviceCode,
    price: safeNumber(item.price),
    quantity: safeNumber(item.quantity),
    totalPrices: isQuantityMode
      ? safeNumber(item.price) * safeNumber(totalDevices)
      : safeNumber(item.price),

    deviceDefinitionId: deviceId,
  };
};

export const getEntriesEquipmentDocumentAndDevice = (
  data: IDeviceTransactionUI & IDeviceTransactionItems
) => {
  const isManageDevice = data.manageBy === MANAGE_BY.isManageDevice;
  const isManageQuantity = data.manageBy === MANAGE_BY.isManageQuantity;
  let device: IDevices[] = [];

  if (isManageDevice) {
    device =
      data.devices?.map((item, index) =>
        mapEquipmentItem({
          item,
          totalDevices: 1,
          isQuantityMode: false,
          deviceName: data.deviceName,
          deviceCode: data.deviceCode,
          maxIndexItem: data.maxIndexItem,
          index,
          deviceUnitName: getOptionLabel(data.deviceUnitId),
          deviceId: data.id,
          totalAvailable: data.totalAvailable,
        })
      ) ?? [];
  } else if (isManageQuantity && data.devices?.length) {
    device = data.devices.map((item) =>
      mapEquipmentItem({
        item: item,
        totalDevices: Number(data.totalDevices),
        isQuantityMode: true,
        deviceName: data.deviceName,
        deviceCode: data.deviceCode,
        maxIndexItem: 1,
        index: 0,
        deviceUnitName: getOptionLabel(data.deviceUnitId),
        deviceId: data.id,
        totalAvailable: data.totalAvailable,
      })
    );
  }

  const totalPrices = device.reduce(
    (sum, item) => sum + Number(item.totalPrices ?? 0) * Number(item.quantity),
    0
  );
  const totalDevices = device.reduce(
    (sum, item) => sum + Number(item.quantity ?? 0),
    0
  );

  const equipmentDocumentEntry: IDeviceTransactionItems = {
    id: data.id,
    statisticCode: data.statisticCode,
    deviceCode: data.deviceCode,
    deviceName: data.deviceName,
    description: "",
    deviceUnitId: getOptionId(data.deviceUnitId),
    deviceUnitName: getOptionLabel(data.deviceUnitId),
    schoolDeviceTypeId: getOptionId(data.schoolDeviceTypeId),
    schoolDeviceTypeName: getOptionLabel(data.schoolDeviceTypeId),
    schoolSubjectName: getOptionLabel(data.schoolSubjectId),
    deviceDTITypeId: null,
    schoolSubjectId: getOptionId(data.schoolSubjectId),
    gradeCodes: data.gradeCodes?.map(getOptionCode) ?? [],
    userTypes: data.userTypes?.map(getOptionId) ?? [],
    isManageQuantity: Number(isManageQuantity),
    isManageDevice: Number(isManageDevice),
    isConsumable: data.isConsumable,
    isSelfMade: data.isSelfMade,
    minimumQuantity: data.minimumQuantity,
    status: DataConstant.STATUS_TYPE.active,
    itemId: data.itemId,
    devices: device,
    totalAvailable: data.totalAvailable,
    totalDevices,
    maxIndexItem: Number(data.maxIndexItem ?? 1) - 1,
    totalPrices: isManageDevice ? totalDevices * totalPrices : totalPrices,
  };

  return { equipmentDocumentEntry, device };
};

export function findMissingEquipmentDocumentEntryIds(
  originalList?: IDeviceTransactionItems[],
  filteredList?: IDeviceTransactionItems[]
): number[] {
  if (!originalList) return [];
  const filteredIdSet = new Set((filteredList ?? []).map((item) => item.id));
  return originalList
    .filter((item) => !filteredIdSet.has(item.id))
    .map((item) => item.id as number);
}

export const cleanIdString = (
  deviceTransactionItems: IDeviceTransactionItems
) => {
  return {
    ...deviceTransactionItems,
    id:
      typeof deviceTransactionItems.itemId === "number"
        ? deviceTransactionItems.itemId
        : typeof deviceTransactionItems.id === "number"
        ? deviceTransactionItems.id
        : 0,
    devices: deviceTransactionItems.devices?.map((device) => ({
      ...device,
      id:
        typeof device.itemId === "number"
          ? device.itemId
          : typeof device.id === "number"
          ? device.id
          : 0,
      deviceDefinitionId:
        typeof device.deviceDefinitionId === "number"
          ? device.deviceDefinitionId
          : 0,
    })),
  };
};

export const buildDeviceRow = (
  d: Partial<IDevices>,
  quantity: number,
  documentDate?: Date | null | Dayjs
): IDevices => ({
  id: d?.id ?? uuid(),
  itemId: d?.itemId,
  serial: d?.serial ?? "",
  price: d?.price ?? 0,
  quantity,
  roomId: d?.roomId ?? null,
  countryId: d?.countryId ?? null,
  entryDate: d?.entryDate ?? (documentDate as any) ?? null,
  expireDate: d?.expireDate ?? null,
  deviceTransactionItemId: d?.deviceTransactionItemId ?? 0,
  code: d?.code ?? "",
});

export function groupDevicesByRoomSimple(
  deviceDefinitions: IDeviceTransactionItems[]
): IDeviceRoomGroup[] {
  const result: IDeviceRoomGroup[] = [];

  deviceDefinitions.forEach((def) => {
    const roomMap = new Map<number | string, IDeviceRoomGroup>();

    def?.devices?.forEach((dev) => {
      const key = dev.roomId;
      if (typeof key === "number") {
        if (!roomMap.has(key)) {
          roomMap.set(key, {
            ...dev,
            ...def,
            deviceDefinitionId: def.id,
            deviceDefinitionOriginalId: def.id,
            id: uuid(),
            devices: [],
            totalDevices: 0,
            totalPrices: 0,
            deviceIds: [],
          });
        }

        const group = roomMap.get(key)!;

        group.deviceIds?.push(dev.id as number);
        group.devices?.push(dev);
        group.totalDevices =
          (group.totalDevices ?? 0) + Number(dev.quantity ?? 0);
        group.totalPrices =
          (group.totalPrices ?? 0) +
          Number(dev.quantity ?? 0) * Number(dev.price ?? 0);
      }
    });

    result.push(...Array.from(roomMap.values()));
  });

  return result;
}
