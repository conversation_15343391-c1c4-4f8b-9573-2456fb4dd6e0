import http from "@/api";
import {
  DataListResponseModel,
  DataResponseModel,
} from "@/models/response.model";
import { ISearchParams, ITeacher } from "@/models/system.model";
import { IDeviceLiquidationAction } from "./deviceLiquidation.model";
import {
  DEVICE_LIQUIDATION,
  DEVICE_LIQUIDATION_BY_ID,
  TEACHER_COMBO,
} from "@/constant/api.const";
import stringFormat from "string-format";

export const getTeacherChooseService = (params: ISearchParams) => {
  return http.get<DataListResponseModel<ITeacher>>(TEACHER_COMBO, {
    params,
  });
};

export const postDeviceLiquidationService = (
  body: IDeviceLiquidationAction
) => {
  return http.post<DataResponseModel<number>>(DEVICE_LIQUIDATION, body);
};

export const putUpdateDeviceLiquidationService = (
  body: IDeviceLiquidationAction,
  id?: number
) => {
  return http.put<DataResponseModel<unknown>>(
    stringFormat(DEVICE_LIQUIDATION_BY_ID, { id }),
    body
  );
};
