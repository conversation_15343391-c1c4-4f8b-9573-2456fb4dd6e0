"use client";

import { formatPrice } from "@/utils/format.utils";
import { Box, BoxProps, TextField, TextFieldProps } from "@mui/material";
import { forwardRef, memo, useState } from "react";

const AppTextField = forwardRef(
  (
    {
      isDecimal,
      sx,
      type,
      InputProps,
      size = "medium",
      slotProps,
      isCurrency,
      onInput,
      value,
      disabled,
      onBlur,
      inputProps,
      boxProps,
      ...otherProps
    }: AppTextFieldProps,
    ref
  ) => {
    const [showCurrency, setShowCurrency] = useState(isCurrency);

    const hasStartAdornment = Boolean(InputProps?.startAdornment);
    const isNumberType = type === "number";

    const { min, max } = isNumberType
      ? (slotProps?.htmlInput as SlotExtend) || inputProps || {}
      : {};

    const numericMin = min !== undefined ? parseFloat(min) : undefined;
    const numericMax = max !== undefined ? parseFloat(max) : undefined;

    const handleInput = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (!isNumberType) return;

      const value = parseFloat(e.target.value);
      if (isNaN(value)) return;

      if (numericMin !== undefined && value < numericMin) {
        e.target.value = numericMin.toString();
      }

      if (numericMax !== undefined && value > numericMax) {
        e.target.value = numericMax.toString();
      }

      onInput?.(e);
    };

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      if (isNumberType) {
        if (isCurrency) {
          setShowCurrency(false);
        }
        setTimeout(() => {
          e.target.select();
        }, 0);
      }
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      if (isCurrency) setShowCurrency(true);
      onBlur?.(e);
    };

    return (
      <Box
        {...boxProps}
        sx={{ position: "relative", width: "100%", ...boxProps?.sx }}
      >
        <TextField
          disabled={disabled}
          sx={{
            ".MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline": {
              borderColor: "primary.main",
            },
            ".MuiOutlinedInput-notchedOutline": {
              borderColor: "border.main",
            },
            ".MuiInputBase-root.MuiOutlinedInput-root.Mui-error .MuiOutlinedInput-notchedOutline":
              {
                borderColor: "error.main",
              },
            "& .MuiInputBase-root": {
              backgroundColor: "common.white",
              borderRadius: "4px",
              pl: hasStartAdornment ? 0 : "6px",
              "&.Mui-disabled": {
                backgroundColor: "#f3f4f6",
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "grey.350",
                },
              },
            },
            "& .MuiFormLabel-root, & .MuiInputLabel-shrink": {
              fontWeight: "400 !important",
            },
            textarea: {
              pl: 1.75,
            },
            input: {
              px: 1,
              textAlign: isNumberType ? "end" : "start",
              "&::-webkit-outer-spin-button, &::-webkit-inner-spin-button": {
                ml: 0.5,
              },
              height: size === "small" ? "30px" : "36px",
              py: 0,
              borderRadius: "4px",
              boxShadow: "0 0 0 1000px white inset !important",
              "&.Mui-disabled": {
                boxShadow: "0 0 0 1000px #f3f4f6 inset !important",
              },
              "&:-webkit-autofill": {
                boxShadow: "0 0 0 1000px white inset !important",
                WebkitTextFillColor: "#333333 !important",
                caretColor: "#333333 !important",
              },
            },
            "& .MuiInputLabel-root": {
              transform:
                size === "small"
                  ? "translate(12px, 7px) scale(1)"
                  : "translate(14px, 8px) scale(1)",
            },
            "& .MuiInputLabel-shrink": {
              transform: "translate(14px, -7px) scale(0.75)",
            },
            ...sx,
          }}
          size={size}
          fullWidth
          inputRef={ref}
          InputProps={InputProps}
          inputProps={inputProps}
          slotProps={slotProps}
          onFocus={handleFocus}
          type={type}
          onKeyDown={
            isNumberType ? (e) => handleKeyDown(e, isDecimal) : undefined
          }
          onInput={isNumberType ? handleInput : onInput}
          onBlur={handleBlur}
          {...otherProps}
          value={value}
        />
        {isCurrency && showCurrency && (
          <Box
            sx={{
              position: "absolute",
              paddingRight: "10px",
              right: 2,
              top: "50%",
              transform: "translateY(-50%)",
              bgcolor: disabled ? "#f3f4f6" : "white",
              color: disabled ? "text.disabled" : "text.primary",
              pointerEvents: "none",
              textAlign: "right",
              width: "calc(100% - 4px)",
              whiteSpace: "nowrap",
              overflow: "hidden",
            }}
          >
            {formatPrice(value as string)}
          </Box>
        )}
      </Box>
    );
  }
);

export type AppTextFieldProps = TextFieldProps & {
  isDecimal?: boolean;
  isCurrency?: boolean;
  boxProps?: BoxProps;
};
type SlotExtend = {
  min?: string;
  max?: string;
  step?: string;
};

AppTextField.displayName = "AppTextField";
export default memo(AppTextField);
const handleKeyDown = (
  e: React.KeyboardEvent<HTMLDivElement>,
  isDecimal?: boolean
) => {
  const input = e.currentTarget.querySelector("input");
  if (!input) return;

  const { value, selectionStart } = input;

  const blockKeys = ["e", "E", "ê", "Ê", "+", "_"];
  if (!isDecimal) blockKeys.push(".");

  if (blockKeys.includes(e.key)) {
    e.preventDefault();
    return;
  }

  if (e.key === "-") {
    const hasMinus = value.includes("-");
    const cursorAtStart = selectionStart === 0;

    if (hasMinus || !cursorAtStart) {
      e.preventDefault();
    }
  }
};
