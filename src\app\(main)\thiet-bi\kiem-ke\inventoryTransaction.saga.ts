import { inventoryTransactionActions } from "@/app/(main)/thiet-bi/kiem-ke/inventoryTransaction.slice";
import { ApiConstant, EnvConstant } from "@/constant";
import { IDevice, IDeviceParams } from "@/models/eduDevice.model";
import {
  DataListResponseModel,
  IPaginationModel,
} from "@/models/response.model";
import { getDeviceService } from "@/services/eduDevice.service";
import { extractErrorMessage, toggleAppProgress } from "@/utils/common.utils";
import { PayloadAction } from "@reduxjs/toolkit";
import { call, put, takeLatest } from "redux-saga/effects";
import { toast } from "sonner";

function* getChooseDeviceSaga(
  action: PayloadAction<IDeviceParams & IPaginationModel>
) {
  try {
    toggleAppProgress(true);

    const response: DataListResponseModel<IDevice> = yield call(
      getDeviceService,
      action.payload
    );
    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(inventoryTransactionActions.setChooseDevices(response.data));
    } else {
      throw new Error(response.message);
    }
  } catch (error: any) {
    EnvConstant.IS_DEV && console.log(error);
    toast.error("Thất bại", {
      description: extractErrorMessage(error),
    });
  } finally {
    toggleAppProgress(false);
  }
}

export function* inventoryTransactionSaga() {
  yield takeLatest(
    inventoryTransactionActions.getChooseDevice.type,
    getChooseDeviceSaga
  );
}
