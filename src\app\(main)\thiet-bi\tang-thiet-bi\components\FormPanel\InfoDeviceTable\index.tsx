import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>kel<PERSON>,
  <PERSON>ack,
  Tab,
  Tabs,
  <PERSON><PERSON><PERSON>,
} from "@mui/material";
import React, { memo, useCallback, useMemo, useState } from "react";
import TocTable from "./TocTable";
import DetailTable from "./DetailTable";
import {
  FullScreenIcon,
  FullScreenIconExit,
  PlusIcon,
} from "@/components/icons";
import AppFormLayoutPanel from "@/components/common/AppFormLayoutPanel";
import dynamic from "next/dynamic";
import ChooseDeviceModal from "../../../../../../../components/sn-common/ChooseDeviceModal";
import { useAppDispatch } from "@/redux/hook";
import { equipmentDocumentActions } from "../../../equipmentDocument.slice";

const AddDeviceModal = dynamic(
  () =>
    import("../../device-modal/AddDeviceModal").then((mod) =>
      React.memo(mod.default)
    ),
  { ssr: false }
);

const InfoDeviceTable = () => {
  const dispatch = useAppDispatch();
  const [tabValue, setTabValue] = useState<TAB>(TAB.toc); // tab đang chọn
  const [delayedTab, setDelayedTab] = useState<TAB>(TAB.toc); // tab thực sự dùng để render table
  const [isPending, setIsPending] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const handleClose = useCallback(() => setIsOpen(false), []);

  const handleChangeTab = useCallback((_: React.SyntheticEvent, value: TAB) => {
    setTabValue(value);
    setIsPending(true);
    setTimeout(() => {
      setDelayedTab(value);
      setIsPending(false);
    }, 350);
  }, []);

  const handleToggleFullscreen = useCallback(() => {
    setIsFullscreen((prev) => !prev);
  }, []);

  const tableProps = useMemo(() => {
    return {
      tableContainerProps: {
        sx: {
          height: isFullscreen ? "100%" : 400,
        },
      },
      sx: {
        height: isFullscreen ? "calc(100% - 40px)" : "unset",
      },
      boxProps: {
        sx: {
          flex: 1,
          minHeight: 0,
        },
      },
    };
  }, [isFullscreen]);

  const fullscreenSx = useMemo(
    () =>
      isFullscreen
        ? {
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            zIndex: 500,
          }
        : {},
    [isFullscreen]
  );

  return (
    <>
      <AppFormLayoutPanel
        sx={fullscreenSx}
        alignItems="flex-start"
        flex={1}
        mt={isFullscreen ? 0 : 1.5}
        title="Thông tin thiết bị"
        childrenProps={{
          p: 0,
          flex: 1,
          minHeight: 0,
        }}
        titleProps={{ justifyContent: "space-between", pr: 1.5 }}
        actions={
          <Tooltip title={isFullscreen ? "Thu nhỏ" : "Phóng to"}>
            <IconButton
              sx={{ width: 24, height: 24 }}
              onClick={handleToggleFullscreen}
            >
              {isFullscreen ? <FullScreenIconExit /> : <FullScreenIcon />}
            </IconButton>
          </Tooltip>
        }
      >
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          py={0.75}
        >
          <Tabs value={tabValue} onChange={handleChangeTab} slotProps={{}}>
            <Tab label="Xem theo đầu thiết bị" value={TAB.toc} />
            <Tab label="Xem theo chi tiết thiết bị" value={TAB.detail} />
          </Tabs>

          <Stack direction="row" spacing={1} pr={2}>
            <Button
              onClick={() => setIsOpen(true)}
              startIcon={<PlusIcon />}
              variant="contained"
              size="small"
            >
              Thêm thiết bị
            </Button>
            <ChooseDeviceModal
              onChooseDevice={(data) => {
                dispatch(equipmentDocumentActions.chooseDevice(data));
              }}
            />
          </Stack>
        </Stack>

        <Stack flex={1} minHeight={0} position="relative">
          {isPending && <LoadingSkeleton isFullscreen={isFullscreen} />}

          {delayedTab === TAB.toc && <TocTable tableProps={tableProps} />}
          {delayedTab === TAB.detail && <DetailTable tableProps={tableProps} />}
        </Stack>
      </AppFormLayoutPanel>

      <AddDeviceModal isOpen={isOpen} onClose={handleClose} />
    </>
  );
};

const enum TAB {
  toc,
  detail,
}

export default memo(InfoDeviceTable);

// Loading Skeleton
const LoadingSkeleton = memo(({ isFullscreen }: { isFullscreen: boolean }) => (
  <Stack
    spacing={2}
    position="absolute"
    top={0}
    left={0}
    right={0}
    bottom={0}
    zIndex={1000}
    bgcolor="white"
    flex={1}
    minHeight={0}
    overflow="hidden"
  >
    <Skeleton variant="rectangular" sx={{ minHeight: 40 }} />
    {Array.from({ length: isFullscreen ? 40 : 15 }).map((_, i) => (
      <Skeleton key={i} variant="text" />
    ))}
  </Stack>
));
