import React, { memo } from "react";
import { SvgIcon, SvgIconProps } from "@mui/material";

const CloseEyePasswordIcon = ({ sx, ...otherProps }: SvgIconProps) => {
  return (
    <SvgIcon
      viewBox="0 0 24 24"
      width="24px"
      height="24px"
      sx={{ fontSize: "inherit", width: "24px", height: "24px", ...sx }}
      {...otherProps}
    >
      <path
        d="M21.0707 9.20999C21.2895 8.88172 21.2166 8.44401 20.9248 8.18869C20.5965 7.93337 20.1588 8.00632 19.9035 8.33459C19.867 8.37106 15.8183 13.1493 11.0036 13.1493C6.3348 13.1493 2.10372 8.33459 2.06724 8.29811C1.81192 8.00631 1.33774 7.96984 1.04594 8.22516C0.754139 8.48049 0.717667 8.95466 0.97299 9.24646C1.04594 9.35588 1.92134 10.3407 3.30739 11.435L1.44716 13.3681C1.15536 13.6599 1.19183 14.1341 1.48363 14.3894C1.55658 14.5353 1.73896 14.6083 1.92133 14.6083C2.10371 14.6083 2.28608 14.5353 2.43198 14.3894L4.4381 12.3104C5.38645 12.9669 6.51717 13.587 7.75732 14.0247L6.99135 16.6144C6.88192 17.0156 7.10077 17.4169 7.502 17.5263C7.57495 17.5263 7.64789 17.5263 7.72085 17.5263C8.04912 17.5263 8.34092 17.3074 8.41387 16.9792L9.17984 14.3894C9.76344 14.4989 10.3835 14.5718 11.0036 14.5718C11.6237 14.5718 12.2437 14.4989 12.8273 14.3894L13.5933 16.9427C13.6663 17.271 13.9945 17.4898 14.2863 17.4898C14.3593 17.4898 14.4322 17.4898 14.4687 17.4533C14.8699 17.3439 15.0888 16.9427 14.9794 16.5415L14.2134 13.9882C15.4535 13.5505 16.5843 12.9304 17.5326 12.2739L19.5023 14.3165C19.6482 14.4624 19.8305 14.5353 20.0129 14.5353C20.1953 14.5353 20.3776 14.4624 20.5236 14.3165C20.8153 14.0247 20.8153 13.587 20.56 13.2952L18.6998 11.362C20.1953 10.2678 21.0707 9.20999 21.0707 9.20999Z"
        fill="currentColor"
      />
    </SvgIcon>
  );
};

export default memo(CloseEyePasswordIcon);
