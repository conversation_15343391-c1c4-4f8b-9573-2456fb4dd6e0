import { AppFormTextField } from "@/components/common";
import AppFormLayoutPanel from "@/components/common/AppFormLayoutPanel";
import AppFormDatePicker from "@/components/common/form/AppFormDatePicker";
import { Grid } from "@mui/material";
import { memo } from "react";
import { useFormState } from "react-hook-form";

const InformationForm = ({ control }) => {
  const { errors } = useFormState({ control });

  return (
    <AppFormLayoutPanel title="Thông tin chung">
      <Grid container columnSpacing={5} rowSpacing={2}>
        <Grid size={3}>
          <AppFormTextField
            control={control}
            name="documentNumber"
            direction="row"
            labelProps={{
              sx: {
                overflow: "initial",
                minWidth: 80,
              },
            }}
            label="Số phiếu"
            rules={{
              required: "Vui lòng nhập số phiếu.",
            }}
            textfieldProps={{
              error: !!errors.documentNumber,
              helperText: errors.documentNumber?.message as string,
            }}
          />
        </Grid>
        <Grid size={3}>
          <AppFormDatePicker
            control={control}
            name="documentDate"
            direction="row"
            labelProps={{
              sx: {
                overflow: "initial",
                minWidth: 80,
              },
            }}
            label="Ngày lập"
            rules={{
              required: "Vui lòng chọn ngày lập.",
            }}
            datePickerProps={{
              maxDate: null,
              slotProps: {
                textField: {
                  error: !!errors.documentDate,
                  helperText: errors.documentDate?.message as string,
                },
              },
            }}
          />
        </Grid>
        <Grid size={6} />
        <Grid size={6}>
          <AppFormTextField
            control={control}
            name="notes"
            label="Nội dung"
            direction="row"
            labelProps={{
              sx: {
                overflow: "initial",
                minWidth: 80,
              },
            }}
            textfieldProps={{
              error: !!errors.notes,
              helperText: errors.notes?.message as string,
            }}
          />
        </Grid>
      </Grid>
    </AppFormLayoutPanel>
  );
};

export default memo(InformationForm);
