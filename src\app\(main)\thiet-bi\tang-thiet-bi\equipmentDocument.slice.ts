import {
  createSlice,
  createSelector,
  WithSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { rootReducer } from "@/redux/reducer";
import {
  IDeviceTransaction,
  IDeviceMap,
  IDeviceTransactionItems,
  IDeviceTransactionAction,
  IDevices,
  IDeviceRoomGroup,
  IDeviceTransactionUI,
} from "./equipmentDocument.model";
import { RootState } from "@/redux/store";
import { AppConstant } from "@/constant";
import { IEduDevice } from "@/models/eduDevice.model";
import { IOption } from "@/components/common";
import { Dayjs } from "dayjs";
import { v4 as uuid } from "uuid";
import { toOption } from "@/components/common/AppAutoComplete";
import { UseFormSetValue } from "react-hook-form";

/* ------------- State & Interface ------------- */
export interface IInitialState {
  isFetching: boolean;

  equipmentDocumentEntry: IDeviceTransactionItems[];
  deviceMap: IDeviceMap;
  documentEntry: null | IDeviceTransaction;

  deviceTransactionItem: null | IDeviceTransactionItems;

  isPendingBuildDevice: boolean;

  deviceIdsRoomGroup: Record<string, (number | string)[]>;

  deviceTransactionItemEdited: (number | string)[];
}

const initialState: IInitialState = {
  isFetching: false,
  equipmentDocumentEntry: [],
  deviceMap: {},
  documentEntry: null,

  deviceTransactionItem: null,

  isPendingBuildDevice: false,
  deviceIdsRoomGroup: {},
  deviceTransactionItemEdited: [],
};

/* ------------- Selector ------------- */
export const selectEquipmentDocumentEntry = createSelector(
  [
    (state: RootState) =>
      state.equipmentDocumentReducer?.equipmentDocumentEntry,
  ],
  (equipmentDocumentEntry) => [...(equipmentDocumentEntry ?? [])].reverse()
);

export const selectDevices = createSelector(
  [(state: RootState) => state.equipmentDocumentReducer?.deviceMap],
  (deviceMap) => {
    if (!deviceMap) return [];
    return Object.values(deviceMap).flat().reverse();
  }
);

export const selectDeviceMap = createSelector(
  [(state: RootState) => state.equipmentDocumentReducer?.deviceMap],
  (deviceMap) => deviceMap
);

export const selectDeviceMapList = createSelector(
  [(state: RootState) => state.equipmentDocumentReducer?.deviceMap],
  (deviceMap) => Object.values(deviceMap || {}).flat()
);

export const selectIsPendingBuildDevice = createSelector(
  [(state: RootState) => state.equipmentDocumentReducer?.isPendingBuildDevice],
  (isPendingBuildDevice) => isPendingBuildDevice
);

export const selectDeviceTransactionItem = createSelector(
  [(state: RootState) => state.equipmentDocumentReducer],
  (equipmentDocumentReducer) =>
    equipmentDocumentReducer?.deviceTransactionItem ?? null
);

export const selectQuantityOfEquipment = (data: IDeviceTransactionItems) =>
  createSelector(
    [(state: RootState) => state.equipmentDocumentReducer?.deviceMap],
    (deviceMap) => {
      if (!data) return AppConstant.NOT_AVAILABLE_VALUE;
      if (!deviceMap) return AppConstant.NOT_AVAILABLE_VALUE;

      if (data.id) {
        if (data.isManageDevice) {
          return (
            deviceMap[data.id]?.reduce(
              (sum, item) => sum + (item.quantity || 0),
              0
            ) ?? AppConstant.NOT_AVAILABLE_VALUE
          );
        } else if (data.isManageQuantity) {
          return data.totalDevices ?? AppConstant.NOT_AVAILABLE_VALUE;
        }
      }
      return AppConstant.NOT_AVAILABLE_VALUE;
    }
  );

export const selectTotalPricesOfEquipment = (data: IDeviceTransactionItems) =>
  createSelector(
    [(state: RootState) => state.equipmentDocumentReducer?.deviceMap],
    (deviceMap) => {
      if (!data) return AppConstant.NOT_AVAILABLE_VALUE;
      if (!deviceMap) return AppConstant.NOT_AVAILABLE_VALUE;

      if (data.id) {
        if (data.isManageDevice) {
          return (
            deviceMap[data.id]?.reduce(
              (sum, item) => sum + (Number(item.price) || 0),
              0
            ) ?? AppConstant.NOT_AVAILABLE_VALUE
          );
        } else if (data.isManageQuantity) {
          return data.totalPrices ?? AppConstant.NOT_AVAILABLE_VALUE;
        }
      }
      return AppConstant.NOT_AVAILABLE_VALUE;
    }
  );
export const makeSelectRoomOfDevices = () =>
  createSelector(
    [
      (state: RootState, defId: string) =>
        state.equipmentDocumentReducer?.deviceMap[defId] || [],
      (state: RootState, _: string, idGroup: string) =>
        state.equipmentDocumentReducer?.deviceIdsRoomGroup?.[idGroup] || [],
    ],
    (devices, deviceIds) => {
      const items = devices.filter((item) =>
        deviceIds.includes(item.id as string | number)
      );

      if (items.length > 0) {
        const [first] = items;
        const allSame = items.every(
          (i) => i.roomId === first.roomId && i.roomId !== null
        );

        return allSame
          ? { id: first.roomId!, label: first.roomName ?? "" }
          : null;
      }

      return null;
    }
  );
export const makeSelectCountryOfDevices = () =>
  createSelector(
    [
      (state: RootState, defId: string) =>
        state.equipmentDocumentReducer?.deviceMap[defId] || [],
      (state: RootState, _: string, groupId: string) =>
        state.equipmentDocumentReducer?.deviceIdsRoomGroup?.[groupId] || [],
    ],
    (devices, deviceIds) => {
      const items = devices.filter((item) =>
        deviceIds.includes(item.id as string | number)
      );
      if (items.length === 0) return null;

      const [first] = items;
      const allSame = items.every(
        (i) => i.countryId === first.countryId && i.countryId !== null
      );
      return allSame
        ? { id: first.countryId!, label: first.countryName ?? "" }
        : null;
    }
  );

export const makeSelectExpireDateOfDevices = () =>
  createSelector(
    [
      (state: RootState, defId: string) =>
        state.equipmentDocumentReducer?.deviceMap[defId] || [],
      (state: RootState, _: string, groupId: string) =>
        state.equipmentDocumentReducer?.deviceIdsRoomGroup?.[groupId] || [],
    ],
    (items, deviceIds) => {
      const filtered = items.filter((item) =>
        deviceIds.includes(item.id as string | number)
      );
      if (filtered.length === 0) return null;

      const [first] = filtered;
      const allSame =
        first.expireDate !== null &&
        filtered.every((i) => i.expireDate === first.expireDate);
      return allSame ? first.expireDate : null;
    }
  );

export const makeSelectMinCodeOfDevices = () =>
  createSelector(
    [
      (state: RootState, defId: string) =>
        state.equipmentDocumentReducer?.deviceMap[defId] || [],
      (state: RootState, _: string, groupId: string) =>
        state.equipmentDocumentReducer?.deviceIdsRoomGroup?.[groupId] || [],
    ],
    (devices, deviceIds) => {
      const filtered = devices.filter((d) =>
        deviceIds.includes(d.id as string | number)
      );

      // Bắt phần số cuối sau dấu gạch
      const nums = filtered
        .map((d) => {
          const m = d.code?.match(/-(\d+)$/);
          return m ? parseInt(m[1], 10) : null;
        })
        .filter((n): n is number => n !== null);

      if (nums.length === 0) {
        // Chưa có, bắt đầu từ 1
        return 1;
      }

      // Tìm min
      return Math.min(...nums);
    }
  );
export const makeSelectMaxCodeOfDevices = () =>
  createSelector(
    [
      (state: RootState, defId: string) =>
        state.equipmentDocumentReducer?.deviceMap[defId] || [],
      (state: RootState, _: string, groupId: string) =>
        state.equipmentDocumentReducer?.deviceIdsRoomGroup?.[groupId] || [],
    ],
    (devices, deviceIds) => {
      const filtered = devices.filter((d) =>
        deviceIds.includes(d.id as string | number)
      );

      // Bắt phần số cuối sau dấu gạch
      const nums = filtered
        .map((d) => {
          const m = d.code?.match(/-(\d+)$/);
          return m ? parseInt(m[1], 10) : null;
        })
        .filter((n): n is number => n !== null);

      if (nums.length === 0) {
        // Chưa có, trả về 1 (hoặc "0001" tuỳ UI)
        return "0001";
      }

      const max = Math.max(...nums);
      // Chuyển về string với 4 chữ số
      return String(max).padStart(4, "0");
    }
  );
export const makeSelectTotalDevicesOfDevices = () =>
  createSelector(
    [
      (state: RootState, defId: string) =>
        state.equipmentDocumentReducer?.deviceMap[defId] || [],
      (state: RootState, _: string, groupId: string) =>
        state.equipmentDocumentReducer?.deviceIdsRoomGroup?.[groupId] || [],
    ],
    (devices, deviceIds) => {
      const filtered = devices.filter((d) =>
        deviceIds.includes(d.id as string | number)
      );
      return filtered.reduce((sum, d) => sum + (Number(d.quantity) || 0), 0);
    }
  );
export const makeSelectTotalPricesOfDevices = () =>
  createSelector(
    [
      (state: RootState, defId: string) =>
        state.equipmentDocumentReducer?.deviceMap[defId] || [],
      (state: RootState, _: string, groupId: string) =>
        state.equipmentDocumentReducer?.deviceIdsRoomGroup?.[groupId] || [],
    ],
    (devices, deviceIds) => {
      const filtered = devices.filter((d) =>
        deviceIds.includes(d.id as string | number)
      );

      return filtered.reduce((sum, d) => {
        const quantity = Number(d.quantity) || 0;
        const price = Number(d.price) || 0;
        return sum + quantity * price;
      }, 0);
    }
  );

/* ------------- Reducers ------------- */
const reducers = {
  addEquipmentDocumentEntry: (
    state: IInitialState,
    action: PayloadAction<{
      equipmentDocumentEntry: IDeviceTransactionItems;
      device: IDevices[];
    }>
  ) => {
    const { equipmentDocumentEntry, device } = action.payload;

    state.equipmentDocumentEntry = [
      ...state.equipmentDocumentEntry,
      equipmentDocumentEntry,
    ];
    state.deviceMap[equipmentDocumentEntry.id as string] = device;
  },
  editEquipmentDocumentEntry: (
    state: IInitialState,
    action: PayloadAction<{
      equipmentDocumentEntry: IDeviceTransactionItems;
      device: IDevices[];
    }>
  ) => {
    const { equipmentDocumentEntry, device } = action.payload;

    state.equipmentDocumentEntry = state.equipmentDocumentEntry.map((item) =>
      item.id === equipmentDocumentEntry.id
        ? { ...item, ...equipmentDocumentEntry }
        : item
    );

    state.deviceMap[equipmentDocumentEntry.id as string] = device;
    state.deviceTransactionItemEdited.push(equipmentDocumentEntry.id as string);
  },
  deleteEquipmentDocumentEntry: (
    state: IInitialState,
    action: PayloadAction<IDeviceRoomGroup>
  ) => {
    const deviceDefinitionId = action.payload.deviceDefinitionId;
    if (!deviceDefinitionId) return;

    const ids = action.payload.deviceIds;
    if (!ids?.length) return;

    const index = state.equipmentDocumentEntry.findIndex(
      (entry) => entry.id === deviceDefinitionId
    );

    if (index !== -1) {
      const entry = state.equipmentDocumentEntry[index];
      entry.devices = entry.devices?.filter(
        (dev) => !ids.includes(dev.id as string)
      );

      if (!entry.devices?.length) {
        state.equipmentDocumentEntry.splice(index, 1); // Xóa cả item cha nếu không còn device
        delete state.deviceMap[deviceDefinitionId];
      } else {
        state.deviceMap[deviceDefinitionId] = entry.devices;
      }
    }
  },
  deleteDevice: (
    state: IInitialState,
    action: PayloadAction<IDevices | null>
  ) => {
    const data = action.payload;

    if (data && data.deviceDefinitionId && data.id) {
      const { deviceDefinitionId, id } = data;
      const deviceList = state.deviceMap[deviceDefinitionId] ?? [];

      const newDevice = deviceList.filter((item) => item.id !== id);
      state.deviceMap[deviceDefinitionId] = newDevice;

      if (newDevice.length === 0) {
        state.equipmentDocumentEntry = state.equipmentDocumentEntry.filter(
          (item) => item.id !== data.deviceDefinitionId
        );
      } else {
        const entryIndex = state.equipmentDocumentEntry.findIndex(
          (item) => item.id === deviceDefinitionId
        );
        if (entryIndex !== -1) {
          state.deviceTransactionItemEdited.push(
            state.equipmentDocumentEntry[entryIndex].id as number
          );
        }
      }
    }
  },
  chooseDevice: (state: IInitialState, action: PayloadAction<IEduDevice>) => {},
  chooseDeviceSuccess: (
    state: IInitialState,
    action: PayloadAction<IDeviceTransactionItems>
  ) => {
    // push vào edited
    if (typeof action.payload.id !== "undefined") {
      state.equipmentDocumentEntry = [
        ...state.equipmentDocumentEntry,
        {
          ...action.payload,
          // changed: true,
        },
      ];

      state.deviceMap[action.payload.id] = action.payload.devices ?? [];
    }
  },
  getMapDevice: (
    state: IInitialState,
    action: PayloadAction<{
      id: number | string | undefined;
      setValue: UseFormSetValue<IDeviceTransactionUI>;
    }>
  ) => {
    const setValue = action.payload.setValue;
    const devices = state.deviceMap[action.payload.id as string];

    setValue("totalAvailable", devices[0]?.totalAvailable);

    setValue(
      "devices",
      devices?.map((item) => ({
        ...item,
        serial: item.serial ?? "",
        itemId: item.id,
        roomId: toOption(item.roomId, item.roomName) as any,
        countryId: toOption(item.countryId, item.countryName) as any,
      })) ?? []
    );
  },
  changeRoomOfDevice: (
    state: IInitialState,
    action: PayloadAction<{ data: IDeviceRoomGroup; room: IOption }>
  ) => {
    const { data, room } = action.payload;
    if (data.deviceDefinitionId && room) {
      const deviceIds = data.devices?.map((item) => item.id) ?? [];

      const targetDevices = state.deviceMap[data.deviceDefinitionId];
      if (targetDevices) {
        targetDevices.forEach((item) => {
          if (deviceIds.includes(item.id)) {
            item.roomId = room.id as number;
            item.roomName = room.label;
          }
        });
      }

      const entryIndex = state.equipmentDocumentEntry.findIndex(
        (item) => item.id === data.deviceDefinitionId
      );

      if (entryIndex !== -1) {
        state.deviceTransactionItemEdited.push(
          state.equipmentDocumentEntry[entryIndex].id as number
        );
      }
    }
  },
  changeCountryOfDevice: (
    state: IInitialState,
    action: PayloadAction<{ data: IDeviceRoomGroup; country: IOption }>
  ) => {
    const { data, country } = action.payload;
    if (data.deviceDefinitionId) {
      const deviceIds = data.devices?.map((item) => item.id) ?? [];

      const targetDevices = state.deviceMap[data.deviceDefinitionId];
      if (targetDevices) {
        targetDevices.forEach((item) => {
          if (deviceIds.includes(item.id)) {
            item.countryId = (country?.id as number) ?? 0;
            item.countryName = country?.label ?? "";
          }
        });
      }

      const entryIndex = state.equipmentDocumentEntry.findIndex(
        (item) => item.id === data.deviceDefinitionId
      );

      if (entryIndex !== -1) {
        state.deviceTransactionItemEdited.push(
          state.equipmentDocumentEntry[entryIndex].id as number
        );
      }
    }
  },
  changeExpireDateOfDevice: (
    state: IInitialState,
    action: PayloadAction<{
      data: IDeviceRoomGroup;
      expireDate: Date | Dayjs | null;
    }>
  ) => {
    const { data, expireDate } = action.payload;

    if (data.deviceDefinitionId) {
      const deviceIds = data.devices?.map((item) => item.id) ?? [];

      const targetDevices = state.deviceMap[data.deviceDefinitionId];
      if (targetDevices) {
        targetDevices.forEach((item) => {
          if (deviceIds.includes(item.id)) {
            item.expireDate = (expireDate as Date) ?? null;
          }
        });
      }

      const entryIndex = state.equipmentDocumentEntry.findIndex(
        (item) => item.id === data.deviceDefinitionId
      );

      if (entryIndex !== -1) {
        state.deviceTransactionItemEdited.push(
          state.equipmentDocumentEntry[entryIndex].id as number
        );
      }
    }
  },

  addDocumentEntry: (
    state: IInitialState,
    action: PayloadAction<{
      data: IDeviceTransactionAction;
      onSuccess?: () => void;
    }>
  ) => {},
  updateDocumentEntry: (
    state: IInitialState,
    action: PayloadAction<{
      data: IDeviceTransactionAction;
      defaultData: IDeviceTransaction | null;
      onSuccess?: () => void;
    }>
  ) => {},
  getDocumentEntryDetail: (
    state: IInitialState,
    action: PayloadAction<number>
  ) => {},
  getDocumentEntryDetailSuccess: (
    state: IInitialState,
    action: PayloadAction<IDeviceTransaction>
  ) => {
    state.documentEntry = action.payload;
  },
  // khỏi tạo dữ liệu khi mở modal sửa chứng từ
  initDocumentEntry: (
    state: IInitialState,
    action: PayloadAction<IDeviceTransaction>
  ) => {
    const data = action.payload;
    state.documentEntry = data;
    state.equipmentDocumentEntry = data.deviceTransactionItems ?? [];
    data.deviceTransactionItems.forEach((item) => {
      state.deviceMap[item.id as number] = item.devices ?? [];
    });
  },
  clearActionData: (state: IInitialState) => {
    state.deviceMap = {};
    state.documentEntry = null;
    state.equipmentDocumentEntry = [];
    state.deviceTransactionItemEdited = [];
    state.deviceIdsRoomGroup = {};
    state.deviceTransactionItem = null;
  },
  setDeviceTransactionItemDetail: (
    state: IInitialState,
    action: PayloadAction<IEduDevice>
  ) => {
    state.deviceTransactionItem = action.payload;
  },
  togglePendingBuildDevice: (
    state: IInitialState,
    action: PayloadAction<boolean>
  ) => {
    state.isPendingBuildDevice = action.payload;
  },
  setFetching: (state: IInitialState, action: PayloadAction<boolean>) => {
    state.isFetching = action.payload;
  },
  changeMinRegister: (
    state: IInitialState,
    action: PayloadAction<{ data: IDeviceRoomGroup; value: number }>
  ) => {
    const { data, value } = action.payload;
    const deviceIds = state.deviceIdsRoomGroup[data.id as string];
    const { deviceDefinitionId, deviceCode: prefix } = data;

    const all = [...state.deviceMap[deviceDefinitionId as string]].toReversed();
    if (!all || Number.isNaN(value)) return;

    // 1) Lọc ra các device tạm và giữ thứ tự
    const temp = all.filter((d) => typeof d.id === "string");

    // 2) Tìm vị trí bắt đầu
    const startIndex = temp.findIndex((d) => deviceIds.includes(d.id as any));
    if (startIndex < 0) return;

    // 3) Tạo set reservedNumbers
    const reserved = new Set<number>();
    const re = /-(\d+)$/;

    // 3a) Từ thiết bị đã lưu
    all
      .filter((d) => typeof d.id === "number")
      .forEach((d) => {
        const m = d.code?.match(re);
        if (m) reserved.add(+m[1]);
      });

    // 3b) Từ các temp trước startIndex (giữ nguyên code cũ)
    for (let i = 0; i < startIndex; i++) {
      const m = temp[i].code?.match(re);
      if (m) reserved.add(+m[1]);
    }

    // 4) Gán lại từ startIndex trở đi, đảm bảo không trùng
    let next = value;
    const pad = (n: number) => `${prefix}-${String(n).padStart(4, "0")}`;

    for (let i = startIndex; i < temp.length; i++) {
      while (reserved.has(next)) next++;
      temp[i].code = pad(next);
      reserved.add(next);
      next++;
    }

    // 5) Cập nhật lại vào state
    state.deviceMap[deviceDefinitionId as string] = all
      .toReversed()
      .map((d) => {
        if (typeof d.id === "string") {
          const updated = temp.find((t) => t.id === d.id);
          return updated ?? d;
        }
        return d;
      });

    // mark changed
    const idx = state.equipmentDocumentEntry.findIndex(
      (e) => e.id === deviceDefinitionId
    );

    if (idx !== -1) {
      state.deviceTransactionItemEdited.push(
        state.equipmentDocumentEntry[idx].id as number
      );
    }
  },

  changeQuantityOfDevice: (
    state: IInitialState,
    action: PayloadAction<{ data: IDeviceRoomGroup; value: number }>
  ) => {
    const { data, value } = action.payload;
    if (Number.isNaN(value) || value < 0) return;

    const defId = data.deviceDefinitionId;
    const groupId = String(data.id);
    const entryIndex = state.equipmentDocumentEntry.findIndex(
      (e) => e.id === defId
    );
    if (entryIndex === -1) return;

    const entry = state.equipmentDocumentEntry[entryIndex];
    let devices = state.deviceMap[defId as string] || [];

    // Ensure group exists
    state.deviceIdsRoomGroup[groupId] ??= devices.map((d) => d.id as number);
    const groupIds = state.deviceIdsRoomGroup[groupId];

    if (entry.isManageQuantity === 1) {
      // --- Quản lý theo số lượng ---
      const ordered = groupIds
        .map((id) => devices.find((d) => String(d.id) === String(id)))
        .filter((d): d is (typeof devices)[0] => !!d);

      const currentTotal = ordered.reduce(
        (sum, d) => sum + (d.quantity || 0),
        0
      );
      const diff = value - currentTotal;

      if (diff > 0 && ordered.length) {
        ordered[0].quantity = (ordered[0].quantity || 0) + diff;
      } else if (diff < 0) {
        let remain = -diff;
        for (const d of ordered) {
          const sub = Math.min(d.quantity || 0, remain);
          d.quantity = (d.quantity || 0) - sub;
          remain -= sub;
          if (remain <= 0) break;
        }
      }

      // Loại bỏ quantity = 0
      devices = devices.filter((d) => (d.quantity ?? 0) > 0);
      state.deviceMap[defId as string] = devices;

      // Cập nhật lại group IDs
      state.deviceIdsRoomGroup[groupId] = ordered
        .filter((d) => (d.quantity ?? 0) > 0)
        .map((d) => d.id as number);
    } else if (entry.isManageDevice === 1) {
      // --- Quản lý theo thiết bị ---
      const idSet = new Set(groupIds.map(String));
      const groupDevices = devices.filter((d) => idSet.has(String(d.id)));
      const otherDevices = devices.filter((d) => !idSet.has(String(d.id)));

      const currentCount = groupDevices.length;
      const diff = value - currentCount;

      if (diff > 0) {
        const template = groupDevices[0];
        if (!template) return;

        // ——— Sinh newOnes với code không trùng ———
        const existingCodes = devices
          .map((d) => d.code)
          .filter((c): c is string => !!c);

        const maxNum = existingCodes.reduce((max, code) => {
          const m = code.match(/(\d+)$/);
          if (!m) return max;
          const num = parseInt(m[1], 10);
          return Number.isNaN(num) ? max : Math.max(max, num);
        }, 0);

        const prefix = template.code?.replace(/(\d+)$/, "") || "";

        const newOnes = Array.from({ length: diff }, (_, i) => {
          const seq = String(maxNum + i + 1).padStart(3, "0");
          return {
            ...template,
            id: uuid(),
            code: `${prefix}${seq}`,
            quantity: 1,
          };
        });

        devices = [...otherDevices, ...groupDevices, ...newOnes];
        state.deviceMap[defId as string] = devices;
        state.deviceIdsRoomGroup[groupId] = [
          ...groupDevices.map((d) => d.id as number),
          ...newOnes.map((d) => d.id),
        ];
      } else if (diff < 0) {
        const removeCount = -diff;
        const toRemove = groupDevices
          .slice(-removeCount)
          .map((d) => String(d.id));

        const remaining = groupDevices.filter(
          (d) => !toRemove.includes(String(d.id))
        );
        devices = [...otherDevices, ...remaining];
        state.deviceMap[defId as string] = devices;
        state.deviceIdsRoomGroup[groupId] = remaining.map(
          (d) => d.id as number
        );
      }
    }
  },
  initDeviceIdsRoom: (
    state: IInitialState,
    action: PayloadAction<IDeviceRoomGroup[]>
  ) => {
    const data = action.payload;
    data.forEach((item) => {
      state.deviceIdsRoomGroup[item.id as string] = item.deviceIds;
    });
  },
  reset: (state: IInitialState) => {
    state.isFetching = false;
    state.equipmentDocumentEntry = [];
    state.deviceMap = {};
    state.documentEntry = null;
    state.deviceTransactionItemEdited = [];
    state.deviceIdsRoomGroup = {};
    state.deviceTransactionItem = null;
  },
};

/* ------------- Selectors ------------- */
const selectors = {
  selectSlice: (state: IInitialState) => state,
  selectIsFetching: (state: IInitialState) => state.isFetching,
};

/* ------------- Slice ------------- */
export const equipmentDocumentSlice = createSlice({
  name: "equipmentDocumentReducer",
  initialState,
  reducers,
  selectors,
});

/* ------------- Export Actions ------------- */
export const equipmentDocumentActions = equipmentDocumentSlice.actions;

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices
    extends WithSlice<typeof equipmentDocumentSlice> {}
}

const injectedEquipmentDocumentSlice =
  equipmentDocumentSlice.injectInto(rootReducer);

/* ------------- Export Selectors ------------- */
export const equipmentDocumentSelectors = {
  selectSlice: injectedEquipmentDocumentSlice.selectors.selectSlice,
  selectIsFetching: createSelector(
    [injectedEquipmentDocumentSlice.selectors.selectSlice],
    (slice) => slice.isFetching
  ),
};
