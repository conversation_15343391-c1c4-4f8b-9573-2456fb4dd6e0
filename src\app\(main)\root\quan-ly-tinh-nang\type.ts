import { DataConstant } from "@/constant";

export interface IFeature {
  id: number;
  code: string;
  name: string;
  applicationId: number;
  parentId: number;
  parentName: string;
  order: number;
  status: DataConstant.STATUS_TYPE;
  isSystem: DataConstant.STATUS_TYPE;
  createdBy: string | null;
  createdAt: string | null;
  updatedBy: string | null;
  updatedAt: string | null;
  applicationFeatures: IFeature[];
  listGroupsUnitCode: string[];
  menuConfigId: number | null;
  //Payload key
  groupUnitCodes: string[];
}

export interface IFeatureFilter {
  groupUnitCode?: string;
  applicationId?: number | null;
  searchKey?: string;
}

export interface IApplication {
  id: number;
  code: string;
  name: string;
  status: DataConstant.STATUS_TYPE;
  order: number;
}
