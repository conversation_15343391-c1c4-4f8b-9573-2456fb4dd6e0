import http from "@/api";
import { ApiConstant } from "@/constant";
import { DataResponseModel } from "@/models/response.model";
import {
  IDeviceTransactionItems,
  IDeviceTransactionAction,
} from "./equipmentDocument.model";
import stringFormat from "string-format";

export const addDocumentEntryService = (body: IDeviceTransactionAction) => {
  return http.post<DataResponseModel<unknown>>(
    ApiConstant.DOCUMENT_ENTRY,
    body
  );
};

export const updateDocumentEntryService = (body: IDeviceTransactionAction) => {
  return http.put<DataResponseModel<unknown>>(
    stringFormat(ApiConstant.DOCUMENT_ENTRY_BY_ID, { id: body.id }),
    body
  );
};

export const getDocumentEntryDetailService = (id: number) => {
  return http.get<DataResponseModel<unknown>>(
    stringFormat(ApiConstant.DOCUMENT_ENTRY_BY_ID, { id })
  );
};

export const validEquipmentDocumentEntry = (
  data: IDeviceTransactionItems & {
    deviceCodes: string[];
  }
) => {
  return http.post<DataResponseModel<unknown>>(
    ApiConstant.VALID_DOCUMENT_ENTRY,
    data
  );
};
