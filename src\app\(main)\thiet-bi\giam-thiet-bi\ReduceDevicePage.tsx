"use client";

import { reduceDeviceActions } from "@/app/(main)/thiet-bi/giam-thiet-bi/reduceDevice.slice";
import { IReduceDevice } from "@/app/(main)/thiet-bi/giam-thiet-bi/type";
import { TablePageLayout } from "@/components/common";
import { REDUCE_DEVICE } from "@/constant/api.const";
import { useAppDispatch, useAppStore } from "@/redux/hook";
import { systemActions } from "@/redux/system.slice";
import { reduceDeviceSaga } from "@/app/(main)/thiet-bi/giam-thiet-bi/reduceDevice.saga";
import { createInjectableSaga } from "@/saga/injectableSaga";
import { systemSaga } from "@/saga/system.saga";
import { FormatUtils } from "@/utils";
import { ColumnDef } from "@tanstack/react-table";
import { useEffect, useState } from "react";
import dynamic from "next/dynamic";

const CreateModal = dynamic(() => import("./components/CreateModal"), {
  ssr: false,
});

const EditModal = dynamic(() => import("./components/EditModal"), {
  ssr: false,
});

const ReduceDevicePage = () => {
  const dispatch = useAppDispatch();
  const store = useAppStore();

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    createInjectableSaga("systemReducer", systemSaga).injectInto(store);
    createInjectableSaga("reduceDeviceReducer", reduceDeviceSaga).injectInto(
      store
    );

    setIsClient(true);

    return () => {
      dispatch(systemActions.systemReset());
      dispatch(reduceDeviceActions.resetReduceDevice());
    };
  }, []);

  if (!isClient) return null;

  return (
    <TablePageLayout<IReduceDevice>
      apiUrl={REDUCE_DEVICE}
      actions={["create", "delete", "update"]}
      formConfig={{
        deleteUrl: REDUCE_DEVICE,
        detailUrl: REDUCE_DEVICE,
      }}
      tableProps={{
        columns: COLUMN,
      }}
      filterConfig={[
        {
          key: "searchKey",
          type: "text",
          label: "Số phiếu",
          size: 2.4,
        },
        {
          key: "dateRange",
          type: "dateRange",
          label: "Ngày chứng từ",
          keyDateRange: ["fromDate", "toDate"],
          value: [null, null],
        },
      ]}
      CreateModalComponent={CreateModal}
      EditModalComponent={EditModal}
    />
  );
};

export default ReduceDevicePage;

const COLUMN: ColumnDef<IReduceDevice>[] = [
  {
    id: "documentNumber",
    accessorKey: "documentNumber",
    header: "Số phiếu",
  },
  {
    id: "documentDate",
    header: "Ngày lập",
    accessorFn: (row) => FormatUtils.formatDayjsWithType(row.documentDate),
    size: 100,
  },
  {
    id: "notes",
    header: "Nội dung",
    accessorKey: "notes",
  },
];
