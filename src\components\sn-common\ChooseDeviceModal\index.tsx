import { AppModal } from "@/components/common";
import { CursorClickIcon } from "@/components/icons";
import { Button } from "@mui/material";
import React, { memo, useCallback, useState } from "react";
import DeviceContent from "./DeviceContent";
import { IEduDevice } from "@/models/eduDevice.model";

const ChooseDeviceModal = ({
  onChooseDevice,
}: {
  onChooseDevice: (data: IEduDevice) => void;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  return (
    <>
      <Button
        startIcon={<CursorClickIcon />}
        onClick={() => setIsOpen(true)}
        variant="contained"
        size="small"
      >
        Chọn thiết bị
      </Button>
      <AppModal
        fullWidth
        maxWidth="lg"
        slotProps={{
          paper: {
            sx: {
              height: "100%",
            },
          },
        }}
        onClose={handleClose}
        isOpen={isOpen}
        modalTitleProps={{ title: "Chọn thiết bị" }}
        modalContentProps={{
          sx: { py: 1 },
          content: (
            <DeviceContent
              onChooseDevice={onChooseDevice}
              onClose={handleClose}
            />
          ),
        }}
      />
    </>
  );
};

export default memo(ChooseDeviceModal);
