import AppFormTextField, {
  AppFormTextFieldProps,
} from "@/components/common/form/AppFormTextField";
import RenderTreeFeatureSelect from "@/components/common/tree/RenderTreeFeatureSelect";
import { ITreeData } from "@/models/types";
import { InputLabel, Popover, Stack, TextFieldProps } from "@mui/material";
import { memo, useEffect, useState, JSX } from "react";
import {
  FieldValues,
  RegisterOptions,
  useFormContext,
  useWatch,
} from "react-hook-form";
import AppTextField from "../AppTextField";
import { findMenuConfigTree } from "@/utils/tree.utils";
import { AppConstant } from "@/constant";

const AppFormSelectTree = <T extends FieldValues>({
  control,
  name,
  data,
  idNodeTreeSelected,
  nodeChildren,
  label,
  currentDataTree,
  rules,
  onSelectTree,
  textfieldProps,
  treeValue,
  expandAll = true,
  onChangeValueForm,
  ...otherProps
}: AppFormSelectTreeProps<T>) => {
  const keyValue = useWatch({ control, name });
  const { setValue: setValueForm } = useFormContext();
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  const [labelSelect, setLabelSelect] = useState<string>("");

  const handleSelectTree = (dataTreeItems: NodeData) => {
    onSelectTree?.(dataTreeItems);
    setValueForm(name, dataTreeItems.id as any);
    onChangeValueForm?.(dataTreeItems.id as any);
  };

  useEffect(() => {
    if (keyValue) {
      const findData = findMenuConfigTree(data, keyValue);
      if (findData) {
        setLabelSelect(findData.name);
      }
    } else {
      setLabelSelect(AppConstant.ROOT_NODE_TREE);
    }
  }, [keyValue, data]);

  return (
    <>
      <Stack>
        {label && (
          <InputLabel
            required={Boolean(rules?.required)}
            htmlFor={name}
            sx={{ mb: 0.5 }}
          >
            {label}
          </InputLabel>
        )}
        <AppTextField
          value={labelSelect}
          onClick={(event: React.MouseEvent<HTMLElement>) =>
            setAnchorEl(event.currentTarget)
          }
        />
        <AppFormTextField
          control={control}
          name={name}
          rules={rules as any}
          sx={{ display: "none", position: "absolute", zIndex: -1 }}
        />
      </Stack>
      <Popover
        open={Boolean(anchorEl)}
        sx={{
          borderRadius: "4px",
        }}
        anchorEl={anchorEl}
        onClose={() => setAnchorEl(null)}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        slotProps={{
          paper: {
            sx: {
              height: "fit-content",
              maxHeight: 300,
              overflow: "auto",
            },
          },
        }}
      >
        <RenderTreeFeatureSelect
          currentDataTree={currentDataTree}
          onSelectTree={handleSelectTree}
          value={labelSelect}
          onSetValue={setLabelSelect}
          onClosePopover={() => setAnchorEl(null)}
          idNodeTreeSelected={idNodeTreeSelected}
          data={data}
          nodeChildren={nodeChildren}
          expandAll={expandAll}
        />
      </Popover>
    </>
  );
};

export type AppFormSelectTreeProps<T extends FieldValues> = Omit<
  AppFormTextFieldProps<T>,
  "onChangeValueForm" | "rules"
> & {
  data: ITreeData[];
  idNodeTreeSelected?: number;
  currentDataTree?: ITreeData | null;
  nodeChildren: string;
  onSelectTree?: (dataTreeItems: NodeData) => void;
  rules?: RegisterOptions<FieldValues>;
  textfieldProps?: TextFieldProps;
  treeValue?: string;
  expandAll?: boolean;
  onChangeValueForm?: (value: any) => void;
};

export interface NodeData {
  name: string;
  id: number;
  parentId: number | null;
  applicationId?: number | null;
}

export default memo(AppFormSelectTree) as <T extends FieldValues>(
  props: AppFormSelectTreeProps<T>
) => JSX.Element;
