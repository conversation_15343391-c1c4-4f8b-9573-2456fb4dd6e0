"use client";

import { useAppSelector } from "@/redux/hook";
import { Avatar, Skeleton, Stack, Typography } from "@mui/material";
import React, { memo, useCallback, useMemo, useState } from "react";
import dynamic from "next/dynamic";

const MenuDropdown = dynamic(() => import("./MenuDropdown"), { ssr: false });

const User = () => {
  const userInfo = useAppSelector((state) => state.appReducer.userInfo);
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  const username = useMemo(() => {
    if (!userInfo) return "";
    if (userInfo.fullName) return userInfo.fullName;
    return `${userInfo.lastName ?? ""} ${userInfo.firstName ?? ""}`.trim();
  }, [userInfo]);

  const handleAvatarClick = useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      setAnchorEl(event.currentTarget);
    },
    []
  );

  const handleCloseDropdown = useCallback(() => {
    setAnchorEl(null);
  }, []);

  return (
    <Stack direction="row" alignItems="center">
      {username ? (
        <Typography className="text-ellipsis-2-row" variant="h5" noWrap>
          {username}
        </Typography>
      ) : (
        <Skeleton variant="text" width={88} height={28} />
      )}
      <Avatar
        src={userInfo?.avatar}
        alt={username}
        sx={{
          border: "1px solid",
          borderColor: "grey.400",
          cursor: "pointer",
          width: 32,
          height: 32,
          ml: 1.5,
        }}
        onClick={handleAvatarClick}
      />
      <MenuDropdown
        onClose={handleCloseDropdown}
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
      />
    </Stack>
  );
};

export default memo(User);
