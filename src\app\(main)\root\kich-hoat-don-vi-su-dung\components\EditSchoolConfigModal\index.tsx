import { useForm } from "react-hook-form";
import { Button } from "@mui/material";
import { SaveIcon } from "@/components/icons";
import { AppModal } from "@/components/common";
import { useCallback, useEffect } from "react";
import EditContentModal from "./EditContentModal";
import { ISchoolConfig } from "../../schoolConfig.model";
import { DataConstant } from "@/constant";
import { putSchoolConfig } from "../../hooks/usePutSchoolConfig";

const SchoolConfigModalAction = ({
  open,
  onClose,
  onSuccess,
  data,
}: SchoolConfigModalActionProps) => {
  const {
    control,
    reset,
    handleSubmit,
    setValue: handleSetValueForm,
  } = useForm({ defaultValues: INIT_VALUE });

  const handleResetState = useCallback(() => {
    reset(INIT_VALUE);
    onClose();
    onSuccess?.();
  }, []);

  useEffect(() => {
    if (data) {
      handleSetValueForm("name", data.name);
      handleSetValueForm("schoolCode", data.schoolCode);
      handleSetValueForm("doetName", data.doetName);
      handleSetValueForm("divisionName", data.divisionName);
      handleSetValueForm("schoolLevelName", data.schoolLevelName);
      handleSetValueForm("domailUrl", data.website);
      handleSetValueForm("isActived", data.status);
    }
  }, [data]);

  const handleSubmitData = (valueForm: any) => {
    if (data?.id) {
      putSchoolConfig({
        id: data.id,
        body: valueForm,
        onSuccess: handleResetState,
      });
    }
  };

  return (
    <AppModal
      component="form"
      onSubmit={handleSubmit(handleSubmitData)}
      isOpen={open}
      onClose={handleResetState}
      maxWidth="xs"
      modalTitleProps={{
        title: "Cấu hình đơn vị",
      }}
      sx={{
        "& .MuiDialog-paper": {
          minWidth: 700,
          borderRadius: "4px",
          boxShadow: "0px 0px 20px 0px rgba(0, 0, 0, 0.25)",
        },
      }}
      modalActionsProps={{
        children: (
          <>
            <Button
              variant="outlined"
              onClick={handleResetState}
              color="secondary"
            >
              Đóng
            </Button>
            <Button type="submit" variant="contained" endIcon={<SaveIcon />}>
              Ghi
            </Button>
          </>
        ),
      }}
      modalContentProps={{
        content: <EditContentModal control={control} />,
      }}
    />
  );
};

export default SchoolConfigModalAction;

type SchoolConfigModalActionProps = {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  data?: ISchoolConfig;
};

const INIT_VALUE = {
  isActived: DataConstant.BOOLEAN_TYPE.false,
  isInitedData: DataConstant.BOOLEAN_TYPE.false,
  isConvertData: DataConstant.BOOLEAN_TYPE.false,
  name: "",
  schoolCode: "",
  doetName: "",
  domailUrl: "",
  divisionName: "",
  schoolLevelName: "",
};
