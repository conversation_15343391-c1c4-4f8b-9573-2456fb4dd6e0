import {
  IDeviceInput,
  IReduceDevicePayload,
} from "@/app/(main)/thiet-bi/giam-thiet-bi/type";
import { IDevice } from "@/models/eduDevice.model";

export const convertToPayload = (
  data: IDevice[],
  deviceInput: IDeviceInput[]
): IReduceDevicePayload[] => {
  return data.map((item) => {
    const deviceInputItem = deviceInput.find((input) => input.id === item.id);
    return {
      id: item.id,
      code: item.code,
      deviceDefinitionId: item.deviceDefinitionId,
      roomId: item.roomId,
      teacherId: item.teacherId,
      quantity: item.quantity,
      price: item.price,
      countryId: item.countryId,
      entryDate: item.entryDate,
      serial: item.serial,
      expireDate: item.expireDate,
      totalBroken: deviceInputItem?.totalBroken || 0,
      totalLost: deviceInputItem?.totalLost || 0,
      totalAvailable: deviceInputItem?.totalAvailable || 0,
      deviceTransactionItemId: item.deviceTransactionItemId,
    };
  });
};
