export interface IAdminAccount {
  id: number;
  doetCode: string;
  divisionCode: string;
  schoolCode: string;
  groupUnitCode: string;
  libraryCard: string;
  userName: string;
  fullName: string;
  lastName: string;
  firstName: string;
  gender: number;
  avatar: string;
  dateOfBirth: string;
  status: number;
  accountCode: string;
  email: string;
  phone: string;
  createdAt: string;
  createdBy: number;
  updatedAt: string;
  updatedBy: number;
  oldId: number;
  schoolName: string;
  applicationFunctionName: string;
  applicationFunctionCode: string;
  password: string;
}

export interface IAdminAccountParams {
  searchKey: string;
  groupUnitCode: string;
  doetCode: string;
  schoolCode: string;
  divisionCode: string;
  provinceCode: string;
  districtCode: string;
  wardCode: string;
}
