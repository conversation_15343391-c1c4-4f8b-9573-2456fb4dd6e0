"use client";
import {
  Control,
  Controller,
  ControllerProps,
  FieldPath,
  FieldValues,
  RegisterOptions,
} from "react-hook-form";
import {
  FormControlLabel,
  InputLabel,
  InputLabelProps,
  Radio,
  RadioGroup,
  RadioGroupProps,
  RadioProps,
  Stack,
  StackProps,
} from "@mui/material";
import { RefAttributes, memo } from "react";
import { IOption } from "../AppAutoComplete";

const AppFormRadio = <T extends FieldValues>({
  control,
  name,
  radioList = [],
  controlProps,
  onChangeValueForm,
  radioProps,
  defaultValue,
  label,
  radioGroupProps,
  formLabelProps,
  isDisableValue,
  rules,
  direction = "column",
  ...otherProps
}: AppFormRadioProps<T>) => {
  return (
    <Stack
      spacing={0.5}
      alignItems={"center"}
      direction={direction}
      {...otherProps}
    >
      {label && (
        <InputLabel
          required={Boolean(rules?.required)}
          htmlFor={name}
          {...formLabelProps}
          sx={{
            width: direction === "row" ? "unset" : "100%",
            ...formLabelProps?.sx,
          }}
        >
          {label}
        </InputLabel>
      )}
      <Controller
        rules={rules}
        name={name}
        control={control}
        render={({ field: { onChange, ...otherFieldProps } }) => (
          <RadioGroup
            row
            onChange={(_, value) => {
              if (onChangeValueForm instanceof Function)
                onChangeValueForm(value);
              onChange(value);
            }}
            sx={{
              gap: 1.5,
              width: "100%",
            }}
            {...otherFieldProps}
            {...radioGroupProps}
          >
            {radioList.map((item) => {
              const isDisabled = isDisableValue?.some(
                (val) => val == item.value
              );
              return (
                <FormControlLabel
                  key={item.id}
                  value={item.id}
                  label={item.label}
                  disabled={isDisabled}
                  control={
                    <Radio
                      checked={otherFieldProps.value == item.id}
                      sx={{
                        width: 40,
                        height: 40,
                        ...radioProps?.sx,
                      }}
                      {...radioProps}
                    />
                  }
                />
              );
            })}
          </RadioGroup>
        )}
        {...controlProps}
      />
    </Stack>
  );
};

type AppFormRadioProps<T extends FieldValues> = StackProps & {
  control: Control<any, object>;
  name: FieldPath<T>;
  radioList: Array<IOption>;

  radioProps?: RadioProps & RefAttributes<HTMLInputElement>;
  controlProps?: Omit<ControllerProps, "render" | "name" | "control">;
  label?: string;
  formLabelProps?: InputLabelProps;
  radioGroupProps?: RadioGroupProps;
  onChangeValueForm?: (value?: string | number) => void;
  isDisableValue?: Array<number | string>;
  rules?: RegisterOptions<FieldValues>;
};

export default memo(AppFormRadio);
