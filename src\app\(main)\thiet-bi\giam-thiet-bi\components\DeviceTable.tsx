import ChooseDeviceModal from "@/app/(main)/thiet-bi/giam-thiet-bi/components/ChooseDeviceModal";
import {
  reduceDeviceActions,
  reduceDeviceSelectors,
  selectorDeviceInputById,
  selectorDeviceItem,
} from "@/app/(main)/thiet-bi/giam-thiet-bi/reduceDevice.slice";
import { AppTable, AppTextField } from "@/components/common";
import AppFormLayoutPanel from "@/components/common/AppFormLayoutPanel";
import DeleteCell from "@/components/common/table/cell/DeleteCell";
import { AppConstant } from "@/constant";
import { IDevice } from "@/models/eduDevice.model";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { CommonUtils, FormatUtils } from "@/utils";
import { Typography } from "@mui/material";
import { ColumnDef } from "@tanstack/react-table";
import { memo, useMemo, useState } from "react";
import { toast } from "sonner";

const DeviceTable = () => {
  const dispatch = useAppDispatch();
  const devices = useAppSelector(reduceDeviceSelectors.devices);

  const columns = getColumns((id) => {
    dispatch(reduceDeviceActions.deleteDevice(id));
  });

  return (
    <AppFormLayoutPanel
      title="Danh sách thiết bị"
      isDoc
      actions={<ChooseDeviceModal />}
      childrenProps={{
        sx: {
          p: 0,
        },
      }}
    >
      <AppTable
        tableContainerProps={{
          sx: {
            height: 400,
          },
        }}
        columns={columns}
        data={devices}
        totalData={devices.length}
        hasDefaultPagination
      />
    </AppFormLayoutPanel>
  );
};

export default memo(DeviceTable);

const getColumns = (onDelete): ColumnDef<IDevice>[] => [
  {
    id: "index",
    header: "STT",
    cell: ({ row }) => {
      return <Typography>{row.index + 1}</Typography>;
    },
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "delete",
    header: "Xóa",
    cell: ({ row }) => <DeleteCell onClick={() => onDelete(row.original.id)} />,
    size: 50,
    meta: {
      align: "center",
    },
  },
  {
    id: "deviceCode",
    header: "Mã thiết bị",
    accessorKey: "deviceCode",
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
  },
  {
    id: "roomName",
    header: "Kho/phòng",
    accessorKey: "roomName",
  },
  {
    id: "deviceUnitName",
    header: "Đơn vị tính",
    accessorKey: "deviceUnitName",
  },
  {
    id: "deviceInput",
    header: "SL giảm theo",
    meta: {
      align: "center",
    },
    columns: [
      {
        id: "totalBroken",
        header: "Hỏng",
        accessorKey: "totalBroken",
        cell: ({ row }) => (
          <DeviceInputField
            rowId={row.original.id}
            field="totalBroken"
            currentValue={row.original.transactionTotalBroken}
            isEdit={row.original.deviceTransactionItemId !== 0}
            maxFieldLabel="Số lượng hỏng"
          />
        ),
      },
      {
        id: "totalLost",
        header: "Mất",
        accessorKey: "totalLost",
        cell: ({ row }) => (
          <DeviceInputField
            rowId={row.original.id}
            field="totalLost"
            currentValue={row.original.transactionTotalLost}
            isEdit={row.original.deviceTransactionItemId !== 0}
            maxFieldLabel="Số lượng mất"
          />
        ),
      },
      {
        id: "totalAvailable",
        header: "Còn SD",
        accessorKey: "totalAvailable",
        cell: ({ row }) => (
          <DeviceInputField
            rowId={row.original.id}
            field="totalAvailable"
            currentValue={row.original.transactionTotalAvailable}
            isEdit={row.original.deviceTransactionItemId !== 0}
            maxFieldLabel="Số lượng còn SD"
          />
        ),
      },
    ],
  },
  {
    id: "total",
    header: "Tổng SL giảm",
    cell: ({ row }) => <TotalInput rowId={row.original.id} />,
    meta: {
      align: "right",
    },
    size: 120,
  },
];

type DeviceInputFieldProps = {
  rowId: number;
  field: "totalLost" | "totalBroken" | "totalAvailable";
  currentValue: number;
  isEdit: boolean;
  label?: string;
  maxFieldLabel: string;
};

const DeviceInputField = memo(
  ({
    rowId,
    field,
    currentValue,
    isEdit,
    maxFieldLabel,
  }: DeviceInputFieldProps) => {
    const dispatch = useAppDispatch();
    const deviceFind = useAppSelector(selectorDeviceItem(rowId));
    const [value, setValue] = useState(isEdit ? currentValue : "0");
    const [isError, setIsError] = useState(false);

    const changeValueDebounce = useMemo(
      () =>
        CommonUtils.debounce((newValue: number) => {
          dispatch(
            reduceDeviceActions.changeDeviceInput({
              id: rowId,
              [field]: newValue,
            })
          );
        }, AppConstant.DEBOUNCE_TIME_IN_MILLISECOND),
      [rowId, field]
    );

    const handleChange = (val: string) => {
      setValue(val);
      changeValueDebounce(Number(val));
    };

    const handleBlur = () => {
      if (value === "") {
        setValue("0");
        setIsError(false);
        return;
      }

      const inputVal = Number(value);
      const maxVal = deviceFind?.[field] ?? 0;

      if (inputVal > maxVal) {
        setIsError(true);
        toast.warning(
          `Không thể ghi giảm cho thiết bị ${deviceFind?.code} với số lượng lớn hơn ${maxFieldLabel}, vui lòng kiểm tra lại. (${maxFieldLabel}: ${maxVal})`
        );
      } else {
        setIsError(false);
      }
    };

    return (
      <AppTextField
        type="number"
        slotProps={{ htmlInput: { min: 0 } }}
        onBlur={handleBlur}
        value={value}
        error={isError}
        onChange={(e) => handleChange(e.currentTarget.value)}
      />
    );
  }
);

const TotalInput = memo(({ rowId }: { rowId: number }) => {
  const deviceFind = useAppSelector(selectorDeviceInputById(rowId));

  return (
    <Typography>
      {FormatUtils.formatNumber(
        (deviceFind?.totalAvailable || 0) +
          (deviceFind?.totalBroken || 0) +
          (deviceFind?.totalLost || 0),
        0,
        0
      )}
    </Typography>
  );
});
