"use client";

import { AppModal, AppFormAutocomplete, IOption } from "@/components/common";
import { AppModalProps } from "@/components/common/modal/AppModal";
import { AppConstant } from "@/constant";
import { SEMESTER_TYPE, SEMESTER_TYPE_LIST } from "@/constant/data.const";
import { appActions, selectSchoolYearOptions } from "@/redux/app.slice";
import { useAppDispatch } from "@/redux/hook";
import { RootState } from "@/redux/store";
import { Button, Stack } from "@mui/material";
import { memo, useCallback, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useSelector } from "react-redux";
interface ChangeSchoolYearModalProps
  extends Omit<
    AppModalProps,
    "modalTitleProps" | "modalContentProps" | "modalActionsProps" | "onSubmit"
  > {
  isOpen: boolean;
  onClose: () => void;
}

interface ChangeSchoolYearFormData {
  schoolYear: IOption | null;
  semester: IOption | null;
}

const ChangeSchoolYearModal = ({
  isOpen,
  onClose,
  ...otherProps
}: ChangeSchoolYearModalProps) => {
  const dispatch = useAppDispatch();
  const { schoolYearSelected, semesterSelected } = useSelector(
    (state: RootState) => state.appReducer
  );
  const schoolYearOptions = useSelector(selectSchoolYearOptions);

  const { control, reset, handleSubmit } = useForm<ChangeSchoolYearFormData>({
    defaultValues: {
      schoolYear: schoolYearSelected ?? null,
      semester: semesterSelected ?? null,
    },
  });

  const handleCloseModal = useCallback(() => {
    reset({
      schoolYear: schoolYearSelected ?? null,
      semester: semesterSelected ?? null,
    });
    onClose();
  }, [onClose, semesterSelected, schoolYearSelected]);

  const handleSubmitData = useCallback(
    (values: ChangeSchoolYearFormData) => {
      const { schoolYear, semester } = values;

      dispatch(
        appActions.changeSchoolYear({
          schoolYear: schoolYear as IOption,
          semester: semester as IOption,
          onSuccess: () => {
            handleCloseModal();
          },
        })
      );
    },
    [handleCloseModal]
  );

  return (
    <AppModal
      component="form"
      onSubmit={handleSubmit(handleSubmitData)}
      isOpen={isOpen}
      onClose={handleCloseModal}
      modalTitleProps={{
        title: "Thay đổi năm học",
      }}
      modalContentProps={{
        content: (
          <Stack spacing={2}>
            <AppFormAutocomplete
              control={control}
              name="schoolYear"
              label="Chọn năm làm việc"
              options={schoolYearOptions}
              rules={{ required: "Vui lòng chọn năm học" }}
            />
            <AppFormAutocomplete
              control={control}
              name="semester"
              label="Chọn học kỳ"
              options={SEMESTER_TYPE_LIST}
              rules={{ required: "Vui lòng chọn học kỳ" }}
            />
          </Stack>
        ),
      }}
      modalActionsProps={{
        children: (
          <>
            <Button
              variant="outlined"
              onClick={handleCloseModal}
              color="secondary"
            >
              Đóng
            </Button>
            <Button type="submit" variant="contained">
              Ghi
            </Button>
          </>
        ),
      }}
      {...otherProps}
    />
  );
};

export default memo(ChangeSchoolYearModal);
