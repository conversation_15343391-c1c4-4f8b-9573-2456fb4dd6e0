import React, { createContext, useContext, ReactNode } from "react";

export type ModalConfigContextProps = {
  detailUrl?: string;
  formatDetailData?: (data: any) => any;
};

const ModalConfigContext = createContext<ModalConfigContextProps | undefined>(
  undefined
);

export const ModalConfigProvider = ({
  children,
  detailUrl,
  formatDetailData,
}: ModalConfigContextProps & { children: ReactNode }) => {
  return (
    <ModalConfigContext.Provider value={{ detailUrl, formatDetailData }}>
      {children}
    </ModalConfigContext.Provider>
  );
};

export const useModalConfig = () => {
  const context = useContext(ModalConfigContext);
  if (!context) {
    throw new Error("useModalConfig must be used within a ModalConfigProvider");
  }
  return context;
};
