"use client";

import TreeContent from "@/app/(main)/root/quan-ly-tinh-nang/components/TreeContent";
import useFeature from "@/app/(main)/root/quan-ly-tinh-nang/hooks/useFeature";
import useMenuOptions from "@/app/(main)/root/quan-ly-tinh-nang/hooks/useMenuOptions";
import {
  featureActions,
  featureSelectors,
} from "@/app/(main)/root/quan-ly-tinh-nang/store/feature.slice";
import {
  IFeature,
  IFeatureFilter,
} from "@/app/(main)/root/quan-ly-tinh-nang/type";
import { TablePageLayout } from "@/components/common";
import AppFormSelectTree from "@/components/common/form/AppFormSelectTree";
import {
  ExpandedCell,
  ExpandedHeaderCell,
} from "@/components/common/table/AppTable";
import {
  FilterConfig,
  FormFieldConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import { TableIcon, TreeIcon } from "@/components/icons";
import { AppConstant, DataConstant } from "@/constant";
import { APPLICATION, FEATURE } from "@/constant/api.const";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { convertDataTreeItem } from "@/utils/tree.utils";
import {
  Box,
  Stack,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
} from "@mui/material";
import { ColumnDef } from "@tanstack/react-table";
import { useEffect, useMemo, useRef, useState } from "react";

const FeatureManagementPage = () => {
  const dispatch = useAppDispatch();
  const tableRef = useRef<ITableRef>(null);
  const [view, setView] = useState<"tree" | "table">("tree");
  const features = useAppSelector(featureSelectors.features);
  const filter = useAppSelector(featureSelectors.filter);
  const { menuOptions, setMenuTypeId } = useMenuOptions();
  const { fetchFeatures } = useFeature();

  const handleChangeFilterWithKey = (key: keyof IFeatureFilter, value: any) => {
    dispatch(featureActions.changeFilterWithKey({ key, value }));
  };

  const formConfig: FormFieldConfig<IFeature>[] = useMemo(
    () => [
      {
        key: "parentId",
        type: "custom",
        label: "Thư mục cha",
        size: 12,
        defaultValue: AppConstant.DEFAULT_TREE,
        component: (control) => {
          return (
            <AppFormSelectTree
              data={convertDataTreeItem(features)}
              control={control}
              name="parentId"
              label="Thư mục cha"
              nodeChildren="applicationFeatures"
              expandAll={false}
            />
          );
        },
      },
      {
        key: "applicationId",
        type: "select",
        label: "Module",
        size: 6,
        apiListUrl: APPLICATION,
        rules: {
          required: "Module không được để trống",
        },
      },
      {
        key: "code",
        type: "text",
        label: "Mã tính năng",
        size: 6,
        rules: {
          required: "Mã tính năng không được để trống",
        },
      },
      {
        key: "name",
        type: "text",
        label: "Tên tính năng",
        size: 6,
        rules: {
          required: "Tên tính năng không được để trống",
        },
      },
      {
        key: "order",
        type: "number",
        label: "Thứ tự",
        size: 6,
        defaultValue: 1,
        textFieldNumberProps: {
          min: 1,
        },
        rules: {
          required: "Thứ tự không được để trống",
        },
      },
      {
        key: "groupUnitCodes",
        type: "select",
        label: "Đối tượng sử dụng",
        size: 12,
        selectConfig: {
          options: DataConstant.DON_VI_LIST,
          isMulti: true,
        },
      },
      {
        key: "menuConfigId",
        type: "select",
        label: "Môn hệ thống",
        size: 12,
        selectConfig: {
          options: menuOptions.map((item) => ({
            id: item.id,
            label: item.name,
          })),
        },
      },
      {
        key: "status",
        type: "toggle",
        label: "Trạng thái",
        size: 6,
        defaultValue: DataConstant.BOOLEAN_TYPE.true,
      },
      {
        key: "isSystem",
        type: "toggle",
        label: "Là tính năng hệ thống",
        size: 6,
        defaultValue: DataConstant.BOOLEAN_TYPE.true,
      },
    ],
    [features, menuOptions]
  );

  const filterConfig: FilterConfig[] = useMemo(
    () => [
      {
        key: "applicationId",
        type: "select",
        label: "Module",
        size: 2.4,
        onChangeValue: (value) => {
          handleChangeFilterWithKey("applicationId", value);
          setMenuTypeId(value as number);
        },
        apiListUrl: APPLICATION,
        fieldProps: {
          disableClearable: true,
        },
      },
      {
        key: "groupUnitCode",
        type: "select",
        label: "Đơn vị",
        size: 2.4,
        options: DataConstant.DON_VI_LIST,
        value: DataConstant.DON_VI_TYPE.truong,
        onChangeValue: (value) => {
          handleChangeFilterWithKey("groupUnitCode", value);
        },
        fieldProps: {
          disableClearable: true,
        },
      },
    ],
    []
  );

  useEffect(() => {
    fetchFeatures();
  }, [filter]);

  useEffect(() => {
    const interval = setInterval(() => {
      const loading =
        tableRef.current?.isLoadingFilter && tableRef.current?.isLoading;
      if (!loading) {
        const application = tableRef.current?.filterOptions?.applicationId;
        const applicationId = application?.[0]?.id;
        if (applicationId) {
          setMenuTypeId(applicationId);
          handleChangeFilterWithKey("applicationId", applicationId);
          tableRef?.current?.handleChangeFilter?.("applicationId")?.(
            applicationId
          );
          clearInterval(interval);
        }
      }
    }, 500);
    return () => clearInterval(interval);
  }, []);

  return (
    <TablePageLayout<IFeature>
      fetchAll
      visibleCol={VISIBLE_COL}
      ref={tableRef}
      apiUrl={FEATURE}
      tableProps={{
        columns,
        paginationData: undefined,
        options: {
          getSubRows: (row) => (row as any).children,
        },
      }}
      customActions={
        <Stack direction="row" spacing={1}>
          <ToggleButtonGroup
            value={view}
            exclusive
            color="primary"
            onChange={(_, value) => setView(value)}
          >
            <Tooltip title="Xem dạng cây" arrow>
              <ToggleButton
                value="tree"
                aria-label="tree"
                sx={{
                  width: 36,
                  height: 36,
                  minWidth: "unset",
                  fontSize: 20,
                  "&.Mui-selected": {
                    backgroundColor: "primary.main",
                    color: "white",
                    "&:hover": {
                      backgroundColor: "primary.main",
                      color: "white",
                    },
                  },
                }}
              >
                <TreeIcon />
              </ToggleButton>
            </Tooltip>
            <Tooltip title="Xem dạng bảng" arrow>
              <ToggleButton
                value="table"
                aria-label="table"
                sx={{
                  width: 36,
                  height: 36,
                  minWidth: "unset",
                  fontSize: 20,
                  "&.Mui-selected": {
                    backgroundColor: "primary.main",
                    color: "white",
                    "&:hover": {
                      backgroundColor: "primary.main",
                      color: "white",
                    },
                  },
                }}
              >
                <TableIcon />
              </ToggleButton>
            </Tooltip>
          </ToggleButtonGroup>
        </Stack>
      }
      formatData={convertToTreeWithChildren}
      filterConfig={filterConfig}
      actions={["create", "update", "delete"]}
      formConfig={{
        deleteUrl: FEATURE,
        detailUrl: FEATURE,
        createUrl: FEATURE,
        updateUrl: FEATURE,
        createFields: formConfig,
        updateFields: formConfig,
        onSuccess: () => {
          fetchFeatures();
        },
      }}
      pageContent={
        view === "tree" ? (
          <Box width={"40%"} px={3}>
            <TreeContent />
          </Box>
        ) : undefined
      }
    />
  );
};

export default FeatureManagementPage;

const VISIBLE_COL = [
  {
    id: "code",
    name: "Mã",
  },
  {
    id: "name",
    name: "Tên tính năng",
  },
  {
    id: "parentName",
    name: "Thư mục cha",
  },
];

const columns: ColumnDef<IFeature>[] = [
  {
    id: "expanded",
    size: 0,
    minSize: 0,
    header: ({ table }) => <ExpandedHeaderCell table={table} />,
    meta: {
      align: "center",
      cellSx: {
        width: "1px !important",
      },
    },
    cell: ({ row }) => <ExpandedCell row={row} />,
  },
  {
    id: "code",
    header: "Mã",
    accessorKey: "code",
    size: 80,
  },
  {
    id: "name",
    header: "Tên môn học",
    accessorKey: "name",
    size: 200,
  },
  {
    id: "parentName",
    accessorKey: "parentName",
    header: "Thư mục cha",
    size: 80,
    meta: {
      align: "center",
    },
  },
];

function convertToTreeWithChildren(data: IFeature[]): any[] {
  return data.map((item) => ({
    ...item,
    children: convertToTreeWithChildren(item.applicationFeatures || []),
    applicationFeatures: undefined, // Loại bỏ field cũ nếu muốn
  }));
}
